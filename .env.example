# ===========================================
# 🔥 FIREBASE CONFIGURATION (LEGACY)
# ===========================================
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# ===========================================
# 🚀 SUPABASE CONFIGURATION (NEW)
# ===========================================
VITE_SUPABASE_URL=https://pcfrgvhigvklersufktf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjZnJndmhpZ3ZrbGVyc3Vma3RmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3NjA5MzcsImV4cCI6MjA2NDMzNjkzN30.sz7YpgMNQ8AT5PzTBy_MBtPNdE135R7hy2LU7YZO56g

# ===========================================
# 🔄 MIGRATION CONTROL
# ===========================================
# Set to 'true' to use Supabase (NEW), 'false' to use Firebase (LEGACY)
VITE_USE_SUPABASE_AUTH=true

# Enable complete Supabase migration (all features)
VITE_USE_SUPABASE_DATA=true

# ===========================================
# 🤖 AI & EXTERNAL SERVICES
# ===========================================
VITE_GEMINI_API_KEY=your_gemini_api_key
VITE_BREVO_API_KEY=your_brevo_api_key

# ===========================================
# 🛠️ DEVELOPMENT SETTINGS
# ===========================================
# Enable debug logging for migration
VITE_DEBUG_MIGRATION=false

# Enable performance monitoring
VITE_ENABLE_ANALYTICS=true
