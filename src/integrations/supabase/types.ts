export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      aiChats: {
        Row: {
          id: string
          userId: string
          createdBy: string | null
          title: string | null
          slug: string | null
          messages: Json | null
          createdAt: Json | null
          updatedAt: Json | null
          isPublic: boolean | null
          viewCount: number | null
          preview: string | null
          comments: Json | null
          isPinned: boolean | null
          isStarred: boolean | null
          status: string | null
          tags: string[] | null
        }
        Insert: {
          id: string
          userId: string
          createdBy?: string | null
          title?: string | null
          slug?: string | null
          messages?: Json | null
          createdAt?: Json | null
          updatedAt?: Json | null
          isPublic?: boolean | null
          viewCount?: number | null
          preview?: string | null
          comments?: Json | null
          isPinned?: boolean | null
          isStarred?: boolean | null
          status?: string | null
          tags?: string[] | null
        }
        Update: {
          id?: string
          userId?: string
          createdBy?: string | null
          title?: string | null
          slug?: string | null
          messages?: Json | null
          createdAt?: Json | null
          updatedAt?: Json | null
          isPublic?: boolean | null
          viewCount?: number | null
          preview?: string | null
          comments?: Json | null
          isPinned?: boolean | null
          isStarred?: boolean | null
          status?: string | null
          tags?: string[] | null
        }
        Relationships: []
      }
      chats: {
        Row: {
          id: string
          name: string
          description: string | null
          members: string[] | null
          createdBy: string | null
          createdAt: string | null
          updatedAt: string | null
          isPublic: boolean | null
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          members?: string[] | null
          createdBy?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          isPublic?: boolean | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          members?: string[] | null
          createdBy?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          isPublic?: boolean | null
        }
        Relationships: []
      }
      exams: {
        Row: {
          id: string
          userId: string
          name: string
          date: string
          totalMarks: number
          totalMarksObtained: number
          subjectMarks: Json | null
          notes: string | null
          createdAt: string | null
        }
        Insert: {
          id: string
          userId: string
          name: string
          date: string
          totalMarks: number
          totalMarksObtained: number
          subjectMarks?: Json | null
          notes?: string | null
          createdAt?: string | null
        }
        Update: {
          id?: string
          userId?: string
          name?: string
          date?: string
          totalMarks?: number
          totalMarksObtained?: number
          subjectMarks?: Json | null
          notes?: string | null
          createdAt?: string | null
        }
        Relationships: []
      }
      groups: {
        Row: {
          id: string
          name: string
          description: string | null
          members: string[] | null
          createdBy: string
          createdAt: string | null
          isPublic: boolean | null
          inviteCode: string | null
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          members?: string[] | null
          createdBy: string
          createdAt?: string | null
          isPublic?: boolean | null
          inviteCode?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          members?: string[] | null
          createdBy?: string
          createdAt?: string | null
          isPublic?: boolean | null
          inviteCode?: string | null
        }
        Relationships: []
      }
      messages_queue: {
        Row: {
          created_at: string | null
          id: string
          image_data: string | null
          message: string
          processed_at: string | null
          response: string | null
          retry_count: number | null
          status: Database["public"]["Enums"]["message_status"]
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          image_data?: string | null
          message: string
          processed_at?: string | null
          response?: string | null
          retry_count?: number | null
          status?: Database["public"]["Enums"]["message_status"]
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          image_data?: string | null
          message?: string
          processed_at?: string | null
          response?: string | null
          retry_count?: number | null
          status?: Database["public"]["Enums"]["message_status"]
          user_id?: string
        }
        Relationships: []
      }
      todos: {
        Row: {
          id: string
          title: string
          description: string | null
          priority: string | null
          createdAt: number
          updatedAt: number
          dueDate: number | null
          assignedTo: string | null
          assignedToName: string | null
          assignedToPhotoUrl: string | null
          createdBy: string
          groupId: string | null
          columnId: string | null
        }
        Insert: {
          id: string
          title: string
          description?: string | null
          priority?: string | null
          createdAt: number
          updatedAt: number
          dueDate?: number | null
          assignedTo?: string | null
          assignedToName?: string | null
          assignedToPhotoUrl?: string | null
          createdBy: string
          groupId?: string | null
          columnId?: string | null
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          priority?: string | null
          createdAt?: number
          updatedAt?: number
          dueDate?: number | null
          assignedTo?: string | null
          assignedToName?: string | null
          assignedToPhotoUrl?: string | null
          createdBy?: string
          groupId?: string | null
          columnId?: string | null
        }
        Relationships: []
      }
      userSubjects: {
        Row: {
          id: string
          userId: string
          name: string
          color: string | null
          createdAt: string | null
        }
        Insert: {
          id: string
          userId: string
          name: string
          color?: string | null
          createdAt?: string | null
        }
        Update: {
          id?: string
          userId?: string
          name?: string
          color?: string | null
          createdAt?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          id: string
          email: string
          username: string | null
          created_at: string | null
          updated_at: string | null
          stats: Json | null
          progress: Json | null
          uid: string | null
          displayName: string | null
          photoURL: string | null
          lastLogin: string | null
          welcomeEmailSent: boolean | null
          backgroundImage: string | null
          bio: string | null
          location: string | null
          studySessions: Json | null
          mockTests: Json | null
        }
        Insert: {
          id: string
          email: string
          username?: string | null
          created_at?: string | null
          updated_at?: string | null
          stats?: Json | null
          progress?: Json | null
          uid?: string | null
          displayName?: string | null
          photoURL?: string | null
          lastLogin?: string | null
          welcomeEmailSent?: boolean | null
          backgroundImage?: string | null
          bio?: string | null
          location?: string | null
          studySessions?: Json | null
          mockTests?: Json | null
        }
        Update: {
          id?: string
          email?: string
          username?: string | null
          created_at?: string | null
          updated_at?: string | null
          stats?: Json | null
          progress?: Json | null
          uid?: string | null
          displayName?: string | null
          photoURL?: string | null
          lastLogin?: string | null
          welcomeEmailSent?: boolean | null
          backgroundImage?: string | null
          bio?: string | null
          location?: string | null
          studySessions?: Json | null
          mockTests?: Json | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      message_status: "pending" | "processing" | "completed" | "failed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
