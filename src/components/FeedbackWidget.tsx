
import { useAuth } from '@/contexts/AuthContext';

interface FeedbackWidgetProps {
  className?: string;
}

export function FeedbackWidget({ className }: FeedbackWidgetProps) {
  const { user } = useAuth();

  return (
    <a
      href="https://isotopeai.featurebase.app"
      target="_blank"
      rel="noopener noreferrer"
      data-featurebase-link
      className={className}
    >
      Feedback
    </a>
  );
} 
