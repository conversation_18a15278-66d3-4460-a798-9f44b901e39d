import React, { useState, useEffect } from 'react';
import { Comment } from '../types/chat';
import { CommentComponent } from './Comment';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { ChevronDown, ChevronUp, MessageCircle, X, MessageSquare, MessageSquarePlus } from 'lucide-react';
import { subscribeToComments, addComment, addReply, deleteComment, deleteReply, isDeveloperUser } from '../utils/firebase';
import { useToast } from './ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CommentsSectionProps {
  chatId: string;
}

const COMMENTS_PER_GROUP = 5;

// Animation utilities for header elements
const animateIn = "animate-in fade-in slide-in-from-bottom-2 duration-500 fill-mode-both";
const staggerDelay = (index: number) => `delay-[${100 + (index * 75)}ms]`;

export const DiscussionSection: React.FC<CommentsSectionProps> = ({
  chatId,
}) => {
  const { user } = useAuth();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [maxVisibleGroup, setMaxVisibleGroup] = useState(0);
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [expandedReplyIds, setExpandedReplyIds] = useState<Set<string>>(new Set());
  const [username, setUsername] = useState<string>("");
  const [isDeveloper, setIsDeveloper] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      try {
        const userRef = doc(db, 'users', user.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const userData = userSnap.data();
          setUsername(userData.username || "");
          // Check if user is Developer
          setIsDeveloper(userData.username === "Developer");
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, [user]);

  useEffect(() => {
    if (!chatId) {
      toast({
        title: "Error",
        description: "No chat selected. Please start a new chat.",
        variant: "destructive",
      });
      return;
    }

    const unsubscribe = subscribeToComments(chatId, (updatedComments) => {
      setComments(updatedComments);
    });

    return () => {
      try {
        unsubscribe();
      } catch (error) {
        console.error("Error unsubscribing from comments:", error);
      }
    };
  }, [chatId, toast]);

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast({
        title: "Empty Comment",
        description: "Please enter a comment before submitting.",
        variant: "destructive",
      });
      return;
    }

    if (!username) {
      toast({
        title: "Error",
        description: "Please wait while we load your profile.",
        variant: "destructive",
      });
      return;
    }

    try {
      await addComment(chatId, newComment.trim(), username);
      setNewComment('');
      setShowCommentInput(false);
    } catch (error) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddReply = async (parentId: string, content: string) => {
    if (!username) {
      toast({
        title: "Error",
        description: "Please wait while we load your profile.",
        variant: "destructive",
      });
      return;
    }

    try {
      if (!user) {
        toast({
          title: "Authentication Error",
          description: "You must be signed in to reply to comments.",
          variant: "destructive",
        });
        return;
      }

      await addReply(chatId, parentId, content, username);
    } catch (error: any) {
      console.error('Error adding reply:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExpandReply = (commentId: string) => {
    setExpandedReplyIds(prev => new Set([...prev, commentId]));
  };

  const handleCollapseReply = (commentId: string) => {
    setExpandedReplyIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(commentId);
      return newSet;
    });
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment(chatId, commentId);
      toast({
        title: "Success",
        description: "Comment deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteReply = async (commentId: string, replyId: string, parentReplyId?: string) => {
    try {
      if (!isDeveloper && user) {
        // Double-check server-side if user is Developer
        const isDevUser = await isDeveloperUser(user.uid);
        if (!isDevUser) {
          toast({
            title: "Permission Denied",
            description: "Only users with username 'Developer' can delete replies.",
            variant: "destructive",
          });
          return;
        }
      }

      await deleteReply(chatId, commentId, replyId, parentReplyId);
      toast({
        title: "Success",
        description: "Reply deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting reply:', error);
      toast({
        title: "Error",
        description: "Failed to delete reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditReply = async (commentId: string, replyId: string, content: string, parentReplyId?: string) => {
    try {
      if (!isDeveloper && user) {
        // Double-check server-side if user is Developer
        const isDevUser = await isDeveloperUser(user.uid);
        if (!isDevUser) {
          toast({
            title: "Permission Denied",
            description: "Only users with username 'Developer' can edit replies.",
            variant: "destructive",
          });
          return;
        }
      }

      // Get the comment document from Firestore
      const commentRef = doc(db, `chats/${chatId}/comments`, commentId);
      const commentSnap = await getDoc(commentRef);

      if (!commentSnap.exists()) {
        throw new Error("Comment not found");
      }

      const commentData = commentSnap.data();
      let replies = commentData.replies || [];

      // Helper function to find and update a reply's content
      const updateReplyInTree = (replyList: any[], targetId: string, newContent: string, parentId?: string): boolean => {
        // If we have a parent ID, we need to find that parent first
        if (parentId) {
          for (let i = 0; i < replyList.length; i++) {
            if (replyList[i].id === parentId) {
              if (replyList[i].replies) {
                const replyIndex = replyList[i].replies.findIndex((r: any) => r.id === targetId);
                if (replyIndex !== -1) {
                  replyList[i].replies[replyIndex].content = newContent;
                  return true;
                }
              }
            }
            if (replyList[i].replies && replyList[i].replies.length > 0) {
              if (updateReplyInTree(replyList[i].replies, targetId, newContent, parentId)) {
                return true;
              }
            }
          }
        } else {
          // Direct reply to the comment
          const replyIndex = replyList.findIndex((reply: any) => reply.id === targetId);
          if (replyIndex !== -1) {
            replyList[replyIndex].content = newContent;
            return true;
          }
          // Search in nested replies
          for (let i = 0; i < replyList.length; i++) {
            if (replyList[i].replies && replyList[i].replies.length > 0) {
              if (updateReplyInTree(replyList[i].replies, targetId, newContent)) {
                return true;
              }
            }
          }
        }
        return false;
      };

      const success = updateReplyInTree(replies, replyId, content, parentReplyId);

      if (success) {
        await updateDoc(commentRef, { replies });
        toast({
          title: "Success",
          description: "Reply updated successfully.",
        });
      } else {
        throw new Error("Reply not found");
      }
    } catch (error) {
      console.error('Error updating reply:', error);
      toast({
        title: "Error",
        description: "Failed to update reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditComment = async (commentId: string, content: string) => {
    try {
      if (!isDeveloper && user) {
        // Double-check server-side if user is Developer
        const isDevUser = await isDeveloperUser(user.uid);
        if (!isDevUser) {
          toast({
            title: "Permission Denied",
            description: "Only users with username 'Developer' can edit comments.",
            variant: "destructive",
          });
          return;
        }
      }

      const commentRef = doc(db, `chats/${chatId}/comments`, commentId);
      await updateDoc(commentRef, { content });

      // Update local state
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId ? { ...comment, content } : comment
        )
      );

      // Reset editing state
      setEditingCommentId(null);
      setEditedContent('');

      toast({
        title: "Success",
        description: "Comment updated successfully.",
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      toast({
        title: "Error",
        description: "Failed to update comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const visibleComments = comments.slice(0, (maxVisibleGroup + 1) * COMMENTS_PER_GROUP);
  const hasMoreComments = comments.length > visibleComments.length;

  return (
    <div className="w-full bg-card rounded-xl sm:rounded-2xl shadow-lg p-3 sm:p-6 transition-all duration-300 hover:shadow-xl max-w-full">
      <div className="flex flex-col sm:flex-row items-start justify-between mb-4 sm:mb-8 gap-2">
        <div className="flex items-center gap-2 sm:gap-3">
          <div className={`bg-primary/10 p-1.5 sm:p-2 rounded-full ${animateIn} ${staggerDelay(0)}`}>
            <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-primary" />
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <h2 className={`text-base sm:text-lg md:text-2xl font-semibold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent ${animateIn} ${staggerDelay(1)}`}>
              Discussion
            </h2>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <a
                    href="https://isotopeai.featurebase.app"
                    target="_blank"
                    rel="noopener noreferrer"
                    data-featurebase-link
                    className={`inline-flex items-center gap-1.5 text-xs sm:text-sm text-muted-foreground hover:text-primary transition-colors duration-200 bg-muted/50 hover:bg-muted px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full ${animateIn} ${staggerDelay(2)}`}
                  >
                    <MessageSquarePlus className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                    <span className="hidden sm:inline">Request Features, Report Bugs, Give Feedback</span>
                    <span className="sm:hidden">Feedback</span>
                  </a>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Submit feature requests, report bugs, or give feedback</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className={`flex items-center gap-3 sm:gap-4 self-end ${animateIn} ${staggerDelay(3)}`}>
          {username && (
            <span className="text-xs sm:text-sm text-muted-foreground bg-muted/50 px-3 py-1 rounded-full">
              Signed in as {username}
            </span>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowCommentInput(!showCommentInput)}
            className={cn(
              "rounded-full hover:bg-primary/10 transition-all duration-300",
              showCommentInput && "rotate-90"
            )}
          >
            {showCommentInput ? (
              <X className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
            ) : (
              <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
            )}
          </Button>
        </div>
      </div>

      <div className={cn(
        "overflow-hidden transition-all duration-300",
        showCommentInput ? "max-h-[300px] opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="mb-4 sm:mb-6 space-y-2 sm:space-y-3 md:space-y-4 bg-white/5 p-3 sm:p-4 md:p-5 rounded-lg border border-border/50 backdrop-blur-sm">
          <div className="flex flex-col space-y-2 sm:space-y-3">
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Share your thoughts..."
              className="min-h-[80px] sm:min-h-[100px] md:min-h-[120px] text-xs sm:text-sm md:text-base resize-none bg-background/50 focus:bg-background transition-colors duration-200"
            />
            <Button
              onClick={handleSubmitComment}
              className="self-end text-xs sm:text-sm md:text-base px-3 sm:px-4 md:px-6 py-1 sm:py-2 transition-all duration-200 hover:scale-105"
              disabled={!username}
            >
              Post Comment
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {visibleComments.map((comment) => (
          <CommentComponent
            key={comment.id}
            comment={comment}
            onReply={handleAddReply}
            expandedReplyIds={expandedReplyIds}
            onExpandReply={handleExpandReply}
            onCollapseReply={handleCollapseReply}
            isDeveloperMode={isDeveloper || localStorage.getItem("isDeveloperMode") === "true"}
            handleDeleteReply={handleDeleteReply}
            handleDeleteComment={handleDeleteComment}
            handleEditComment={handleEditComment}
            handleEditReply={handleEditReply}
          />
        ))}
        {comments.length === 0 && (
          <div className={`text-left py-6 sm:py-8 md:py-12 px-3 sm:px-4 bg-muted/30 rounded-lg border border-border/50 ${animateIn} ${staggerDelay(4)}`}>
            <div className="flex items-center gap-2 sm:gap-3">
              <MessageSquare className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 text-muted-foreground/50" />
              <p className="text-xs sm:text-sm md:text-base text-muted-foreground">No discussions yet. Be the first to start a discussion!</p>
            </div>
          </div>
        )}
      </div>

      {hasMoreComments && (
        <Button
          variant="ghost"
          className={`mt-6 w-full flex items-center justify-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200 ${animateIn} ${staggerDelay(5)}`}
          onClick={() => setMaxVisibleGroup(prev => prev + 1)}
        >
          <ChevronDown className="h-4 w-4" />
          Show More Comments
        </Button>
      )}

      {maxVisibleGroup > 0 && (
        <Button
          variant="ghost"
          className={`mt-2 w-full flex items-center justify-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200 ${animateIn} ${staggerDelay(6)}`}
          onClick={() => setMaxVisibleGroup(0)}
        >
          <ChevronUp className="h-4 w-4" />
          Show Less
        </Button>
      )}
    </div>
  );
};
