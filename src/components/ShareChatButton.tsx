import React, { useState, useEffect } from 'react';
import { Share2, Check, AlertCircle, Copy } from 'lucide-react';
import { Button } from './ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from './ui/dialog';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { saveAIChat, getUserProfile } from '@/utils/firebase';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { pingSearchEngines } from '@/utils/seoUtils';

interface ShareChatButtonProps {
  chatId: string;
  chatData: any;
  className?: string;
  showTextLabel?: boolean;
  onShareGenerated?: (url: string) => void;
}

export function ShareChatButton({ 
  chatId, 
  chatData, 
  className, 
  showTextLabel = true,
  onShareGenerated 
}: ShareChatButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState<string>('');
  const [isSharing, setIsSharing] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [autoShared, setAutoShared] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Automatically generate share link when component mounts
  useEffect(() => {
    const autoGenerateShareLink = async () => {
      // Only auto-share if we have chat data, a user, and a valid chatId
      if (chatData?.messages?.length > 1 && user && chatId && !shareUrl) {
        try {
          // Get username from Firestore
          const userProfile = await getUserProfile(user.uid);
          const username = userProfile?.username || 'Anonymous';
          
          // Add user information to chat data
          const chatWithUser = {
            ...chatData,
            userId: user.uid,
            username: username,
            userPhotoURL: user.photoURL || '',
          };
          
          // Save chat to Firebase (always public)
          await saveAIChat(chatId, chatWithUser, true);
          
          // Generate share URL
          const baseUrl = window.location.origin;
          const generatedShareUrl = `${baseUrl}/shared/${chatId}`;
          setShareUrl(generatedShareUrl);
          setAutoShared(true);
          
          // Call callback with the generated URL if provided
          if (onShareGenerated) {
            onShareGenerated(generatedShareUrl);
          }
          
          // Ping search engines with the new URL to get it indexed
          pingSearchEngines(generatedShareUrl);
          
          console.log('Auto-shared chat with URL:', generatedShareUrl);
        } catch (error) {
          console.error('Error auto-sharing chat:', error);
          // Silent fail for auto-share - user can still manually share if needed
        }
      }
    };
    
    autoGenerateShareLink();
  }, [chatId, chatData, user, shareUrl, onShareGenerated]);

  const handleShareClick = () => {
    // If we already have a share URL, just open the dialog
    if (shareUrl) {
      setIsOpen(true);
      return;
    }
    
    // Otherwise, open the dialog and let user generate the link
    setIsOpen(true);
  };

  const handleShare = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to share this chat.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSharing(true);
      
      // Get username from Firestore
      const userProfile = await getUserProfile(user.uid);
      const username = userProfile?.username || 'Anonymous';
      
      console.log('Sharing chat with username:', username);
      
      // Add user information to chat data
      const chatWithUser = {
        ...chatData,
        userId: user.uid,
        username: username,
        userPhotoURL: user.photoURL || '',
      };
      
      // Save chat to Firebase (always public)
      await saveAIChat(chatId, chatWithUser, true);
      
      // Generate share URL
      const baseUrl = window.location.origin;
      const shareUrl = `${baseUrl}/shared/${chatId}`;
      setShareUrl(shareUrl);
      
      toast({
        title: "Chat Shared Successfully",
        description: "Anyone with the link can now view this chat.",
      });
    } catch (error) {
      console.error('Error sharing chat:', error);
      toast({
        title: "Error Sharing Chat",
        description: "An error occurred while sharing the chat. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSharing(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setIsCopied(true);
      
      toast({
        title: "Link Copied!",
        description: "Share link copied to clipboard.",
      });
      
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast({
        title: "Copy Failed",
        description: "Failed to copy link to clipboard. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <>
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={handleShareClick} 
        className={`${className} ${autoShared ? 'text-primary' : ''}`}
      >
        {isCopied ? (
          <Check className={`h-4 w-4 ${showTextLabel ? 'mr-2' : ''}`} />
        ) : (
          <Share2 className={`h-4 w-4 ${showTextLabel ? 'mr-2' : ''}`} />
        )}
        {showTextLabel && "Share"}
      </Button>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Share Chat</DialogTitle>
            <DialogDescription>
              {shareUrl ? "Copy this link to share" : "Share this conversation with others"}
            </DialogDescription>
          </DialogHeader>
          
          {!shareUrl ? (
            <>
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <p className="text-sm text-muted-foreground">
                  Anyone with the link will be able to view this chat without signing in.
                </p>
              </div>
              
              <DialogFooter>
                <Button 
                  onClick={handleShare} 
                  disabled={isSharing}
                >
                  {isSharing ? 'Sharing...' : 'Generate Share Link'}
                </Button>
              </DialogFooter>
            </>
          ) : (
            <>
              <div className="flex items-center space-x-2">
                <div className="grid flex-1 gap-2">
                  <Label htmlFor="share-link" className="sr-only">Share Link</Label>
                  <Input
                    id="share-link"
                    readOnly
                    value={shareUrl}
                    className="w-full"
                  />
                </div>
                <Button 
                  size="sm" 
                  className="px-3" 
                  onClick={copyToClipboard}
                >
                  {isCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              
              <DialogFooter>
                <Button 
                  onClick={() => setIsOpen(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
} 