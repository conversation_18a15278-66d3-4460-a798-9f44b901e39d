import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from './ui/button';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function SignIn() {
  const { signInWithGoogle, signInWithEmailPassword, signUpWithEmailPassword } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('signin');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const { toast } = useToast();

  const clearErrors = () => {
    setEmailError('');
    setPasswordError('');
  };

  const clearForm = () => {
    setEmail('');
    setPassword('');
    clearErrors();
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    clearForm();
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      clearForm();
      setActiveTab('signin');
    }
  };

  const handleGoogleSignIn = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    try {
      const result = await signInWithGoogle();
      if (!result) return;
    } catch (error: any) {
      console.error('Error signing in:', error);
      toast({
        title: "Sign-in Error",
        description: error.message || "Failed to sign in with Google. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSignIn = async () => {
    if (isLoading || !email || !password) return;

    clearErrors();
    setIsLoading(true);
    try {
      await signInWithEmailPassword(email, password);
      setIsDialogOpen(false);
    } catch (error: any) {
      console.error('Error signing in:', error);
      switch (error.code) {
        case 'auth/user-not-found':
          setEmailError('This email is not registered.');
          toast({
            title: "Account Not Found",
            description: "Would you like to create a new account? Switching to sign up...",
            variant: "default"
          });
          setActiveTab('signup');
          break;
        case 'auth/wrong-password':
        case 'auth/invalid-credential':
        case 'auth/invalid-login-credentials':
          setPasswordError('Incorrect email or password. Please try again.');
          break;
        case 'auth/invalid-email':
          setEmailError('Please enter a valid email address.');
          break;
        case 'auth/too-many-requests':
          toast({
            title: "Too Many Attempts",
            description: "Too many failed attempts. Please try again later or reset your password.",
            variant: "destructive"
          });
          break;
        default:
          console.log('Firebase error code:', error.code);
          toast({
            title: "Sign-in Error",
            description: "Failed to sign in. Please check your credentials and try again.",
            variant: "destructive"
          });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSignUp = async () => {
    if (isLoading || !email || !password) return;

    clearErrors();
    setIsLoading(true);
    try {
      await signUpWithEmailPassword(email, password);
      setIsDialogOpen(false);
    } catch (error: any) {
      console.error('Error signing up:', error);
      switch (error.code) {
        case 'auth/email-already-in-use':
          setEmailError('This email is already registered.');
          toast({
            title: "Account Exists",
            description: "This email is already registered!",
            variant: "default"
          });
          setActiveTab('signin');
          break;
        case 'auth/invalid-email':
          setEmailError('Please enter a valid email address.');
          break;
        case 'auth/weak-password':
          setPasswordError('Password should be at least 6 characters long.');
          break;
        default:
          toast({
            title: "Sign-up Error",
            description: error.message || "Failed to create account. Please try again.",
            variant: "destructive"
          });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setEmailError('');
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setPasswordError('');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col items-center justify-center p-4 sm:p-8 space-y-4 bg-white/[0.03] rounded-lg border border-white/[0.08] w-full"
    >
      <h2 className="text-xl sm:text-2xl font-bold text-white/90">Sign in to IsotopeAI</h2>
      <p className="text-sm sm:text-base text-white/60 text-center max-w-xl">
        Join our community to get instant help with your academic doubts in Physics, Chemistry, and Mathematics.
      </p>
      <p className="text-xs sm:text-sm text-white/40 text-center max-w-xl">
        Email verification is required to prevent abuse and ensure a safe, high-quality learning environment for all users.
      </p>
      <div className="flex items-center justify-center gap-4 w-full">
        <Button
          onClick={handleGoogleSignIn}
          disabled={isLoading}
          className="flex-1 flex items-center justify-center space-x-2 bg-white/10 hover:bg-white/20 text-white py-6"
        >
          <img src="/google.svg" alt="Google" className="w-5 h-5" />
          <span>{isLoading ? "Signing in..." : "Continue with Google"}</span>
        </Button>

        <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="bg-white/10 hover:bg-white/20 text-white border-white/[0.08] h-[52px] w-[52px]"
            >
              <Plus className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px] bg-black/90 border border-white/[0.08] overflow-y-auto w-[calc(100%-2rem)] p-6 rounded-lg fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 max-h-[90vh] m-4">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold text-white/90">Sign In or Sign Up</DialogTitle>
            </DialogHeader>
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="grid w-full grid-cols-2 bg-white/[0.03]">
                <TabsTrigger 
                  value="signin"
                  className="data-[state=active]:bg-white/10 text-white/70 data-[state=active]:text-white"
                >
                  Sign In
                </TabsTrigger>
                <TabsTrigger 
                  value="signup"
                  className="data-[state=active]:bg-white/10 text-white/70 data-[state=active]:text-white"
                >
                  Sign Up
                </TabsTrigger>
              </TabsList>
              <TabsContent value="signin" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email-signin" className="text-white/70">Email</Label>
                    <Input
                      id="email-signin"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={handleEmailChange}
                      className={`bg-white/[0.03] border-white/[0.08] text-white placeholder:text-white/40 focus-visible:ring-white/20 ${emailError ? 'border-red-500' : ''}`}
                      autoFocus={false}
                    />
                    {emailError && <p className="text-red-500 text-sm mt-1">{emailError}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password-signin" className="text-white/70">Password</Label>
                    <Input
                      id="password-signin"
                      type="password"
                      placeholder="Enter your password"
                      value={password}
                      onChange={handlePasswordChange}
                      className={`bg-white/[0.03] border-white/[0.08] text-white placeholder:text-white/40 focus-visible:ring-white/20 ${passwordError ? 'border-red-500' : ''}`}
                    />
                    {passwordError && <p className="text-red-500 text-sm mt-1">{passwordError}</p>}
                  </div>
                  <Button
                    className="w-full bg-white/10 hover:bg-white/20 text-white"
                    onClick={handleEmailSignIn}
                    disabled={isLoading || !email || !password}
                  >
                    {isLoading ? "Signing in..." : "Sign In"}
                  </Button>
                </div>
              </TabsContent>
              <TabsContent value="signup" className="space-y-4 mt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email-signup" className="text-white/70">Email</Label>
                    <Input
                      id="email-signup"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={handleEmailChange}
                      className={`bg-white/[0.03] border-white/[0.08] text-white placeholder:text-white/40 focus-visible:ring-white/20 ${emailError ? 'border-red-500' : ''}`}
                      autoFocus={false}
                    />
                    {emailError && <p className="text-red-500 text-sm mt-1">{emailError}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password-signup" className="text-white/70">Password</Label>
                    <Input
                      id="password-signup"
                      type="password"
                      placeholder="Create a password (min. 6 characters)"
                      value={password}
                      onChange={handlePasswordChange}
                      className={`bg-white/[0.03] border-white/[0.08] text-white placeholder:text-white/40 focus-visible:ring-white/20 ${passwordError ? 'border-red-500' : ''}`}
                    />
                    {passwordError && <p className="text-red-500 text-sm mt-1">{passwordError}</p>}
                  </div>
                  <Button
                    className="w-full bg-white/10 hover:bg-white/20 text-white"
                    onClick={handleEmailSignUp}
                    disabled={isLoading || !email || !password}
                  >
                    {isLoading ? "Creating account..." : "Sign Up"}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      </div>
    </motion.div>
  );
}