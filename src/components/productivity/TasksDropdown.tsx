import { useState, useRef, useEffect } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { ListTodo, Plus, Eye, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { useTodoStore } from '@/stores/todoStore';
import { useAuth } from '@/contexts/AuthContext';
import Draggable from 'react-draggable';
import { TodoBoard } from './TodoBoard';

interface TasksDropdownProps {
  groupId?: string;
  initialSelectedSubject?: string | null; // Add the new prop
}

export function TasksDropdown({
  groupId,
  initialSelectedSubject // Destructure the new prop
}: TasksDropdownProps) {
  const { user } = useAuth();
  const { addTask } = useTodoStore();

  const [isAddingTask, setIsAddingTask] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showTasksMenu, setShowTasksMenu] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [newTaskPriority, setNewTaskPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [newTaskDueDate, setNewTaskDueDate] = useState<Date | undefined>(undefined);
  const [descriptionError, setDescriptionError] = useState('');
  const [position, setPosition] = useState({ x: 20, y: 500 });
  const nodeRef = useRef(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // State for the selected subject in the dropdown
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null);

  // Effect to set the initial selected subject from props
  useEffect(() => {
    if (initialSelectedSubject) {
      setSelectedSubject(initialSelectedSubject);
      // Optionally open the tasks menu automatically if a subject is pre-selected
      setShowTasksMenu(true);
    }
  }, [initialSelectedSubject]); // Run this effect when initialSelectedSubject changes

  const handleAddTask = async () => {
    if (!newTaskTitle.trim()) {
      return;
    }

    if (newTaskDescription.length > 500) {
      setDescriptionError('Description must be less than 500 characters');
      return;
    }

    const taskData = {
      title: newTaskTitle.trim(),
      description: newTaskDescription.trim(),
      priority: newTaskPriority,
      dueDate: newTaskDueDate ? newTaskDueDate.getTime() : undefined,
      createdBy: user?.uid || '',
    };

    await addTask(taskData);

    // Reset form
    setNewTaskTitle('');
    setNewTaskDescription('');
    setNewTaskPriority('medium');
    setNewTaskDueDate(undefined);
    setIsAddingTask(false);
  };

  // Set initial position based on viewport size
  useEffect(() => {
    setPosition({ x: 20, y: window.innerHeight - 80 });
  }, []);

  // Handle window resize to keep the button within the viewport
  useEffect(() => {
    const handleResize = () => {
      // Adjust position if it's outside the viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let newX = position.x;
      let newY = position.y;

      // Keep button within horizontal bounds
      if (newX < 0) newX = 0;
      if (newX > viewportWidth - 60) newX = viewportWidth - 60;

      // Keep button within vertical bounds
      if (newY < 0) newY = 0;
      if (newY > viewportHeight - 60) newY = viewportHeight - 60;

      if (newX !== position.x || newY !== position.y) {
        setPosition({ x: newX, y: newY });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [position]);

  // Handle click outside to close the tasks menu
  useEffect(() => {
    // We'll use a flag to track if we should ignore the next click
    let ignoreNextClick = false;

    const handleClickOutside = (event: MouseEvent) => {
      // If we should ignore this click, reset the flag and return
      if (ignoreNextClick) {
        ignoreNextClick = false;
        return;
      }

      // Only close if the click is outside the menu
      if (menuRef.current && !menuRef.current.contains(event.target as Node) && showTasksMenu) {
        setShowTasksMenu(false);
      }
    };

    // Handle mousedown events inside the menu to set the ignore flag
    const handleMenuMouseDown = () => {
      ignoreNextClick = true;
    };

    document.addEventListener('mousedown', handleClickOutside);

    // Add event listener to the menu to set the ignore flag
    if (menuRef.current) {
      menuRef.current.addEventListener('mousedown', handleMenuMouseDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      if (menuRef.current) {
        menuRef.current.removeEventListener('mousedown', handleMenuMouseDown);
      }
    };
  }, [showTasksMenu]);

  return (
    <div>
      {/* Container for the draggable button */}
      <div className="fixed inset-0 pointer-events-none" style={{ zIndex: 50 }}>
        <Draggable
          nodeRef={nodeRef}
          position={position}
          onStop={(_, data) => {
            setPosition({ x: data.x, y: data.y });
          }}
          bounds="parent"
          handle=".drag-handle"
        >
          <div ref={nodeRef} className="absolute pointer-events-auto">
            {/* Theme-aware draggable button container */}
            <div className="p-1 rounded-full bg-background/80 dark:bg-black/20 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 border border-border dark:border-white/10 cursor-move drag-handle">
              <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                <DropdownMenuTrigger asChild>
                  {/* Keep primary button style */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full bg-gradient-to-r from-purple-600/80 to-indigo-600/80 hover:from-purple-600 hover:to-indigo-600 text-white relative group"
                  >
                    <div className="absolute inset-0 rounded-full border-2 border-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <span className="absolute -top-1 -right-1 w-3 h-3 bg-white/30 rounded-full animate-pulse"></span>
                    <ListTodo className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                {/* Theme-aware Dropdown Content */}
                <DropdownMenuContent align="start" sideOffset={5} className="w-56 mb-2 bg-popover border-border text-popover-foreground">
                  <DropdownMenuLabel>Tasks</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setShowTasksMenu(true)} className="hover:bg-accent">
                    <Eye className="mr-2 h-4 w-4" />
                    <span>Show Tasks</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setIsAddingTask(true)} className="hover:bg-accent">
                    <Plus className="mr-2 h-4 w-4" />
                    <span>Add New Task</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </Draggable>
      </div>

      {/* Tasks Hover Menu */}
      {showTasksMenu && (
        <div
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 flex items-center justify-center"
          onClick={(e) => {
            // Only close if the click is directly on the overlay
            if (e.target === e.currentTarget) {
              setShowTasksMenu(false);
            }
          }}
        >
          <div
            ref={menuRef}
            // Theme-aware tasks menu container
            className="bg-background dark:bg-background/95 backdrop-blur-md rounded-lg shadow-xl border border-border dark:border-white/10 w-[90%] max-w-5xl max-h-[80vh] overflow-hidden animate-in fade-in zoom-in-95 duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Theme-aware header border */}
            <div className="flex justify-between items-center p-4 border-b border-border dark:border-border/50">
              <h2 className="text-xl font-bold text-foreground">Tasks</h2>
              {/* Theme-aware close button */}
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full hover:bg-accent text-muted-foreground"
                onClick={() => setShowTasksMenu(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            <div className="p-4 overflow-auto max-h-[calc(80vh-80px)]">
              {/* Pass the groupId to TodoBoard to ensure it fetches the correct tasks */}
              <TodoBoard compact={true} groupId={groupId} />
            </div>
          </div>
        </div>
      )}

      {/* Add Task Dialog */}
      <Dialog open={isAddingTask} onOpenChange={setIsAddingTask}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="title" className="text-sm font-medium">
                Title
              </label>
              {/* Theme-aware Input */}
              <Input
                id="title"
                value={newTaskTitle}
                onChange={(e) => setNewTaskTitle(e.target.value)}
                placeholder="Task title"
                className="bg-muted border-border"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              {/* Theme-aware Textarea */}
              <Textarea
                id="description"
                value={newTaskDescription}
                onChange={(e) => {
                  setNewTaskDescription(e.target.value);
                  if (e.target.value.length > 500) {
                    setDescriptionError('Description must be less than 500 characters');
                  } else {
                    setDescriptionError('');
                  }
                }}
                placeholder="Task description"
                className={`${descriptionError ? 'border-red-500' : 'border-border'} bg-muted`}
              />
              {descriptionError && (
                <p className="text-xs text-red-500">{descriptionError}</p>
              )}
              <p className="text-xs text-muted-foreground">
                {newTaskDescription.length}/500 characters
              </p>
            </div>

            <div className="grid gap-2">
              <label htmlFor="priority" className="text-sm font-medium">
                Priority
              </label>
              {/* Theme-aware Select */}
              <Select
                value={newTaskPriority}
                onValueChange={(value) => setNewTaskPriority(value as 'low' | 'medium' | 'high')}
              >
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent className="bg-popover border-border text-popover-foreground">
                  <SelectItem value="low" className="hover:bg-accent">Low</SelectItem>
                  <SelectItem value="medium" className="hover:bg-accent">Medium</SelectItem>
                  <SelectItem value="high" className="hover:bg-accent">High</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="dueDate" className="text-sm font-medium">
                Due Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  {/* Theme-aware Button */}
                  <Button
                    variant="outline"
                    className="justify-start text-left font-normal bg-muted border-border hover:bg-accent"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {newTaskDueDate ? format(newTaskDueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                {/* Theme-aware Popover Content & Calendar */}
                <PopoverContent className="w-auto p-0 bg-popover border-border text-popover-foreground">
                  <Calendar
                    mode="single"
                    selected={newTaskDueDate}
                    onSelect={setNewTaskDueDate}
                    initialFocus
                    className="bg-popover text-popover-foreground" // Ensure calendar itself is themed
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <DialogFooter>
            {/* Primary button (styles likely ok) */}
            <Button onClick={handleAddTask}>Add Task</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
