import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useChatStore } from "@/stores/chatStore"
import { doc, onSnapshot, setDoc } from "firebase/firestore"
import { db } from "@/utils/firebase"
import { StudyTimer } from "./StudyTimer"
import { Button } from "@/components/ui/button"
import { Users } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface GroupTimerState {
  isRunning: boolean
  startTime: number | null
  timeRemaining: number
  currentPhase: "work" | "shortBreak" | "longBreak"
  completedSessions: number
  mode: "pomodoro" | "stopwatch"
}

export function GroupTimer() {
  const { user } = useAuth()
  const { activeGroupId, groups } = useChatStore()
  const [groupTimerState, setGroupTimerState] = useState<GroupTimerState | null>(null)
  const [isSynced, setIsSynced] = useState(false)

  const activeGroup = groups.find(g => g.id === activeGroupId)

  useEffect(() => {
    if (!activeGroupId || !user) return

    // Subscribe to group timer state changes
    const unsubscribe = onSnapshot(doc(db, "groupTimers", activeGroupId), (doc) => {
      if (doc.exists()) {
        setGroupTimerState(doc.data() as GroupTimerState)
      }
    })

    return () => unsubscribe()
  }, [activeGroupId, user])

  const syncWithGroup = async () => {
    if (!activeGroupId || !user) return

    try {
      setIsSynced(true)
      // If no group timer exists, initialize one
      if (!groupTimerState) {
        const initialState: GroupTimerState = {
          isRunning: false,
          startTime: null,
          timeRemaining: 1500, // 25 minutes
          currentPhase: "work",
          completedSessions: 0,
          mode: "pomodoro"
        }
        await setDoc(doc(db, "groupTimers", activeGroupId), initialState)
      }

      toast({
        title: "Synced with group",
        description: "Your timer is now synchronized with the group"
      })
    } catch (error) {
      console.error("Error syncing with group:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to sync with group timer"
      })
      setIsSynced(false)
    }
  }

  if (!activeGroupId) {
    return (
      <div className="flex items-center justify-center p-8 text-white/60">
        Select a group to use synchronized timer
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center gap-6">
      <div className="flex items-center gap-4">
        <h2 className="text-2xl font-semibold">
          {activeGroup?.name} Group Timer
        </h2>
        {!isSynced && (
          <Button
            variant="outline"
            className="gap-2"
            onClick={syncWithGroup}
          >
            <Users className="h-4 w-4" />
            Sync with Group
          </Button>
        )}
      </div>

      {isSynced && groupTimerState && (
        <StudyTimer 
          mode={groupTimerState.mode}
          groupSync={{
            groupId: activeGroupId,
            state: groupTimerState,
            setState: async (newState) => {
              try {
                await setDoc(doc(db, "groupTimers", activeGroupId), newState)
              } catch (error) {
                console.error("Error updating group timer:", error)
              }
            }
          }}
        />
      )}
    </div>
  )
} 