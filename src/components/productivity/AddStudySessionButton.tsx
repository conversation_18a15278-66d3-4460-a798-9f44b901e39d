import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { CalendarIcon, Plus, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/utils/firebase';
import { toast } from '@/components/ui/use-toast';
import { formatDateToLocalYYYYMMDD, getWeekNumber } from '@/utils/dateUtils';
import { SubjectManager, Subject } from './SubjectManager';
import { cn } from '@/lib/utils';

interface AddStudySessionButtonProps {
  className?: string;
}

export function AddStudySessionButton({ className }: AddStudySessionButtonProps) {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('10:00');
  const [mode, setMode] = useState<'pomodoro' | 'stopwatch'>('stopwatch');
  const [taskType, setTaskType] = useState('Study');
  const [taskDescription, setTaskDescription] = useState('');
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [productivityRating, setProductivityRating] = useState(3);

  // Fixed position styles for the floating action button with improved mobile accessibility
  const fabStyle = "fixed bottom-8 right-6 z-[100] rounded-full shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300 bg-purple-600/90 hover:bg-purple-700 text-white h-12 w-12 sm:h-10 sm:w-10 flex items-center justify-center";

  // Handle dialog open/close
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
  };

  // Handle subject change
  const handleSubjectChange = (subject: Subject | null) => {
    setSelectedSubject(subject);
  };

  const handleAddSession = async () => {
    if (!selectedSubject) {
      toast({
        title: 'Error',
        description: 'Please select a subject',
        variant: 'destructive'
      });
      return;
    }

    if (!taskType) {
      toast({
        title: 'Error',
        description: 'Please select a task type',
        variant: 'destructive'
      });
      return;
    }

    // Parse start and end times
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const [endHours, endMinutes] = endTime.split(':').map(Number);

    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) {
      toast({
        title: 'Error',
        description: 'Invalid time format',
        variant: 'destructive'
      });
      return;
    }

    // Calculate start and end in minutes since midnight for comparison
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    // Calculate duration in seconds
    let totalDurationSeconds;
    if (endTotalMinutes > startTotalMinutes) {
      totalDurationSeconds = (endTotalMinutes - startTotalMinutes) * 60;
    } else {
      // End time is on the next day
      totalDurationSeconds = ((24 * 60) - startTotalMinutes + endTotalMinutes) * 60;
    }

    if (totalDurationSeconds <= 0) {
      toast({
        title: 'Error',
        description: 'Duration must be greater than zero',
        variant: 'destructive'
      });
      return;
    }

    // Create the start and end time Date objects
    // First clone the selected date to avoid modifying the original
    const startTimeDate = new Date(selectedDate);
    const endTimeDate = new Date(selectedDate);

    // Set times based on the input values
    startTimeDate.setHours(startHours, startMinutes, 0, 0);
    endTimeDate.setHours(endHours, endMinutes, 0, 0);

    // If end time is earlier than start time, it's assumed to be the next day
    if (endTimeDate < startTimeDate) {
      endTimeDate.setDate(endTimeDate.getDate() + 1);
    }

    // Ensure the dates are valid
    if (isNaN(startTimeDate.getTime()) || isNaN(endTimeDate.getTime())) {
      toast({
        title: 'Error',
        description: 'Invalid date or time',
        variant: 'destructive'
      });
      return;
    }

    // Convert to ISO strings for Firebase storage
    const startTimeISO = startTimeDate.toISOString();
    const endTimeISO = endTimeDate.toISOString();

    const session = {
      userId: user.uid,
      subject: selectedSubject.name,
      taskName: 'Manual Entry',
      taskType: taskType,
      taskDescription: taskDescription,
      startTime: startTimeISO,
      endTime: endTimeISO,
      duration: totalDurationSeconds,
      mode: mode,
      phase: 'work',
      completed: true,
      date: formatDateToLocalYYYYMMDD(selectedDate),
      weekNumber: getWeekNumber(selectedDate),
      month: formatDateToLocalYYYYMMDD(selectedDate).slice(0, 7), // YYYY-MM format
      year: selectedDate.getFullYear(),
      manualEntry: true,
      subjectColor: selectedSubject.color, // Include subject color
      productivityRating: productivityRating, // Add productivity rating
    };

    try {
      // Save to Firestore in the same format as StudyTimer component
      // This ensures it appears in analytics
      const userRef = doc(db, "users", user.uid);
      const sessionId = `manual-${Date.now()}`; // Create a unique ID for the manual entry

      // Use setDoc with merge to add this session to the user's studySessions object
      await setDoc(userRef, {
        studySessions: {
          [sessionId]: session
        }
      }, { merge: true });

      // Reset form and close dialog
      setSelectedSubject(null);
      setStartTime('09:00');
      setEndTime('10:00');
      setTaskType('Study');
      setTaskDescription('');
      setSelectedDate(new Date());
      setMode('stopwatch');
      setProductivityRating(3);
      setIsOpen(false);

      toast({
        title: 'Success',
        description: 'Study session added successfully'
      });
    } catch (error) {
      console.error('Error adding study session:', error);
      toast({
        title: 'Error',
        description: 'Failed to add study session',
        variant: 'destructive'
      });
    }
  };

  return (
    <>
      <Button
        size="icon"
        className={`fixed z-50 bottom-8 right-8 h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-primary text-white hover:bg-primary/90 ${className || ''}`}
        onClick={() => setIsOpen(true)}
        aria-label="Add study session"
      >
        <Plus className="h-5 w-5 sm:h-4 sm:w-4 text-white" />
      </Button>

      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        {/* Adjusted max-width and added max-height/flex for vertical fitting */}
        <DialogContent className="bg-[#1a1f3c]/90 backdrop-blur-md border-white/10 text-white w-[90vw] max-w-sm sm:max-w-md max-h-[85vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <div className="bg-purple-600 p-1.5 rounded-md">
                <Clock className="h-5 w-5" />
              </div>
              <span>Add Study Session</span>
            </DialogTitle>
          </DialogHeader>

          {/* Added overflow-y-auto for vertical scrolling */}
          <div className="grid gap-4 py-4 overflow-y-auto">
            {/* Subject Selection */}
            <div className="grid gap-2">
              <Label htmlFor="subject" className="text-white/80">Subject</Label>
              <div className="bg-transparent border-white/10 text-white rounded-md">
                <SubjectManager
                  selectedSubject={selectedSubject}
                  onSubjectChange={handleSubjectChange}
                />
              </div>
            </div>

            {/* Task type and Description */}
            <div className="grid gap-2">
              <Label htmlFor="taskType" className="text-white/80">Task Type</Label>
              <Select value={taskType} onValueChange={setTaskType}>
                <SelectTrigger className="bg-transparent border-white/10 text-white">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent className="bg-[#1a1f3c] border-white/10 text-white">
                  <SelectItem value="Lecture">Lecture</SelectItem>
                  <SelectItem value="Exercise">Exercise</SelectItem>
                  <SelectItem value="Reading">Reading</SelectItem>
                  <SelectItem value="Practice">Practice</SelectItem>
                  <SelectItem value="Review">Review</SelectItem>
                  <SelectItem value="Study">General Study</SelectItem>
                  <SelectItem value="Custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="taskDescription" className="text-white/80">Description</Label>
              <Input
                id="taskDescription"
                placeholder={`What ${taskType.toLowerCase()} did you work on?`}
                value={taskDescription}
                onChange={(e) => setTaskDescription(e.target.value)}
                className="bg-transparent border-white/10 text-white"
              />
            </div>

            {/* Time selection - Adjusted grid for responsiveness */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime" className="text-white/80">Start Time</Label>
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-white/60" />
                  <Input
                    id="startTime"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="bg-transparent border-white/10 text-white"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime" className="text-white/80">End Time</Label>
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-white/60" />
                  <Input
                    id="endTime"
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    className="bg-transparent border-white/10 text-white"
                  />
                </div>
              </div>
            </div>

            {/* Date */}
            <div className="grid gap-2">
              <Label className="text-white/80">Date</Label>
              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="bg-transparent border-white/10 text-white justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-[#1a1f3c] border-white/10">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => {
                      if (date) {
                        setSelectedDate(date);
                        setIsCalendarOpen(false);
                      }
                    }}
                    disabled={(date) => {
                      // Disable future dates
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);
                      return date > today;
                    }}
                    initialFocus
                    className="bg-[#1a1f3c] text-white"
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Mode Selection */}
            <div className="grid gap-2">
              <Label htmlFor="mode" className="text-white/80">Study Mode</Label>
              <Select value={mode} onValueChange={(value) => setMode(value as 'pomodoro' | 'stopwatch')}>
                <SelectTrigger className="bg-transparent border-white/10 text-white">
                  <SelectValue placeholder="Select mode" />
                </SelectTrigger>
                <SelectContent className="bg-[#1a1f3c] border-white/10 text-white">
                  <SelectItem value="pomodoro">Pomodoro</SelectItem>
                  <SelectItem value="stopwatch">Stopwatch</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Productivity Rating - Adjusted flex direction for responsiveness */}
            <div className="grid gap-2">
              <Label htmlFor="productivityRating" className="text-white/80">How productive was this session?</Label>
              {/* Stack vertically on xs, horizontal on sm+ */}
              <div className="flex flex-col sm:flex-row sm:justify-between items-center gap-2 mt-1">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <button
                    key={rating}
                    type="button"
                    onClick={() => setProductivityRating(rating)}
                    // Ensure buttons take full width when stacked
                    className={`w-full sm:w-auto flex flex-col items-center p-3 rounded-xl transition-all duration-200 ${
                      productivityRating === rating
                        ? "bg-gradient-to-br from-purple-600 to-indigo-600 text-white scale-105 shadow-md"
                        : "bg-gradient-to-br from-background/80 to-background/30 dark:from-white/5 dark:to-white/2 text-muted-foreground hover:from-purple-100/20 hover:to-indigo-100/20 dark:hover:from-purple-900/20 dark:hover:to-indigo-900/20 border border-purple-100/10 dark:border-white/5"
                    }`}
                  >
                    <span className="text-2xl mb-1">
                      {rating === 1 ? "😔" : 
                       rating === 2 ? "😐" : 
                       rating === 3 ? "🙂" : 
                       rating === 4 ? "😊" : "🤩"}
                    </span>
                    <span className="text-xs">
                      {rating === 1 ? "Poor" : 
                       rating === 2 ? "Fair" : 
                       rating === 3 ? "Good" : 
                       rating === 4 ? "Great" : "Excellent!"}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="text-white border-white/10 hover:bg-white/5"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddSession}
              className="bg-purple-600 hover:bg-purple-700 text-white"
              disabled={!selectedSubject || !selectedSubject.name}
            >
              Add Session
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
