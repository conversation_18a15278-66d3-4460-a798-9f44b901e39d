import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import { format, isPast, startOfDay, isToday, isTomorrow, isThisWeek } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  CalendarIcon,
  Edit,
  Filter,
  Trash2,
  X,
  AlertCircle,
  Clock,
  CheckCircle2,
  CircleSlash
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { TodoItem, TodoColumn } from '@/types/todo';
import { useTodoStore } from '@/stores/todoStore';
import { Separator } from '@/components/ui/separator';

interface TodoTableProps {
  tasks: TodoItem[];
}

export function TodoTable({ tasks }: TodoTableProps) {
  const { board, updateTask, deleteTask } = useTodoStore();
  const [isEditing, setIsEditing] = useState(false);
  const [currentTask, setCurrentTask] = useState<TodoItem | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [selectedColumn, setSelectedColumn] = useState('');
  const [descriptionError, setDescriptionError] = useState('');

  // Filters
  const [showOverdueOnly, setShowOverdueOnly] = useState(false);
  const [priorityFilter, setPriorityFilter] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [dueDateFilter, setDueDateFilter] = useState<'all' | 'today' | 'tomorrow' | 'this-week' | 'no-date'>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Get column name for a task
  const getColumnForTask = (taskId: string): string => {
    for (const columnId in board.columns) {
      if (board.columns[columnId].taskIds.includes(taskId)) {
        return board.columns[columnId].title;
      }
    }
    return 'Unknown';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500 hover:bg-red-600';
      case 'medium':
        return 'bg-yellow-500 hover:bg-yellow-600';
      case 'low':
        return 'bg-green-500 hover:bg-green-600';
      default:
        return 'bg-blue-500 hover:bg-blue-600';
    }
  };

  const handleEditClick = (task: TodoItem) => {
    setCurrentTask(task);
    setTitle(task.title);
    setDescription(task.description);
    setPriority(task.priority);
    setDueDate(task.dueDate ? new Date(task.dueDate) : undefined);

    // Find current column
    for (const columnId in board.columns) {
      if (board.columns[columnId].taskIds.includes(task.id)) {
        setSelectedColumn(columnId);
        break;
      }
    }

    setIsEditing(true);
  };

  const handleSave = async () => {
    // Reset error state
    setDescriptionError('');

    // Validate inputs
    if (!currentTask || !title.trim()) {
      toast({
        title: "Validation Error",
        description: "Task title cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    if (!description.trim()) {
      setDescriptionError('Description is required');
      toast({
        title: "Validation Error",
        description: "Task description cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Update task details
      await updateTask(currentTask.id, {
        title,
        description,
        priority,
        dueDate: dueDate ? dueDate.getTime() : undefined,
      });

      // If column changed, move the task
      if (selectedColumn) {
        // Find current column
        let sourceColumnId = '';
        for (const columnId in board.columns) {
          if (board.columns[columnId].taskIds.includes(currentTask.id)) {
            sourceColumnId = columnId;
            break;
          }
        }

        // If column changed, move the task
        if (sourceColumnId && sourceColumnId !== selectedColumn) {
          const sourceIndex = board.columns[sourceColumnId].taskIds.indexOf(currentTask.id);
          const destIndex = 0; // Add to the top of the destination column

          await useTodoStore.getState().moveTask(
            { droppableId: sourceColumnId, index: sourceIndex, taskId: currentTask.id }, // Pass taskId here
            { droppableId: selectedColumn, index: destIndex, taskId: currentTask.id } // Pass taskId here
          );
        }
      }

      setIsEditing(false);
      toast({
        title: "Task saved",
        description: "Your task changes have been saved.",
      });

    } catch (error: any) {
      console.error('Error saving task:', error);
      toast({
        title: "Error saving task",
        description: error.message || "There was a problem saving your task.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!currentTask) return;

    await deleteTask(currentTask.id);
    setIsEditing(false);
  };

  // Check if a task is overdue
  const isTaskOverdue = (task: TodoItem): boolean => {
    if (!task.dueDate) return false;

    const today = startOfDay(new Date());
    const taskDueDate = startOfDay(new Date(task.dueDate));

    // Task is overdue if:
    // 1. Due date is before today (not including today)
    // 2. Task is not in the "Done" column
    const isDone = getColumnForTask(task.id) === 'Done';

    return taskDueDate < today && !isDone;
  };

  // Check if a task matches the due date filter
  const matchesDueDateFilter = (task: TodoItem): boolean => {
    if (dueDateFilter === 'all') return true;
    if (dueDateFilter === 'no-date' && !task.dueDate) return true;
    if (!task.dueDate) return false;

    const taskDate = new Date(task.dueDate);

    switch (dueDateFilter) {
      case 'today':
        return isToday(taskDate);
      case 'tomorrow':
        return isTomorrow(taskDate);
      case 'this-week':
        return isThisWeek(taskDate, { weekStartsOn: 1 }); // Week starts on Monday
      default:
        return true;
    }
  };

  // Check if a task matches all filters
  const matchesAllFilters = (task: TodoItem): boolean => {
    // Overdue filter
    if (showOverdueOnly && !isTaskOverdue(task)) return false;

    // Priority filter
    if (priorityFilter !== 'all' && task.priority !== priorityFilter) return false;

    // Due date filter
    if (!matchesDueDateFilter(task)) return false;

    // Status filter
    if (statusFilter !== 'all') {
      const taskStatus = getColumnForTask(task.id);
      if (taskStatus !== statusFilter) return false;
    }

    return true;
  };

  // Sort tasks by overdue status, then priority, then due date
  const sortedTasks = [...tasks].sort((a, b) => {
    // First sort by overdue status (overdue tasks first)
    const aIsOverdue = isTaskOverdue(a);
    const bIsOverdue = isTaskOverdue(b);

    if (aIsOverdue && !bIsOverdue) return -1;
    if (!aIsOverdue && bIsOverdue) return 1;

    // If both tasks have the same overdue status, sort by priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];

    if (priorityDiff !== 0) {
      return priorityDiff; // If priorities are different, sort by priority
    }

    // Then sort by due date (earliest first)
    if (a.dueDate && b.dueDate) {
      return a.dueDate - b.dueDate;
    } else if (a.dueDate) {
      return -1; // a has due date, b doesn't
    } else if (b.dueDate) {
      return 1; // b has due date, a doesn't
    }

    // If neither has a due date and priorities are the same, keep original order
    return 0;
  });

  // Filter tasks based on all active filters
  const filteredTasks = sortedTasks.filter(task => matchesAllFilters(task));

  // Get all available statuses (column titles)
  const statuses = Object.values(board.columns).map(column => column.title);

  // Reset all filters
  const resetFilters = () => {
    setShowOverdueOnly(false);
    setPriorityFilter('all');
    setDueDateFilter('all');
    setStatusFilter('all');
  };

  // Check if any filters are active
  const hasActiveFilters = showOverdueOnly || priorityFilter !== 'all' || dueDateFilter !== 'all' || statusFilter !== 'all';

  // Prevent event propagation to keep the hover menu open when interacting with filters
  const handleFilterClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Prevent mousedown events from propagating to avoid closing the hover menu
  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    // Theme-aware container border
    <div
      className="rounded-md border border-border"
      onClick={handleFilterClick}
      onMouseDown={handleMouseDown}
    >
      {/* Theme-aware filter section background and border */}
      <div className="p-4 space-y-4 border-b border-border bg-muted/50 dark:bg-muted/20">
        <div className="flex justify-between items-center">
          {/* Theme-aware title */}
          <h3 className="text-lg font-medium text-foreground">Task Filters</h3>

          {hasActiveFilters && (
            // Theme-aware clear button
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                resetFilters();
              }}
              className="flex items-center gap-1 border-destructive/50 text-destructive hover:bg-destructive/10 hover:text-destructive"
            >
              <X className="h-3.5 w-3.5" />
              Clear Filters
            </Button>
          )}
        </div>

        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pb-2">
            {/* Theme-aware badge */}
            <Badge variant="outline" className="bg-muted text-muted-foreground border-border text-xs">
              Showing {filteredTasks.length} of {tasks.length} tasks
            </Badge>

            {showOverdueOnly && (
              // Theme-aware badge
              <Badge className="bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-900/70 border-red-200 dark:border-red-800 text-xs">
                Overdue Only
              </Badge>
            )}

            {priorityFilter !== 'all' && (
              // Theme-aware badge
              <Badge className={`text-xs ${
                priorityFilter === 'high' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800' :
                priorityFilter === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800' :
                'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800'
              }`}>
                {priorityFilter.charAt(0).toUpperCase() + priorityFilter.slice(1)} Priority
              </Badge>
            )}

            {dueDateFilter !== 'all' && (
              // Theme-aware badge
              <Badge className="bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/70 border-blue-200 dark:border-blue-800 text-xs">
                Due: {dueDateFilter === 'today' ? 'Today' :
                     dueDateFilter === 'tomorrow' ? 'Tomorrow' :
                     dueDateFilter === 'this-week' ? 'This Week' :
                     'No Date'}
              </Badge>
            )}

            {statusFilter !== 'all' && (
              // Theme-aware badge
              <Badge className="bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-900/70 border-purple-200 dark:border-purple-800 text-xs">
                Status: {statusFilter}
              </Badge>
            )}
          </div>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
          {/* Overdue filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-1.5">
              <AlertCircle className="h-4 w-4 text-red-500" />
              Overdue
            </label>
            {/* Theme-aware button */}
            <Button
              variant={showOverdueOnly ? "destructive" : "outline"}
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setShowOverdueOnly(!showOverdueOnly);
              }}
              className={`w-full justify-start ${showOverdueOnly ? "" : "border-destructive/50 text-destructive hover:bg-destructive/10 hover:text-destructive"}`}
            >
              {showOverdueOnly ? "Showing Overdue Only" : "Show All Overdue Tasks"}
            </Button>
          </div>

          {/* Priority filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-1.5">
              <Filter className="h-4 w-4 text-orange-500" />
              Priority
            </label>
            {/* Theme-aware Select */}
            <Select
              value={priorityFilter}
              onValueChange={(value) => setPriorityFilter(value as any)}
              onOpenChange={(open) => {
                // Prevent closing the hover menu when opening/closing the select dropdown
                if (open) {
                  const event = new MouseEvent('click', { bubbles: true });
                  event.stopPropagation = () => {};
                }
              }}
            >
              <SelectTrigger className="h-9 border-border dark:border-orange-200">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border text-popover-foreground">
                <SelectItem value="all" className="hover:bg-accent">All Priorities</SelectItem>
                <SelectItem value="high" className="text-red-600 font-medium hover:bg-accent">High</SelectItem>
                <SelectItem value="medium" className="text-yellow-600 font-medium hover:bg-accent">Medium</SelectItem>
                <SelectItem value="low" className="text-green-600 font-medium hover:bg-accent">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Due date filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-1.5">
              <Clock className="h-4 w-4 text-blue-500" />
              Due Date
            </label>
            {/* Theme-aware Select */}
            <Select
              value={dueDateFilter}
              onValueChange={(value) => setDueDateFilter(value as any)}
              onOpenChange={(open) => {
                // Prevent closing the hover menu when opening/closing the select dropdown
                if (open) {
                  const event = new MouseEvent('click', { bubbles: true });
                  event.stopPropagation = () => {};
                }
              }}
            >
              <SelectTrigger className="h-9 border-border dark:border-blue-200">
                <SelectValue placeholder="Select due date" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border text-popover-foreground">
                <SelectItem value="all" className="hover:bg-accent">All Due Dates</SelectItem>
                <SelectItem value="today" className="hover:bg-accent">Due Today</SelectItem>
                <SelectItem value="tomorrow" className="hover:bg-accent">Due Tomorrow</SelectItem>
                <SelectItem value="this-week" className="hover:bg-accent">Due This Week</SelectItem>
                <SelectItem value="no-date" className="hover:bg-accent">No Due Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-1.5">
              <CheckCircle2 className="h-4 w-4 text-purple-500" />
              Status
            </label>
            {/* Theme-aware Select */}
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
              onOpenChange={(open) => {
                // Prevent closing the hover menu when opening/closing the select dropdown
                if (open) {
                  const event = new MouseEvent('click', { bubbles: true });
                  event.stopPropagation = () => {};
                }
              }}
            >
              <SelectTrigger className="h-9 border-border dark:border-purple-200">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border text-popover-foreground">
                <SelectItem value="all" className="hover:bg-accent">All Statuses</SelectItem>
                {statuses.map(status => (
                  <SelectItem key={status} value={status} className="hover:bg-accent">{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Theme-aware Table */}
      <Table>
        <TableHeader>
          {/* Theme-aware TableRow */}
          <TableRow className="border-border">
            <TableHead className="w-[250px]">Title</TableHead>
            <TableHead className="w-[250px]">Description</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredTasks.length > 0 ? (
            filteredTasks.map((task) => (
              // Theme-aware TableRow (including overdue state)
              <TableRow
                key={task.id}
                className={`border-border ${isTaskOverdue(task) ? "bg-red-50 dark:bg-red-950/20 hover:bg-red-100 dark:hover:bg-red-950/30" : "hover:bg-muted/50"}`}
              >
                <TableCell className="font-medium">
                  {task.title}
                  {isTaskOverdue(task) && (
                    // Theme-aware overdue badge
                    <span className="ml-2 text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 font-semibold px-1.5 py-0.5 rounded">OVERDUE</span>
                  )}
                </TableCell>
                <TableCell className="max-w-[250px] truncate">{task.description}</TableCell>
                <TableCell>{getColumnForTask(task.id)}</TableCell>
                <TableCell>
                  {/* Keep priority badge */}
                  <Badge className={`${getPriorityColor(task.priority)} text-white`}>
                    {task.priority}
                  </Badge>
                </TableCell>
                {/* Theme-aware due date cell */}
                <TableCell className={isTaskOverdue(task) ? "text-red-600 dark:text-red-400 font-semibold" : ""}>
                  {task.dueDate ? format(new Date(task.dueDate), 'MMM d, yyyy') : '-'}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    {/* Theme-aware button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-muted-foreground hover:text-foreground"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditClick(task);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    {/* Theme-aware button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentTask(task);
                        handleDelete();
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                {hasActiveFilters
                  ? "No tasks match the current filters."
                  : tasks.length === 0
                    ? "No tasks found. Add a task to get started."
                    : "Loading tasks..."}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Edit Task Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        {/* Theme-aware Dialog Content */}
        <DialogContent className="bg-background border-border text-foreground sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="title" className="text-sm font-medium">
                Title
              </label>
              {/* Theme-aware Input */}
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Task title"
                className="bg-muted border-border"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              {/* Theme-aware Textarea */}
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe your task in detail. What needs to be done? Any specific requirements or notes?"
                rows={3}
                className={`${descriptionError ? "border-red-500" : "border-border"} bg-muted`}
              />
              {descriptionError && (
                <p className="text-sm text-red-500 mt-1">{descriptionError}</p>
              )}
            </div>

            <div className="grid gap-2">
              <label htmlFor="status" className="text-sm font-medium">
                Status
              </label>
              {/* Theme-aware Select */}
              <Select
                value={selectedColumn}
                onValueChange={setSelectedColumn}
              >
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent className="bg-popover border-border text-popover-foreground">
                  {board.columnOrder.map((columnId) => (
                    <SelectItem key={columnId} value={columnId} className="hover:bg-accent">
                      {board.columns[columnId].title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="priority" className="text-sm font-medium">
                Priority
              </label>
              {/* Theme-aware Select */}
              <Select
                value={priority}
                onValueChange={(value) => setPriority(value as 'low' | 'medium' | 'high')}
              >
                <SelectTrigger className="bg-muted border-border">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent className="bg-popover border-border text-popover-foreground">
                  <SelectItem value="low" className="hover:bg-accent">Low</SelectItem>
                  <SelectItem value="medium" className="hover:bg-accent">Medium</SelectItem>
                  <SelectItem value="high" className="hover:bg-accent">High</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="dueDate" className="text-sm font-medium">
                Due Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  {/* Theme-aware Button */}
                  <Button
                    variant="outline"
                    className="justify-start text-left font-normal bg-muted border-border hover:bg-accent"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                {/* Theme-aware Popover Content & Calendar */}
                <PopoverContent className="w-auto p-0 bg-popover border-border text-popover-foreground">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={setDueDate}
                    initialFocus
                    className="bg-popover text-popover-foreground" // Ensure calendar itself is themed
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            {/* Destructive button (styles likely ok) */}
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
            {/* Primary button (styles likely ok) */}
            <Button onClick={handleSave}>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
