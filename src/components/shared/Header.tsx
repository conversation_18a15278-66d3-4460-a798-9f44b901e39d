import { Link, useLocation } from 'react-router-dom';
import { ProductivityLink } from '@/components/productivity/ProductivityLink';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Menu, X } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  children?: ReactNode;
}

const Header = ({ children }: HeaderProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation(); // Get current location
  const [scrolled, setScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Check if current page is productivity
  const isProductivityPage = location.pathname === '/productivity';

  // Check if current page is a landing page
  const isLandingPage = location.pathname.includes('-landing');

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      const target = e.target as Element;
      const isMenuButton = target.closest('.menu-button');
      const isMenuContainer = target.closest('.mobile-menu-container');

      if (isMenuOpen && !isMenuButton && !isMenuContainer) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isMenuOpen]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  return (
    <motion.header
      // Add light mode background styles
      className={`fixed w-full z-50 transition-all duration-300 ${scrolled ? 'py-2 bg-background/80 dark:bg-black/80 backdrop-blur-md shadow-md' : 'py-4 bg-transparent'}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Make overlay dark mode only */}
      <div className={`absolute inset-0 ${scrolled ? 'dark:bg-gradient-to-r from-violet-950/30 via-black/80 to-indigo-950/30' : 'dark:bg-gradient-to-b from-black/50 via-black/20 to-transparent'} backdrop-blur-md dark:block hidden`}></div>

      <div className="container mx-auto px-4 md:px-6 relative">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            {/* Hamburger menu for mobile - moved to the left */}
            <div className="md:hidden mr-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.6 }}
              >
                <button
                  onClick={toggleMenu}
                  // Add light mode styles for mobile menu button
                  className="menu-button flex items-center justify-center w-10 h-10 rounded-full bg-background/80 dark:bg-gradient-to-br dark:from-violet-500/10 dark:to-indigo-500/10 border border-border dark:border-white/10 text-foreground dark:text-white/80 hover:bg-accent dark:hover:bg-white/10 transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95"
                  aria-label={isMenuOpen ? "Close menu" : "Open menu"}
                >
                  <AnimatePresence mode="wait">
                    {isMenuOpen ? (
                      <motion.div
                        key="close"
                        initial={{ rotate: -90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: 90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <X className="w-5 h-5" />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="menu"
                        initial={{ rotate: 90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: -90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Menu className="w-5 h-5" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </button>
              </motion.div>
            </div>

            {isProductivityPage ? (
              <ProductivityLink to="/" className="flex items-center space-x-2 group">
                <div className={`relative ${scrolled ? 'w-8 h-8' : 'w-10 h-10'} transition-all duration-300`}>
                  <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
                  <img
                    src="/icon-192x192.png"
                    alt="IsotopeAI Logo"
                    // Add light mode border
                    className="w-full h-full rounded-full border border-border dark:border-white/10 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                {/* Add light mode text color */}
                <span className={`font-bold ${scrolled ? 'text-lg sm:text-xl' : 'text-xl sm:text-2xl'} text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-white dark:to-white/70 transition-all duration-300`}>
                  IsotopeAI
                </span>
              </ProductivityLink>
            ) : (
              <Link to="/" className="flex items-center space-x-2 group">
                <div className={`relative ${scrolled ? 'w-8 h-8' : 'w-10 h-10'} transition-all duration-300`}>
                  <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
                  <img
                    src="/icon-192x192.png"
                    alt="IsotopeAI Logo"
                    // Add light mode border
                    className="w-full h-full rounded-full border border-border dark:border-white/10 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                {/* Add light mode text color */}
                <span className={`font-bold ${scrolled ? 'text-lg sm:text-xl' : 'text-xl sm:text-2xl'} text-foreground dark:bg-clip-text dark:text-transparent dark:bg-gradient-to-r dark:from-white dark:to-white/70 transition-all duration-300`}>
                  IsotopeAI
                </span>
              </Link>
            )}
          </div>

          {/* Navigation - Desktop */}
          <div className="hidden md:flex items-center space-x-8">
            <motion.div className="flex space-x-1">
              {[
                { name: 'AI Assistant', path: isLandingPage ? '/ai-landing' : '/ai' },
                { name: 'Study Groups', path: isLandingPage ? '/groups-landing' : '/groups' },
                { name: 'Productivity', path: isLandingPage ? '/productivity-landing' : '/productivity' },
                { name: 'Analytics', path: isLandingPage ? '/' : '/analytics' },
                { name: 'Tasks', path: isLandingPage ? '/tasks-landing' : '/tasks' },
                { name: 'Mock Tests', path: isLandingPage ? '/mocktest-landing' : '/mock-tests' },
                { name: 'About Us', path: '/about-us' },
                { name: 'Contact', path: '/contact-us' },
              ].map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                >
                  {isProductivityPage ? (
                    <ProductivityLink
                      to={item.path}
                      className="text-muted-foreground dark:text-white/70 hover:text-foreground dark:hover:text-white font-medium text-sm transition-colors duration-200 px-4 py-2 rounded-full hover:bg-accent dark:hover:bg-white/5"
                    >
                      {item.name}
                    </ProductivityLink>
                  ) : (
                    <Link
                      to={item.path}
                      className="text-muted-foreground dark:text-white/70 hover:text-foreground dark:hover:text-white font-medium text-sm transition-colors duration-200 px-4 py-2 rounded-full hover:bg-accent dark:hover:bg-white/5"
                    >
                      {item.name}
                    </Link>
                  )}
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Auth Buttons and Additional Content */}
          <div className="flex items-center space-x-4">
            {children}
            {user ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.6 }}
              >

              </motion.div>
            ) : null}
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.3 }}
            // Add light mode styles for mobile menu container
            className="fixed top-[60px] right-4 left-4 bg-background/95 dark:bg-black/90 backdrop-blur-xl rounded-xl border border-border dark:border-white/10 shadow-xl overflow-hidden mobile-menu-container md:hidden z-[100]"
          >
            <motion.div
              className="flex flex-col p-4 space-y-2"
              initial="hidden"
              animate="visible"
              variants={{
                hidden: {},
                visible: {
                  transition: {
                    staggerChildren: 0.07
                  }
                }
              }}
            >
              {[
                { name: 'AI Assistant', path: isLandingPage ? '/ai-landing' : '/ai' },
                { name: 'Study Groups', path: isLandingPage ? '/groups-landing' : '/groups' },
                { name: 'Productivity', path: isLandingPage ? '/productivity-landing' : '/productivity' },
                { name: 'Analytics', path: isLandingPage ? '/' : '/analytics' },
                { name: 'Tasks', path: isLandingPage ? '/tasks-landing' : '/tasks' },
                { name: 'Mock Tests', path: isLandingPage ? '/mocktest-landing' : '/mock-tests' },
                { name: 'About Us', path: '/about-us' },
                { name: 'Contact', path: '/contact-us' },
              ].map((item, index) => (
                <motion.div
                  key={item.name}
                  variants={{
                    hidden: { opacity: 0, x: -20 },
                    visible: { opacity: 1, x: 0, transition: { duration: 0.2 } }
                  }}
                >
                  {isProductivityPage ? (
                    <ProductivityLink
                      to={item.path}
                      className="text-muted-foreground dark:text-white/80 hover:text-foreground dark:hover:text-white font-medium px-4 py-3 rounded-lg hover:bg-accent dark:hover:bg-white/5 transition-colors flex items-center gap-3 group"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </ProductivityLink>
                  ) : (
                    <Link
                      to={item.path}
                      className="text-muted-foreground dark:text-white/80 hover:text-foreground dark:hover:text-white font-medium px-4 py-3 rounded-lg hover:bg-accent dark:hover:bg-white/5 transition-colors flex items-center gap-3 group"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )}
                </motion.div>
              ))}
              <motion.div
                variants={{
                  hidden: { opacity: 0, x: -20 },
                  visible: { opacity: 1, x: 0, transition: { duration: 0.2 } }
                }}
              >
                <a
                  href="https://isotopeai.featurebase.app/changelog"
                  target="_blank"
                  rel="noopener noreferrer"
                  // Add light mode styles for mobile menu links
                  className="text-muted-foreground dark:text-white/80 hover:text-foreground dark:hover:text-white font-medium px-4 py-3 rounded-lg hover:bg-accent dark:hover:bg-white/5 transition-colors flex items-center gap-3 group"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Changelog
                </a>
              </motion.div>

              {user && (
                <motion.div
                  variants={{
                    hidden: { opacity: 0, y: 10 },
                    visible: { opacity: 1, y: 0, transition: { duration: 0.3, delay: 0.4 } }
                  }}
                >
                  <Button
                    onClick={() => {
                      navigate('/ai');
                      setIsMenuOpen(false);
                    }}
                    className="bg-gradient-to-r from-violet-500 to-indigo-500 hover:from-violet-600 hover:to-indigo-600 text-white py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1.5 shadow-md w-full mt-2 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0"
                  >
                    Go to Dashboard
                    <ArrowRight className="w-3.5 h-3.5" />
                  </Button>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Glowing border at the bottom - adjust for light mode */}
      <div className="h-px w-full bg-gradient-to-r from-transparent via-black/10 dark:via-white/20 to-transparent"></div>
    </motion.header>
  );
};

export default Header;
