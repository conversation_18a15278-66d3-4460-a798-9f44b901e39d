import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
// We need Content type for type annotations in streaming functions
import { SearchBar } from "./SearchBar";
import { ChatMessage } from "./ChatMessage";
import { 
  Loader2, 
  Maximize2, 
  MessageSquare, 
  X, 
  Plus, 
  Trash2, 
  Pin, 
  PinOff, 
  Send,
  Image as ImageIcon,
  MoreHorizontal,
  ChevronDown,
  Sparkles,
  Command,
  Zap,
  RefreshCw,
  FileImage,
  MessagesSquare
} from "lucide-react";
import { useToast } from "./ui/use-toast";
import { Message as BaseMessage, ChatInterfaceProps, Comment } from "../types/chat";
import { convertImageToBase64, uploadImageToCloudinary } from "../utils/imageUtils";
import { DragDropWrapper } from "./DragDropWrapper";
import { ImageEditor } from "./ImageEditor";
// Updated imports for new API utils structure
import {
  handleApiError,
  getSystemInstruction, // Keep original for instruction string
  streamGeminiContent, // Streaming function for Gemini
  GEMINI_FALLBACK_MODEL // Model name for Gemini
} from "../utils/apiUtils";
import { Button } from "./ui/button";
import { deleteComment as deleteFirebaseComment, saveAIChat, getAIChat, toggleChatPinned } from "../utils/firebase";
import { Tools } from "./Tools";
import { ShareChatButton } from "./ShareChatButton";
import { useAuth } from "@/contexts/AuthContext";
import { generateChatId } from "../utils/chatUtils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { SEOShareLinks } from './SEOShareLinks';
import { motion, AnimatePresence } from "framer-motion";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "./ui/dropdown-menu";
import { cn } from "@/lib/utils";

interface Message extends BaseMessage {
  image?: {
    url: string;
    name: string;
    urlKey: string;
    publicId?: string;
    base64?: string;
    mimeType?: string;
  };
}

function generateUUID() {
  // Check if crypto.randomUUID is available (modern browsers)
  if (typeof window !== 'undefined' && window.crypto && window.crypto.randomUUID) {
    return window.crypto.randomUUID();
  }
  
  // Fallback for older browsers
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const ChatInterface = ({ initialQuery, initialImage, initialChatId, currentChatId, isNewChat, onChatInitialized }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const [isVisible, setIsVisible] = useState(false);
  const { user } = useAuth();
  // chatHistory now stores messages in OpenAI format for OpenRouter primarily
  const [chatHistory, setChatHistory] = useState<Array<{
    role: 'user' | 'assistant',
    content: string,
    image?: {
      url: string,
      name: string,
      urlKey?: string,
      publicId?: string,
      base64?: string,
      mimeType?: string
    }
  }>>([]);
  const chatInterfaceRef = useRef<HTMLDivElement>(null);
  const chatId = useRef<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [showImageEditor, setShowImageEditor] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [chatLoaded, setChatLoaded] = useState(false);
  const [lastQueryTime, setLastQueryTime] = useState<number>(0);
  const [shareUrl, setShareUrl] = useState<string>('');
  // New state for streaming
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedResponse, setStreamedResponse] = useState<string>('');
  const [isPinned, setIsPinned] = useState(false);
  const [isCommandMenuOpen, setIsCommandMenuOpen] = useState(false);
  const commandMenuRef = useRef<HTMLDivElement>(null);
  
  // Define quick commands for the command menu
  const quickCommands = [
    { icon: <MessagesSquare className="h-4 w-4" />, label: "Explain in simple terms", command: "Explain this in simple terms:" },
    { icon: <Zap className="h-4 w-4" />, label: "Give me examples", command: "Give me examples of:" },
    { icon: <RefreshCw className="h-4 w-4" />, label: "Summarize the conversation", command: "Summarize our conversation so far" }
  ];

  const navigate = useNavigate();

  // Constants for rate limiting and duplicate detection
  const MIN_QUERY_INTERVAL = 2000; // 2 seconds between queries
  const DUPLICATE_WINDOW = 30000; // 30 seconds window for duplicate detection

  // Load existing chat if initialChatId is provided, or set up a new chat if isNewChat is true
  useEffect(() => {
    if (isNewChat && initialChatId && messages.length === 0 && (initialQuery || initialImage)) {
      console.log("Setting up new chat directly from props:", initialChatId);
      const queryToUse = initialQuery || (initialImage ? "Image uploaded" : "");
      if (queryToUse) {
        const userMessage: Message = {
          content: queryToUse,
          isUser: true,
        };
        if (initialImage && userMessage.image) { // initialImage is File, userMessage.image might be undefined
           userMessage.image.name = initialImage.name;
        } else if (initialImage) { // If userMessage.image was undefined, create it
            userMessage.image = { name: initialImage.name, url: '', urlKey: generateUUID() };
        }
        setMessages([userMessage]);
      }
      chatId.current = initialChatId;
      setChatLoaded(true);
      setIsLoading(false);
      onChatInitialized?.(); // Call the callback
      return; // Prevent fetching from Firebase for a new chat
    }

    // Proceed to load existing chat from Firebase if it's not a new chat setup
    const loadExistingChat = async () => {
      if (initialChatId && (!chatLoaded || initialChatId !== chatId.current)) {
        try {
          console.log("Loading existing chat from Firebase:", initialChatId);
          setIsLoading(true);
          setMessages([]);
          setChatHistory([]);
          setComments([]);
          setChatLoaded(false);

          interface ChatData {
            id: string;
            messages?: Array<{
              content: string;
              isUser: boolean;
              image?: {
                name: string;
                url: string;
                urlKey?: string;
                publicId?: string;
              };
            }>;
            comments?: Comment[];
            isPinned?: boolean;
          }

          interface ChatData { // Keep this interface local to this function
            id: string;
            messages?: Array<{
              content: string;
              isUser: boolean;
              image?: {
                name: string;
                url: string;
                urlKey?: string;
                publicId?: string;
              };
            }>;
            comments?: Comment[];
            isPinned?: boolean;
          }

          const chatData = await getAIChat(initialChatId) as ChatData;

          if (chatData && chatData.messages) {
            console.log("Chat data loaded:", {
              messageCount: chatData.messages.length || 0
            });

            // Set chat ID
            chatId.current = initialChatId;

            // Set messages
            setMessages(chatData.messages.map((msg) => ({
              content: msg.content,
              isUser: msg.isUser,
              image: msg.image ? {
                name: msg.image.name,
                url: msg.image.url || '',
                urlKey: msg.image.urlKey || generateUUID(),
                publicId: msg.image.publicId // Ensure publicId is loaded from Firebase
              } : undefined
            })));

            // Convert loaded messages to OpenAI format for chatHistory state
            // We'll load base64 data for images when needed in handleSendMessage
            const initialChatHistory = chatData.messages.map((msg) => ({
              role: msg.isUser ? "user" as const : "assistant" as const,
              content: msg.content,
              image: msg.image ? {
                name: msg.image.name,
                url: msg.image.url || '',
                urlKey: msg.image.urlKey || generateUUID(),
                publicId: msg.image.publicId,
                // We'll load base64 data when needed
              } : undefined
            }));

            setChatHistory(initialChatHistory);

            // If there are images in the chat history, preload base64 data for the first one
            // This helps with immediate follow-up questions about the image
            const imagesInHistory = initialChatHistory.filter((msg: { role: string, content: string, image?: any }) => msg.image?.url);
            if (imagesInHistory.length > 0) {
              // Preload the most recent image's base64 data
              const mostRecentImageMsg = imagesInHistory[imagesInHistory.length - 1];
              if (mostRecentImageMsg.image?.url) {
                console.log("Preloading base64 data for most recent image in chat history");
                loadImageBase64(mostRecentImageMsg.image.url)
                  .then(imageData => {
                    if (imageData) {
                      // Update the chat history with the loaded base64 data
                      setChatHistory(prevHistory => {
                        return prevHistory.map(msg => {
                          if (msg.image?.url === mostRecentImageMsg.image?.url) {
                            return {
                              ...msg,
                              image: {
                                ...msg.image,
                                base64: imageData.base64,
                                mimeType: imageData.mimeType
                              }
                            };
                          }
                          return msg;
                        });
                      });
                    }
                  })
                  .catch(error => {
                    console.error("Error preloading base64 data for image:", error);
                  });
              }
            }

            // Set comments
            if (chatData.comments) {
              setComments(chatData.comments);
            }

            // Set the chat as loaded to prevent duplicate processing
            setTimeout(() => {
              setChatLoaded(true);
              setIsLoading(false);
            }, 200);

            // Update pin status
            setIsPinned(!!chatData.isPinned);
          } else {
            console.error("Invalid chat data format", chatData);
            setIsLoading(false);
            toast({
              title: "Error",
              description: "Failed to load chat data. Invalid format.",
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error('Error loading chat:', error);
          toast({
            title: "Error",
            description: "Failed to load chat history. Please try again.",
            variant: "destructive",
          });
          setIsLoading(false);
        }
      }
    };

    if (!isNewChat) { // Only call loadExistingChat if not a new chat being set up
      loadExistingChat();
    }
  }, [initialChatId, isNewChat, initialQuery, initialImage, chatLoaded, toast]); // Added toast to dependencies

  // Special effect to ensure AI response is triggered when messages are loaded
  useEffect(() => {
    // Only trigger AI response if:
    // 1. We have at least one message
    // 2. The last message is from the user
    // 3. We're not currently loading
    // 4. The chat is fully loaded
    if (messages.length > 0 && messages[messages.length - 1].isUser && !isLoading && chatLoaded) {
      console.log("Auto-triggering AI response for user message");

      // Check if this is the first message or if there's no AI response after the last user message
      const isFirstMessage = messages.length === 1;
      const needsResponse = isFirstMessage ||
        (messages.length >= 2 && messages[messages.length - 2].isUser);

      if (needsResponse) {
        console.log("Message needs response. First message:", isFirstMessage);

        // Check timeout before auto-triggering
        const currentTime = Date.now();
        const timeSinceLastQuery = currentTime - lastQueryTime;
        const timeoutDuration = 10000; // 10 seconds in milliseconds

        if (lastQueryTime > 0 && timeSinceLastQuery < timeoutDuration) {
          const remainingTime = Math.ceil((timeoutDuration - timeSinceLastQuery) / 1000);
          console.log(`Auto-trigger waiting ${remainingTime} seconds before sending another query`);
          return;
        }

        const lastUserMessage = messages[messages.length - 1];
        const query = lastUserMessage.content;

        // Skip auto-triggering if this message has an image
        // Images are handled explicitly by the caller functions
        if (lastUserMessage.image) {
          console.log("Skipping auto-trigger for message with image - already being processed");
          return;
        }

        console.log("Auto-trigger processing text-only message:", query);

        // Small delay to ensure UI is updated
        setTimeout(() => {
          // Only process text queries here - image queries are handled separately
          handleSendMessage(query);
        }, 200);
      } else {
        console.log("Message doesn't need response - already has an AI reply");
      }
    }
  }, [messages, chatLoaded, isLoading, lastQueryTime]);

  // Set current chatId from prop and load initial message if present
  useEffect(() => {
    // Initialize visibility with a small delay
    const visibilityTimeout = setTimeout(() => setIsVisible(true), 100);

    // Also ensure we eventually display interface even if loading takes time
    const fallbackTimeout = setTimeout(() => {
      setIsLoading(false);
      setIsVisible(true);
    }, 1500); // Reduced from 3000ms to 1500ms for better responsiveness

    // If there's no initialChatId or currentChatId, this is a brand new chat
    // Clear any session storage from previous chats to avoid message persistence
    if (!initialChatId && !currentChatId) {
      console.log("Starting new chat - clearing session storage data");
      sessionStorage.removeItem('messageInitiallyProcessed');
      sessionStorage.removeItem('chatQuery');
      sessionStorage.removeItem('hasImage');

      // Also reset component state
      chatId.current = null;
      setMessages([]);
      setChatHistory([]);
      setComments([]);
      setChatLoaded(false);

      // No need to clean up local image URLs as we're not using them anymore
    }

    // Check for session storage flags that may have been set by parent component
    const messageInitiallyProcessed = sessionStorage.getItem('messageInitiallyProcessed') === 'true';
    const savedQuery = sessionStorage.getItem('chatQuery');
    const hasImage = sessionStorage.getItem('hasImage') === 'true';

    // Clear the flags now that we've read them
    if (messageInitiallyProcessed) {
      sessionStorage.removeItem('messageInitiallyProcessed');
    }
    if (savedQuery) {
      sessionStorage.removeItem('chatQuery');
    }
    if (hasImage) {
      sessionStorage.removeItem('hasImage');
    }

    // If we have a currentChatId from props, use it
    if (currentChatId) {
      console.log("Setting current chat ID from props:", currentChatId);

      // Only update if it's different from current chatId
      if (chatId.current !== currentChatId) {
        chatId.current = currentChatId;

        // If we have an initial query (either from props or session storage)
        const queryToUse = initialQuery || savedQuery;

        if (queryToUse) {
          console.log("Adding initial query to messages:", queryToUse);

          // Need to handle initial image properly
          if (initialImage) {
            // Upload to Cloudinary immediately instead of creating a local URL
            setIsLoading(true);
            uploadImageToCloudinary(initialImage, {
              folder: "chat_images",
              maxFileSize: 5 * 1024 * 1024, // 5MB limit
            }).then(cloudinaryResponse => {
              // Create user message with Cloudinary URL
              const userMessage: Message = {
                content: queryToUse,
                isUser: true,
                image: {
                  name: initialImage.name,
                  url: cloudinaryResponse.secure_url,
                  urlKey: generateUUID(),
                  publicId: cloudinaryResponse.public_id
                }
              };

              // Add user message to the chat only if no messages exist yet
              if (messages.length === 0) {
                setMessages([userMessage]);
              }

              // For image messages, directly trigger with the original File object
              // We need to manually process since auto-trigger can't access the file
              // This should still run even if messages were already set, to trigger AI.
              handleSendMessage(queryToUse, initialImage, true).then(() => {
                // Now set states after ensuring image processing has started
                setChatLoaded(true);
                setIsLoading(false);
              });
            }).catch(error => {
              console.error("Error uploading initial image to Cloudinary:", error);
              // Add message without image if Cloudinary upload fails and messages are empty
              const userMessage: Message = {
                content: queryToUse,
                isUser: true
              };
              if (messages.length === 0) {
                setMessages([userMessage]);
              }

              // For text-only messages, we can use the auto-triggering
              // This should still run to trigger AI.
              setChatLoaded(true);
              setIsLoading(false);

              // Show error toast for image upload failure
              toast({
                title: "Image Upload Error",
                description: "Failed to upload image. Your message has been sent without the image.",
                variant: "destructive",
              });
            });
          } else {
            // No image, just add the message if messages are empty
            const userMessage: Message = {
              content: queryToUse,
              isUser: true
            };
            if (messages.length === 0) {
              setMessages([userMessage]);
            }

            // Set chat as loaded to allow AI processing to happen
            // For text-only messages, we can use the auto-triggering
            // This should still run to trigger AI.
            setChatLoaded(true);
          }
        } // end if (queryToUse)
      } // end if (chatId.current !== currentChatId)
    } // end if (currentChatId)
    // Only process the initial message if we don't have a current chat ID
    // This block handles the scenario where the chat is initiated from ChatInterface directly (e.g. fullscreen mode without an ID)
    // and is NOT a new chat being set up via initialChatId + isNewChat
    else if (((initialQuery || savedQuery) || initialImage || hasImage) && !chatId.current && !initialChatId && !isNewChat) {
      if (savedQuery) {
        handleInitialMessage(savedQuery, initialImage);
      } else {
        handleInitialMessage(initialQuery, initialImage);
      }
    }

    const handlePaste = async (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;
      for (const item of Array.from(items)) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            setSelectedImage(file);
            setShowImageEditor(true);
            break;
          }
        }
      }
    };
    document.addEventListener('paste', handlePaste);

    // Clean up timeouts and event listeners
    return () => {
      clearTimeout(visibilityTimeout);
      clearTimeout(fallbackTimeout);
      document.removeEventListener('paste', handlePaste);
    };
  }, [currentChatId, initialQuery, initialImage]);

  // Save messages to Firebase whenever they change
  useEffect(() => {
    const saveChatToFirebase = async () => {
      if (messages.length > 0 && user && chatId.current) {
        try {
          // Only save to Firebase if there are messages
          await saveAIChat(chatId.current, {
            userId: user.uid,
            messages: messages.map(msg => {
              const mappedMsg: any = {
                content: msg.content,
                isUser: msg.isUser,
              };

              // Only add image property if there is an image
              if (msg.image && msg.image.name) {
                mappedMsg.image = {
                  name: msg.image.name,
                  url: msg.image.url, // Save Cloudinary URL
                  urlKey: msg.image.urlKey || generateUUID(),
                  publicId: msg.image.publicId, // Save Cloudinary public ID
                  // Don't save base64 data to Firebase as it's too large
                  // We'll convert the image URL to base64 when needed for API calls
                };
              }

              return mappedMsg;
            }),
            comments,
            timestamp: Date.now(),
            preview: messages[0]?.content.substring(0, 100) || "Chat",
            isPinned: isPinned
          }, true); // Always save as public
        } catch (error) {
          console.error('Error saving chat to Firebase:', error);
        }
      }
    };

    // Add a small delay to avoid too many writes during active conversation
    const timeoutId = setTimeout(saveChatToFirebase, 2000);
    return () => clearTimeout(timeoutId);
  }, [messages, comments, user, isPinned]);

  const handleInitialMessage = async (initialQuery: string, initialImage?: File) => {
    if (!initialQuery && !initialImage) {
      window.location.href = '/ai';
      return;
    }

    if (!user) return;

    try {
      // Check timeout
      const currentTime = Date.now();
      const timeSinceLastQuery = currentTime - lastQueryTime;
      const timeoutDuration = 10000;
      if (lastQueryTime > 0 && timeSinceLastQuery < timeoutDuration) {
        const remainingTime = Math.ceil((timeoutDuration - timeSinceLastQuery) / 1000);
        toast({
          title: "Timeout",
          description: `Please wait ${remainingTime} seconds before sending another query.`,
          variant: "default",
        });
        return;
      }

      setIsLoading(true);
      setLastQueryTime(currentTime);

      // Generate new chat ID
      const newChatId = await generateChatId(initialQuery || "New Chat");
      chatId.current = newChatId;

      let imageData: {
        url: string;
        name: string;
        urlKey: string;
        publicId?: string;
      } | undefined;
      if (initialImage) {
        // Validate the image file
        console.log('Initial image details:', {
          name: initialImage.name,
          size: initialImage.size,
          type: initialImage.type
        });

        if (initialImage.size === 0) {
          console.error('Empty initial image file detected');
          toast({
            title: "Error",
            description: "The image file is empty or corrupted. Please try again with a different image.",
            variant: "destructive",
          });
          setIsLoading(false);
          return;
        }

        // Always upload to Cloudinary, no fallback to local URL
        console.log("Uploading initial image to Cloudinary:", initialImage.name);
        try {
          const cloudinaryResponse = await uploadImageToCloudinary(initialImage, {
            folder: "chat_images",
            maxFileSize: 5 * 1024 * 1024, // 5MB limit
          });
          console.log("Cloudinary upload successful:", cloudinaryResponse);

          // Generate a UUID for tracking but don't create a local URL
          const urlKey = generateUUID();

          imageData = {
            url: cloudinaryResponse.secure_url,
            name: initialImage.name,
            urlKey,
            publicId: cloudinaryResponse.public_id
          };
        } catch (cloudinaryError) {
          console.error("Cloudinary upload failed:", cloudinaryError);

          // Don't create a fallback local URL, just notify user and continue without image
          toast({
            title: "Image Upload Error",
            description: "Failed to upload image to cloud storage. Your message will be sent without the image.",
            variant: "destructive",
          });

          // Continue without image
          imageData = undefined;
        }
      }
      // Determine the content for the initial message
      const initialMessageContent = (initialQuery || "").trim() || (initialImage ? "Answer according to the image" : "");

      // Convert image to base64 for API call if present
      let imageBase64: string | null = null;
      if (initialImage && imageData) {
        try {
          imageBase64 = await convertImageToBase64(initialImage);
          console.log("Converted initial image to base64 for API call");
        } catch (error) {
          console.error("Error converting initial image to base64:", error);
        }
      }

      // Create user message with Cloudinary URL if image is present
      const userMessage: Message = {
        content: initialMessageContent, // Use the determined content
        isUser: true,
        image: imageData && imageBase64 ? {
          ...imageData,
          base64: imageBase64,
          mimeType: initialImage?.type
        } : imageData
      };

      // Add to messages state
      setMessages([userMessage]);

      // Save to Firebase with the Cloudinary URL
      await saveAIChat(newChatId, {
        userId: user.uid,
        messages: [userMessage],
        timestamp: Date.now(),
        preview: initialMessageContent.substring(0, 100) || "New Chat", // Use initialMessageContent for preview
        isPinned: false
      }, true);

      if (initialImage && imageData) {
        // For image messages, process immediately with the original File object
        // Pass true to skipAddingUserMessage since we've already added the message
        // Pass the potentially modified initialMessageContent to handleSendMessage
        await handleSendMessage(initialMessageContent, initialImage, true).then(() => {
          setChatLoaded(true);
          setIsLoading(false);
        });
      } else {
        // For text-only messages, let auto-trigger handle it
        setChatLoaded(true);
        setIsLoading(false);
      }

    } catch (error) {
      console.error("Error handling initial message:", error);
      setIsLoading(false);
      toast({
        title: "Error",
        description: "There was a problem processing your question. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Function to load base64 data for an image URL
  const loadImageBase64 = async (imageUrl: string): Promise<{ base64: string, mimeType: string } | null> => {
    try {
      // Create a temporary image element
      const img = new Image();
      img.crossOrigin = 'anonymous';

      // Wait for the image to load
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = imageUrl;
      });

      // Create a canvas and draw the image on it
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Could not get canvas context');

      ctx.drawImage(img, 0, 0);

      // Get the data URL from the canvas
      const dataUrl = canvas.toDataURL();
      const mimeType = dataUrl.split(',')[0].split(':')[1].split(';')[0];

      return { base64: dataUrl, mimeType };
    } catch (error) {
      console.error('Error loading image as base64:', error);
      return null;
    }
  };

  // Function to handle streaming chunks of text
  const handleStreamChunk = (chunk: string) => {
    setStreamedResponse(prev => prev + chunk);
  };

  const handleSendMessage = async (query: string, image?: File, skipAddingUserMessage: boolean = false) => {
    try {
      const currentTime = Date.now();

      console.log(`handleSendMessage called with query: "${query}", image: ${image ? image.name : 'none'}, skipAddingUserMessage: ${skipAddingUserMessage}`);

      // Rate limiting check
      if (currentTime - lastQueryTime < MIN_QUERY_INTERVAL) {
        console.log("Rate limiting applied");
        return;
      }

      // Check if this is a duplicate request (same query in last few messages)
      const isDuplicate = messages.some(msg => {
        // Logic to detect duplicates
        if (!msg.isUser) return false;
        const timeDiff = currentTime - (lastQueryTime || 0);
        if (timeDiff > DUPLICATE_WINDOW) return false;

        // For image messages, compare based on content AND image presence
        if (image && msg.image) {
          console.log("Comparing image message with existing message", {
            content: msg.content,
            queryContent: query,
            matches: msg.content === query
          });
          return msg.content === query;
        }

        // For text-only messages, just compare content
        if (!image && !msg.image) {
          const result = msg.content === query;
          if (result) {
            console.log("Text message duplicate detected", {
              existingMessage: msg.content,
              newQuery: query
            });
          }
          return result;
        }

        // One has image, the other doesn't - not a duplicate
        return false;
      });

      // Also check if there's already a matching message as the last one - don't add another
      const isLastMessage = messages.length > 0 &&
        messages[messages.length - 1].isUser &&
        messages[messages.length - 1].content === query &&
        // For image messages, we need to check image presence too
        ((!image && !messages[messages.length - 1].image) ||
         (image && messages[messages.length - 1].image));

      console.log("Message checks:", { isDuplicate, isLastMessage });

      if (isDuplicate) {
        console.log("Skipping duplicate message processing");
        return;
      }

      // Set loading state immediately to prevent multiple calls
      setIsLoading(true);

      // Update last query time
      setLastQueryTime(currentTime);

      const trimmedQuery = query.trim();
      let imageData: {
        url: string;
        name: string;
        urlKey: string;
        publicId?: string;
      } | undefined;

      if (image) {
        // Log image details for debugging
        console.log('Image details:', {
          name: image.name,
          size: image.size,
          type: image.type
        });

        // Check for empty files
        if (image.size === 0) {
          console.error('Attempting to upload an empty image file');
          toast({
            title: "Error",
            description: "The selected image file is empty. Please select a valid image.",
            variant: "destructive",
          });
          setIsLoading(false);
          return;
        }

        // Always upload to Cloudinary with no fallback to local URL
        console.log("Uploading image to Cloudinary:", image.name);
        try {
          const cloudinaryResponse = await uploadImageToCloudinary(image, {
            folder: "chat_images", // Store in a dedicated folder
            maxFileSize: 5 * 1024 * 1024, // 5MB limit
          });

          console.log("Cloudinary upload successful:", cloudinaryResponse);

          // Generate a unique key for tracking
          const urlKey = generateUUID();

          imageData = {
            url: cloudinaryResponse.secure_url, // Use Cloudinary URL
            name: image.name,
            urlKey,
            publicId: cloudinaryResponse.public_id // Save public ID for future reference
          };
        } catch (cloudinaryError) {
          console.error("Cloudinary upload failed:", cloudinaryError);

          // Instead of falling back to local URL, just notify user and continue without image
          toast({
            title: "Image Upload Error",
            description: "Failed to upload image to cloud storage. Your message will be sent without the image.",
            variant: "destructive",
          });

          // Continue without image
          imageData = undefined;
          // If this was a critical error that prevents us from processing the image
          // with Gemini, set image to undefined so we don't try to use it
          image = undefined;
        }
      }

      // Original message content for display
      const messageContent = !trimmedQuery && image ? "Answer according to the image" : trimmedQuery; // Changed default text here

      // Enhanced query with formatting instructions (only sent to API, not displayed)
      const enhancedQuery = !trimmedQuery && image
        ? "Analyze the image."
        : trimmedQuery;

      // If it's not already the last message, add it
      if (!isLastMessage && !skipAddingUserMessage) {
        // Create user message for display WITHOUT the formatting instruction
        const newUserMessage = {
          content: messageContent,
          isUser: true,
          image: imageData
        };

        setMessages(prevMessages => {
          const newMessages = [...prevMessages, newUserMessage];
          if (newMessages.length > 50) {
            return newMessages.slice(newMessages.length - 50);
          }
          return newMessages;
        });

        // Add a small delay to ensure UI updates before proceeding
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log("Processing AI response for query:", query);
      console.log("Preparing AI request...");

      // Get system instruction (it's now just a string)
      const systemInstructionString = getSystemInstruction();

      // Convert image to base64 if present
      let imageBase64: string | null = null;

      if (image) {
        console.log("Converting image to base64 for API call:", image.name);
        try {
          // convertImageToBase64 returns the string with the data URI prefix
          imageBase64 = await convertImageToBase64(image);

          // Validate that we actually got a valid base64 string back
          if (!imageBase64 || !imageBase64.includes('base64')) {
            console.error("Failed to get valid base64 data from image");
            imageBase64 = null;
            // Don't attempt to use the image if conversion failed
            image = undefined;
          }
        } catch (imageError) {
          console.error("Error converting image to base64:", imageError);
          imageBase64 = null;
          // Don't attempt to use the image if conversion failed
          image = undefined;
        }
      }

      // Process chat history to ensure all images have base64 data
      const processedHistory = await Promise.all(chatHistory.map(async (msg) => {
        // If this message has an image but no base64 data, load it
        if (msg.image?.url && !msg.image.base64) {
          console.log("Loading base64 data for image in chat history:", msg.image.name);
          try {
            const imageData = await loadImageBase64(msg.image.url);
            if (imageData) {
              return {
                ...msg,
                image: {
                  ...msg.image,
                  base64: imageData.base64,
                  mimeType: imageData.mimeType
                }
              };
            }
          } catch (error) {
            console.error("Error loading base64 data for image in chat history:", error);
          }
        }
        return msg;
      }));

      // Prepare history in OpenAI format with image data
      const currentUserMessage = {
        role: "user" as const,
        content: enhancedQuery,
        image: imageData ? {
          ...imageData,
          base64: imageBase64,
          mimeType: image?.type
        } : undefined
      };

      const openRouterHistory = [
        ...processedHistory, // Use processed history with base64 data for images
        currentUserMessage // Add current user query with image if present
      ];

      // Update chatHistory state with processed history and current user message
      // This ensures that base64 data for images is preserved for future queries
      setChatHistory(openRouterHistory);

      // Also update the chatHistory state with the processed history (with base64 data)
      // This ensures that if we have multiple follow-up queries, we don't need to reload the base64 data each time
      if (processedHistory.some(msg => msg.image?.base64)) {
        console.log("Updating chatHistory state with processed history containing base64 data");
        setChatHistory(prevHistory => {
          // Replace messages in prevHistory with their processed versions that have base64 data
          const updatedHistory = prevHistory.map(prevMsg => {
            // Find the corresponding processed message
            const processedMsg = processedHistory.find(msg =>
              msg.role === prevMsg.role &&
              msg.content === prevMsg.content &&
              msg.image?.url === prevMsg.image?.url
            );

            // If found and it has base64 data, use it; otherwise keep the original
            return processedMsg?.image?.base64 ? processedMsg : prevMsg;
          });

          // Add the current user message if it's not already the last one
          const lastMsg = updatedHistory[updatedHistory.length - 1];
          if (!lastMsg ||
              lastMsg.role !== currentUserMessage.role ||
              lastMsg.content !== currentUserMessage.content) {
            return [...updatedHistory, currentUserMessage];
          }

          return updatedHistory;
        });
      }

      // Reset streamed response
      setStreamedResponse('');

      // Add an empty AI message that will be updated with streaming content
      const placeholderMessage = { content: '', isUser: false };
      setMessages(prevMessages => [...prevMessages, placeholderMessage]);

      // Start streaming mode
      setIsStreaming(true);

      try {
        // --- Use Gemini directly with Streaming ---
        console.log(`Using Gemini model with streaming: ${GEMINI_FALLBACK_MODEL}`);

        // Use Gemini streaming directly (no OpenRouter attempt)
        await streamGeminiContent(
          openRouterHistory,
          enhancedQuery,
          (chunk) => {
            // Update the streamed response
            handleStreamChunk(chunk);

            // Update the last message with the current accumulated response
            setMessages(prevMessages => {
              const newMessages = [...prevMessages];
              newMessages[newMessages.length - 1].content = newMessages[newMessages.length - 1].content + chunk;
              return newMessages;
            });

            // Scroll to bottom as new content arrives, but only if user hasn't scrolled up
            if (chatInterfaceRef.current) {
              const isAtBottom = chatInterfaceRef.current.scrollHeight - chatInterfaceRef.current.scrollTop - chatInterfaceRef.current.clientHeight < 50;
              if (isAtBottom) {
                chatInterfaceRef.current.scrollTop = chatInterfaceRef.current.scrollHeight;
              }
            }
          },
          imageBase64,
          image?.type
        );

        console.log("Gemini streaming call completed.");

        // Get the final response text - use streamedResponse as the source of truth
        // and apply defensive checks to ensure we have valid content
        const responseText = streamedResponse || "Sorry, I couldn't generate a response. Please try again.";

        // Update history with AI response (in OpenAI format)
        setChatHistory(prevHistory => [...prevHistory, { role: "assistant", content: responseText }]);

      } catch (geminiError: any) {
        console.error("Gemini streaming call failed:", geminiError);
        handleApiError(geminiError, "Gemini"); // Show Gemini specific error toast

        // Throw a generic error to be caught by the outer catch block
        throw new Error("Gemini streaming failed.");
      }

    } catch (error: any) {
      // This catches errors from OpenRouter *and* Gemini fallback failure *and* setup errors
      console.error("Error generating AI response:", error);

      // Update the placeholder message with an error
      setMessages(prevMessages => {
        const newMessages = [...prevMessages];
        if (newMessages.length > 0 && !newMessages[newMessages.length - 1].isUser) {
          // Update the existing AI message with error
          newMessages[newMessages.length - 1].content = "Sorry, I encountered an error processing your request. Please try again later.";
          return newMessages;
        } else {
          // Add a new error message if there's no AI message to update
          return [...prevMessages, {
            content: "Sorry, I encountered an error processing your request. Please try again later.",
            isUser: false
          }];
        }
      });

      // We already showed specific toasts in handleApiError, no need for another generic one here.
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  const handleImageDrop = (file: File) => {
    setSelectedImage(file);
    setShowImageEditor(true);
  };

  const handleAddComment = async (content: string, author: string) => {
    if (!user || !chatId.current) return;

    const newComment: Comment = {
      id: generateUUID(),
      content,
      author,
      authorId: user.uid,
      timestamp: Date.now(),
    };

    // Update local state
    setComments(prevComments => [...prevComments, newComment]);

    // Save to Firebase (the comments will be saved along with the messages in the useEffect)
  };

  const handleAddReply = async (parentId: string, content: string) => {
    if (!user || !chatId.current) return;

    const reply: Comment = {
      id: generateUUID(),
      content,
      author: user.displayName || 'Anonymous',
      authorId: user.uid,
      timestamp: Date.now(),
    };

    // Update local state
    setComments(prevComments => {
      const updateReplies = (comments: Comment[]): Comment[] => {
        return comments.map(comment => {
          if (comment.id === parentId) {
            return {
              ...comment,
              replies: [...(comment.replies || []), reply],
            };
          }
          if (comment.replies) {
            return {
              ...comment,
              replies: updateReplies(comment.replies),
            };
          }
          return comment;
        });
      };

      return updateReplies(prevComments);
    });

    // Changes will be saved to Firebase via the useEffect that watches comments
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!isDeveloperMode) return;

    try {
      // Delete from Firebase
      await deleteFirebaseComment(chatId.current, commentId);

      // Update local state
      const updatedComments = comments.filter(comment => comment.id !== commentId);
      setComments(updatedComments);

      toast({
        title: "Comment Deleted",
        description: "The comment has been successfully deleted.",
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteReply = async (commentId: string, replyId: string) => {
    if (!isDeveloperMode) return;

    try {
      // Update local state first
      const updatedComments = comments.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            replies: (comment.replies || []).filter(reply => reply.id !== replyId)
          };
        }
        return comment;
      });
      setComments(updatedComments);

      // Delete from Firebase
      await deleteFirebaseComment(chatId.current, replyId);

      toast({
        title: "Reply Deleted",
        description: "The reply has been successfully deleted.",
      });
    } catch (error) {
      console.error('Error deleting reply:', error);
      toast({
        title: "Error",
        description: "Failed to delete reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const isDeveloperMode = sessionStorage.getItem("isDeveloperMode") === "true";
  const username = sessionStorage.getItem("userName") || "";

  const handleMaximize = () => {
    navigate(`/fullscreen?chatId=${chatId.current}`);
  };

  const handleCloseChat = () => {
    // Navigate to the default AI page by setting window location directly
    window.location.href = '/ai';
  };

  const handleNewChat = () => {
    // Navigate to the default AI page for a new chat by setting window location directly
    window.location.href = '/ai';
  };

  useEffect(() => {
    const scrollToBottom = () => {
      if (chatInterfaceRef.current) {
        // Use a single, simple scrolling technique
        chatInterfaceRef.current.scrollTop = chatInterfaceRef.current.scrollHeight;
      }
    };

    // Scroll immediately
    scrollToBottom();

    // Add a single delayed scroll to handle any rendering delays
    const timeout = setTimeout(scrollToBottom, 100);

    // Cleanup
    return () => {
      clearTimeout(timeout);
    };
  }, [messages]);

  // Add a check to ensure messages are properly loaded
  useEffect(() => {
    // When messages are successfully loaded, clear loading state
    if (messages.length > 0) {
      // Add a small delay to ensure rendering completes
      setTimeout(() => {
        setIsLoading(false);
      }, 200);
    }
  }, [messages]);

  // Handle when a share link is generated
  const handleShareGenerated = (url: string) => {
    setShareUrl(url);
  };

  // Toggle pin status for the current chat
  const togglePinChat = async () => {
    if (!chatId.current || !user) return;
    
    try {
      const newPinnedStatus = !isPinned;
      
      // Update in Firebase
      await toggleChatPinned(chatId.current, newPinnedStatus);
      
      // Update local state
      setIsPinned(newPinnedStatus);
      
      toast({
        title: newPinnedStatus ? "Chat Pinned" : "Chat Unpinned",
        description: newPinnedStatus 
          ? "This chat will appear in the pinned section" 
          : "This chat has been removed from pinned section",
      });
    } catch (error) {
      console.error('Error toggling pin status:', error);
      toast({
        title: "Error",
        description: "Failed to update pin status for this chat.",
        variant: "destructive",
      });
    }
  };

  // Close command menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (commandMenuRef.current && !commandMenuRef.current.contains(event.target as Node)) {
        setIsCommandMenuOpen(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Execute a command from the command menu
  const executeCommand = (command: string) => {
    if (command) {
      handleSendMessage(command);
    }
    setIsCommandMenuOpen(false);
  };

  return (
    <>
      {/* SEO Share links */}
      {shareUrl && chatId.current && (
        <SEOShareLinks
          chatId={chatId.current}
          shareUrl={shareUrl}
          chatData={{
            messages,
            username: user?.displayName || 'User',
          }}
        />
      )}

      <div 
        className={cn(
          "transition-all duration-500 ease-out",
          isVisible ? "opacity-100 scale-100" : "opacity-0 scale-95",
          "w-full max-w-5xl mx-auto rounded-2xl overflow-hidden px-2 sm:px-4"
        )}
      >
        <motion.div 
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4, ease: [0.25, 0.1, 0.25, 1.0] }}
          className="flex flex-col bg-card/90 backdrop-blur-md shadow-xl border border-border/30 rounded-2xl overflow-hidden"
          style={{ 
            height: "calc(90vh - 20px)", 
            maxHeight: "1000px" 
          }}
        >
          {/* Modern Header */}
          <header className="px-2 sm:px-4 py-2 sm:py-3 border-b border-border/10 bg-background/50 backdrop-blur-sm flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="flex items-center gap-1 sm:gap-1.5">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleNewChat}
                  className="h-7 w-7 sm:h-8 sm:w-8 rounded-full hover:bg-primary/10 text-muted-foreground"
                >
                  <Plus className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                </Button>
                
                <Tools
                  onToolOutput={(output) => {
                    const newUserMessage = {
                      content: `Used ${output.type} tool`,
                      isUser: true,
                    };
                    const newAIMessage = {
                      content: "```tool-output\n" + JSON.stringify(output, null, 2) + "\n```",
                      isUser: false,
                    };
                    setMessages(prevMessages => [...prevMessages, newUserMessage, newAIMessage]);
                  }}
                />
              </div>
              
              <div className="h-5 w-[1px] bg-border/50 mx-1 hidden sm:block" />
              
              <div className="flex items-center gap-1.5">
                <motion.div 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center"
                >
                  <div className="flex h-6 w-6 sm:h-7 sm:w-7 items-center justify-center rounded-full bg-primary/10">
                    <Sparkles className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary" />
                  </div>
                  <span className="ml-1.5 sm:ml-2 text-xs sm:text-sm font-medium">IsotopeAI</span>
                </motion.div>
              </div>
            </div>
            
            <div className="flex items-center gap-0.5 sm:gap-1">
              {messages.length > 1 && chatId.current && (
                <div className="flex items-center">
                  <ShareChatButton
                    chatId={chatId.current}
                    chatData={{
                      messages,
                      comments,
                      timestamp: Date.now(),
                      preview: messages[0]?.content.substring(0, 100) || "Chat session",
                    }}
                    showTextLabel={false}
                    onShareGenerated={handleShareGenerated}
                  />
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={togglePinChat}
                    className={cn(
                      "h-7 w-7 sm:h-8 sm:w-8 rounded-full hover:bg-primary/10",
                      isPinned ? "text-primary" : "text-muted-foreground"
                    )}
                  >
                    {isPinned ? <PinOff className="h-3.5 w-3.5 sm:h-4 sm:w-4" /> : <Pin className="h-3.5 w-3.5 sm:h-4 sm:w-4" />}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleMaximize}
                    className="h-7 w-7 sm:h-8 sm:w-8 rounded-full hover:bg-primary/10 text-muted-foreground"
                  >
                    <Maximize2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  </Button>
                </div>
              )}
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    className="h-7 w-7 sm:h-8 sm:w-8 rounded-full hover:bg-primary/10 text-muted-foreground"
                  >
                    <MoreHorizontal className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  <DropdownMenuItem className="text-red-500" onClick={handleCloseChat}>
                    Close chat
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>
          
          {/* Messages Area - Updated with responsive design */}
          <div
            ref={chatInterfaceRef}
            className="flex-1 overflow-y-auto px-2 sm:px-3 md:px-6 py-4 space-y-4 sm:space-y-6 bg-background/40 scrollbar-thin scrollbar-thumb-primary/10 hover:scrollbar-thumb-primary/20 scrollbar-track-transparent scroll-smooth"
          >
            <AnimatePresence initial={false}>
              {messages.length === 0 && !isLoading && !initialQuery && !sessionStorage.getItem('chatQuery') ? (
                <motion.div 
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex flex-col items-center justify-center h-full py-8 sm:py-12 text-center"
                >
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-primary/10 flex items-center justify-center mb-3 sm:mb-4">
                    <MessageSquare className="h-6 w-6 sm:h-8 sm:w-8 text-primary/70" />
                  </div>
                  <h3 className="text-lg sm:text-xl font-medium text-foreground mb-1 sm:mb-2">Start a conversation</h3>
                  <p className="text-sm sm:text-base text-muted-foreground max-w-md px-4">
                    Ask any question or upload an image to get started
                  </p>
                </motion.div>
              ) : messages.length === 0 && (initialQuery || sessionStorage.getItem('chatQuery')) ? (
                // Placeholder for initial query
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="py-2"
                >
                  <ChatMessage
                    key="initial-placeholder"
                    content={initialQuery || sessionStorage.getItem('chatQuery') || ""}
                    isUser={true}
                    hideCodeBlocks={false}
                  />
                </motion.div>
              ) : (
                // Regular messages with better spacing and animations
                messages.map((message, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ 
                      duration: 0.3, 
                      delay: index === messages.length - 1 ? 0 : 0,
                      ease: "easeOut" 
                    }}
                    className={cn(
                      "py-1 first:pt-2 last:pb-6",
                      message.isUser ? "pl-2 sm:pl-4 md:pl-8 lg:pl-16" : "pr-2 sm:pr-4 md:pr-8 lg:pr-16"
                    )}
                  >
                    <ChatMessage
                      content={message.content}
                      isUser={message.isUser}
                      image={message.image}
                      hideCodeBlocks={!message.isUser}
                    />
                  </motion.div>
                ))
              )}
            </AnimatePresence>
            
            {/* Loading indicator with animation */}
            {isLoading && (
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="flex justify-center py-6 sm:py-8"
              >
                <div className="relative">
                  <div className="absolute -inset-3 sm:-inset-4 rounded-full bg-primary/5 animate-pulse"></div>
                  <Loader2 className="h-5 w-5 sm:h-6 sm:w-6 animate-spin text-primary" />
                  <div className="absolute -bottom-6 sm:-bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap text-xs sm:text-sm text-muted-foreground/80">
                    Thinking...
                  </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Modern Input Area */}
          <div className="p-2 sm:p-3 md:p-4 lg:p-5 border-t border-border/10 bg-background/60 backdrop-blur-md">
            <DragDropWrapper onImageDrop={handleImageDrop}>
              <div className="relative">
                <div 
                  className={cn(
                    "rounded-xl border border-border/40 bg-background/90 backdrop-blur-sm transition-all duration-300",
                    isStreaming || isLoading ? "border-primary/20 shadow-[0_0_0_1px_rgba(var(--primary)/0.1)]" : "",
                    selectedImage ? "pl-10 sm:pl-12" : "pl-3 sm:pl-4"
                  )}
                >
                  {selectedImage && (
                    <div className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2">
                      <div className="h-5 w-5 sm:h-6 sm:w-6 rounded-full bg-primary/10 flex items-center justify-center">
                        <ImageIcon className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary" />
                      </div>
                    </div>
                  )}
                  
                  <SearchBar
                    onSubmit={handleSendMessage}
                    handleSendMessage={handleSendMessage}
                    isChatVisible={true}
                    selectedImage={selectedImage}
                    setSelectedImage={setSelectedImage}
                    showImageEditor={showImageEditor}
                    setShowImageEditor={setShowImageEditor}
                    customPlaceholder="Message IsotopeAI..."
                    customClassName="py-2.5 sm:py-3 focus-visible:ring-0 border-none shadow-none text-sm sm:text-base"
                  />
                </div>
                
                {/* Keyboard shortcuts hint and command menu button */}
                <div className="mt-1.5 sm:mt-2 px-1 sm:px-2 flex justify-between items-center text-[10px] sm:text-xs text-muted-foreground/60">
                  <span>
                    Press{" "}
                    <kbd className="px-1 py-0.5 bg-muted rounded text-[9px] sm:text-[10px] font-mono">Enter</kbd>
                    {" "}to send
                  </span>
                  <div className="relative">
                    <button 
                      onClick={() => setIsCommandMenuOpen(!isCommandMenuOpen)}
                      className="flex items-center gap-0.5 sm:gap-1 hover:text-muted-foreground transition-colors"
                    >
                      <Command className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-0.5" />
                      <span className="hidden xs:inline">Commands</span>
                      <span className="xs:hidden">Cmd</span>
                      <ChevronDown className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                    </button>
                    
                    {/* Command Menu */}
                    {isCommandMenuOpen && (
                      <div 
                        ref={commandMenuRef}
                        className="absolute bottom-full right-0 mb-2 w-56 sm:w-64 bg-card rounded-lg shadow-lg border border-border/50 overflow-hidden z-10"
                      >
                        <div className="p-2 border-b border-border/10 text-xs font-medium">
                          Quick commands
                        </div>
                        <div className="p-1.5">
                          {quickCommands.map((cmd, index) => (
                            <button
                              key={index}
                              className="w-full text-left flex items-center gap-2.5 px-3 py-1.5 rounded-md text-xs hover:bg-primary/5 transition-colors"
                              onClick={() => executeCommand(cmd.command)}
                            >
                              <span className="text-primary/80 flex-shrink-0">
                                {cmd.icon}
                              </span>
                              <span>{cmd.label}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </DragDropWrapper>
          </div>
        </motion.div>
      </div>
    </>
  );
};
