import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useUserStore } from '../stores/userStore';

export const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { authProvider } = useUserStore();
  const location = useLocation();

  // Use the appropriate auth hook based on the provider
  let user, loading;

  if (authProvider === 'supabase') {
    try {
      const supabaseAuth = useSupabaseAuth();
      user = supabaseAuth.user;
      loading = supabaseAuth.loading;
    } catch {
      // Fallback to Firebase auth if Supabase context is not available
      const firebaseAuth = useAuth();
      user = firebaseAuth.user;
      loading = firebaseAuth.loading;
    }
  } else {
    const firebaseAuth = useAuth();
    user = firebaseAuth.user;
    loading = firebaseAuth.loading;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  if (!user) {
    // Redirect to login page, but save where they were trying to go
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  return <>{children}</>;
}; 