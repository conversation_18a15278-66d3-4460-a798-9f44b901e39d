import React, { useState, useEffect, useRef, useMemo } from "react";
import { useNavigate, Link } from "react-router-dom";
import { GoogleGenerativeAI, Content, HarmBlockThreshold, HarmCategory } from "@google/generative-ai";
import { SearchBar } from "./SearchBar";
import { ChatMessage } from "./ChatMessage";
import { Loader2, Maximize2, Settings, Menu, X, Plus, MessageSquare, Calendar, ArrowUpDown, Pin, PinOff, Star } from "lucide-react";
import { useToast } from "./ui/use-toast";
import { Message, ChatInterfaceProps, Comment, ChatHistory } from "../types/chat";
import { convertImageToBase64, uploadImageToCloudinary } from "../utils/imageUtils";
import { DragDropWrapper } from "./DragDropWrapper";
import { ImageEditor } from "./ImageEditor";
import { handleApiError, GEMINI_MODEL, getSystemInstruction } from "../utils/apiUtils";
import { getAuth } from "firebase/auth";
import { Tools } from "./Tools";
import { cn } from "../lib/utils";
import { Button } from "./ui/button";
import { ShareChatButton } from "./ShareChatButton";
import { useAuth } from "@/contexts/AuthContext";
import { saveAIChat, getAIChat, getUserChatHistory } from "@/utils/firebase";
import { generateChatId } from "../utils/chatUtils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { SEOShareLinks } from './SEOShareLinks';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "./ui/dropdown-menu";
import { ChatHistorySection } from "./ChatHistorySection";

function generateUUID() {
  // Check if crypto.randomUUID is available (modern browsers)
  if (typeof window !== 'undefined' && window.crypto && window.crypto.randomUUID) {
    return window.crypto.randomUUID();
  }
  
  // Fallback for older browsers
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const ChatInterface = ({ initialQuery, initialImage, initialChatId }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const [isVisible, setIsVisible] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [chatContent, setChatContent] = useState<Content[]>([]);
  const chatInterfaceRef = useRef<HTMLDivElement>(null);
  const [currentChatId, setCurrentChatId] = useState<string>(initialChatId || "");
  const { user } = useAuth();
  const chatId = useRef(currentChatId);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [showImageEditor, setShowImageEditor] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [loadingChatHistory, setLoadingChatHistory] = useState(false);
  const [lastQueryTime, setLastQueryTime] = useState<number>(0);
  const [shareUrl, setShareUrl] = useState<string>('');
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "name">("newest");
  const [isPinned, setIsPinned] = useState(false);

  const navigate = useNavigate();

  const handleMaximize = () => {
    navigate(`/fullscreen?chatId=${chatId.current}`);
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    const scrollToBottom = () => {
      if (chatInterfaceRef.current) {
        // Use a single, simple scrolling technique
        chatInterfaceRef.current.scrollTop = chatInterfaceRef.current.scrollHeight;
      }
    };

    // Scroll immediately
    scrollToBottom();

    // Add a single delayed scroll to handle any rendering delays
    const timeout = setTimeout(scrollToBottom, 100);

    // Cleanup
    return () => {
      clearTimeout(timeout);
    };
  }, [messages]);

  // Load chat history from Firebase
  useEffect(() => {
    const loadUserChatHistory = async () => {
      if (!user) return;

      setLoadingChatHistory(true);
      try {
        const history = await getUserChatHistory(user.uid);
        // Make sure history items match the ChatHistory type
        const formattedHistory: ChatHistory[] = history.map((chat: any) => ({
          id: chat.id,
          timestamp: chat.timestamp || chat.createdAt?.toMillis() || Date.now(),
          messages: chat.messages || [],
          preview: chat.preview || (chat.messages?.[0]?.content?.substring(0, 100) + "...") || "Chat",
          comments: chat.comments || [],
          isPinned: chat.isPinned || false,
          isStarred: chat.isStarred || false
        }));

        // Sort chat history by timestamp in descending order (newest first)
        formattedHistory.sort((a, b) => b.timestamp - a.timestamp);

        setChatHistory(formattedHistory);
      } catch (error) {
        console.error('Error loading chat history:', error);
        toast({
          title: "Error",
          description: "Failed to load your chat history.",
          variant: "destructive",
        });
      } finally {
        setLoadingChatHistory(false);
      }
    };

    loadUserChatHistory();
  }, [user]);

  // Load chat data from Firebase
  useEffect(() => {
    const loadChatData = async () => {
      setTimeout(() => setIsVisible(true), 100);

      console.log('Loading chat data with initialChatId:', initialChatId);

      // If we have an initial query, handle it as a new message
      if (initialQuery || initialImage) {
        console.log('Have initialQuery/image, handling as new message');
        handleInitialMessage(initialQuery, initialImage);
        return; // Don't try to load existing chat if we're starting a new one
      }

      // If we have an initialChatId (from URL), try to load from Firebase
      if (initialChatId && messages.length === 0) {
        console.log('Attempting to load chat from Firebase:', initialChatId);
        try {
          setIsLoading(true);
          const firebaseChat = await getAIChat(initialChatId);

          if (firebaseChat && 'messages' in firebaseChat && Array.isArray(firebaseChat.messages) && firebaseChat.messages.length > 0) {
            console.log('Successfully loaded chat from Firebase:', initialChatId);
            // We found messages on Firebase, load them
            // Ensure image data is properly preserved
            setMessages(firebaseChat.messages.map((msg: any) => ({
              content: msg.content,
              isUser: msg.isUser,
              image: msg.image ? {
                name: msg.image.name,
                url: msg.image.url || '',
                urlKey: msg.image.urlKey || generateUUID(),
                publicId: msg.image.publicId
              } : undefined
            })));

            setChatContent(firebaseChat.messages.map((msg: any) => ({
              role: msg.isUser ? "user" : "model",
              parts: [{ text: msg.content }]
            })));

            // Set comments if they exist
            if ('comments' in firebaseChat && Array.isArray(firebaseChat.comments)) {
              setComments(firebaseChat.comments);
            }
            
            // Set pinned status if it exists
            if ('isPinned' in firebaseChat) {
              setIsPinned(!!firebaseChat.isPinned);
            } else {
              setIsPinned(false);
            }
          }
        } catch (error) {
          console.error('Error loading chat from Firebase:', error);
          toast({
            title: "Error",
            description: "Failed to load chat. It may have been deleted.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadChatData();

    const handlePaste = async (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;
      for (const item of Array.from(items)) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            setSelectedImage(file);
            setShowImageEditor(true);
            break;
          }
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [initialChatId, initialQuery, initialImage]);

  // Save messages to Firebase whenever they change
  useEffect(() => {
    const saveToFirebase = async () => {
      if (!user || !currentChatId || messages.length < 1) return;

      try {
        // Save to Firebase
        await saveAIChat(currentChatId, {
          userId: user.uid,
          messages: messages.map(msg => ({
            content: msg.content,
            isUser: msg.isUser,
            // Save all image data if there was an image
            image: msg.image ? {
              name: msg.image.name,
              url: msg.image.url, // Save Cloudinary URL
              urlKey: msg.image.urlKey || generateUUID(),
              publicId: msg.image.publicId // Save Cloudinary public ID
            } : undefined
          })),
          updatedAt: Date.now(), // This will be converted to Timestamp in saveAIChat
          preview: messages[0]?.content.substring(0, 100) || "Chat",
          comments: comments,
          isPinned: false,
          isStarred: false
        }, true); // Always save as public
      } catch (error) {
        console.error('Error saving chat to Firebase:', error);
      }
    };

    // Add a small delay to avoid too many writes during active conversation
    const timeoutId = setTimeout(saveToFirebase, 2000);
    return () => clearTimeout(timeoutId);
  }, [messages, user, currentChatId, comments]);

  const handleInitialMessage = async (initialQuery: string, initialImage?: File) => {
    try {
      // If no query or image, redirect to /ai page
      if (!initialQuery && !initialImage) {
        window.location.href = '/ai';
        return;
      }

      // Check if 10 seconds have passed since the last query
      const currentTime = Date.now();
      const timeSinceLastQuery = currentTime - lastQueryTime;
      const timeoutDuration = 10000; // 10 seconds in milliseconds

      if (lastQueryTime > 0 && timeSinceLastQuery < timeoutDuration) {
        const remainingTime = Math.ceil((timeoutDuration - timeSinceLastQuery) / 1000);
        console.log(`Please wait ${remainingTime} seconds before sending another query`);

        toast({
          title: "Timeout",
          description: `Please wait ${remainingTime} seconds before sending another query.`,
          variant: "default",
        });

        return;
      }

      // Update last query time
      setLastQueryTime(currentTime);

      // Generate a chat ID based on the initial query if not already set
      if (!currentChatId) {
        // Create a deterministic chat ID based on the question content
        const newChatId = await generateChatId(initialQuery);

        // Set the chat ID
        setCurrentChatId(newChatId);
        console.log('Generated new chat ID:', newChatId);
      }

      let imageData;
      if (initialImage) {
        // Upload image to Cloudinary
        console.log("Uploading initial image to Cloudinary:", initialImage.name);
        try {
          const cloudinaryResponse = await uploadImageToCloudinary(initialImage, {
            folder: "chat_images",
            maxFileSize: 5 * 1024 * 1024, // 5MB limit
          });
          console.log("Cloudinary upload successful:", cloudinaryResponse);

          // Generate a UUID for tracking
          const urlKey = generateUUID();

          imageData = {
            url: cloudinaryResponse.secure_url,
            name: initialImage.name,
            urlKey,
            publicId: cloudinaryResponse.public_id
          };
        } catch (cloudinaryError) {
          console.error("Cloudinary upload failed:", cloudinaryError);

          toast({
            title: "Image Upload Error",
            description: "Failed to upload image to cloud storage. Your message will be sent without the image.",
            variant: "destructive",
          });

          // Continue without image
          imageData = undefined;
        }
      }

      // Initial user message
      const userMessage = {
        content: initialQuery,
        isUser: true,
        image: imageData
      };

      // Add user message to the chat
      setMessages([userMessage]);

      // Process user query
      await handleSendMessage(initialQuery, initialImage, true);
    } catch (error) {
      console.error('Error handling initial message:', error);
      toast({
        title: "Error",
        description: "Failed to process your question. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleNewChat = () => {
    setCurrentChatId("");
    setMessages([]);
    setChatContent([]);
    setSidebarOpen(false);
    window.location.href = '/ai';
    toast({
      title: "New Chat Started",
      description: "You can begin a new conversation.",
    });
  };

  const loadChat = async (chatId: string) => {
    try {
      setIsLoading(true);
      const chatData = await getAIChat(chatId);

      if (chatData && 'messages' in chatData && Array.isArray(chatData.messages)) {
        setCurrentChatId(chatId);
        setMessages(chatData.messages);
        setChatContent(chatData.messages.map((msg: Message) => ({
          role: msg.isUser ? "user" : "model",
          parts: [{ text: msg.content }]
        })));
        
        // Use proper type checking for comments
        if ('comments' in chatData && Array.isArray(chatData.comments)) {
          setComments(chatData.comments);
        } else {
          setComments([]);
        }
        
        setSidebarOpen(false);

        toast({
          title: "Previous Chat Loaded",
          description: "You can continue your conversation from where you left off.",
        });
      }
    } catch (error) {
      console.error('Error loading chat:', error);
      toast({
        title: "Error",
        description: "Failed to load chat. It may have been deleted.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSendMessage = async (query: string, image?: File, skipAddingUserMessage: boolean = false) => {
    try {
      // Check if 10 seconds have passed since the last query
      const currentTime = Date.now();
      const timeSinceLastQuery = currentTime - lastQueryTime;
      const timeoutDuration = 10000; // 10 seconds in milliseconds

      if (lastQueryTime > 0 && timeSinceLastQuery < timeoutDuration) {
        const remainingTime = Math.ceil((timeoutDuration - timeSinceLastQuery) / 1000);
        console.log(`Please wait ${remainingTime} seconds before sending another query`);

        toast({
          title: "Timeout",
          description: `Please wait ${remainingTime} seconds before sending another query.`,
          variant: "default",
        });

        return;
      }

      setIsLoading(true);

      // Update last query time
      setLastQueryTime(currentTime);

      const trimmedQuery = query.trim();
      let imageData;

      // Upload image to Cloudinary if provided
      if (image) {
        // Log image details for debugging
        console.log('Image details:', {
          name: image.name,
          size: image.size,
          type: image.type
        });

        // Check for empty files
        if (image.size === 0) {
          console.error('Attempting to upload an empty image file');
          toast({
            title: "Error",
            description: "The selected image file is empty. Please select a valid image.",
            variant: "destructive",
          });
          setIsLoading(false);
          return;
        }

        // Upload to Cloudinary with no fallback to local URL
        console.log("Uploading image to Cloudinary:", image.name);
        try {
          const cloudinaryResponse = await uploadImageToCloudinary(image, {
            folder: "chat_images", // Store in a dedicated folder
            maxFileSize: 5 * 1024 * 1024, // 5MB limit
          });

          console.log("Cloudinary upload successful:", cloudinaryResponse);

          // Generate a unique key for tracking
          const urlKey = generateUUID();

          imageData = {
            url: cloudinaryResponse.secure_url, // Use Cloudinary URL
            name: image.name,
            urlKey,
            publicId: cloudinaryResponse.public_id // Save public ID for future reference
          };
        } catch (cloudinaryError) {
          console.error("Cloudinary upload failed:", cloudinaryError);

          // Notify user of the error but continue without image
          toast({
            title: "Image Upload Error",
            description: "Failed to upload image to cloud storage. Your message will be sent without the image.",
            variant: "destructive",
          });

          // Continue without image
          imageData = undefined;
          image = undefined;
        }
      }

      const messageContent = !trimmedQuery && image ? "Answer according to the image" : trimmedQuery; // Changed default text here

      // Only add the user message if not skipping
      if (!skipAddingUserMessage) {
        const newUserMessage = {
          content: messageContent,
          isUser: true,
          image: imageData // Include the Cloudinary image data
        };
        setMessages(prevMessages => [...prevMessages, newUserMessage]);
      }

      const updatedContent = [
        ...chatContent,
        { role: "user", parts: [{ text: messageContent }] }
      ];
      setChatContent(updatedContent);

      const safetySettings = [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
        },
      ];

      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error("API key not configured");
      }
      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({
        model: GEMINI_MODEL,
        safetySettings: safetySettings,
        systemInstruction: getSystemInstruction()
      });

      let result;
      if (image) {
        // Convert image to base64 for sending to Gemini API
        const base64Image = await convertImageToBase64(image);
        result = await model.generateContent([
          {
            inlineData: {
              data: base64Image.split(',')[1],
              mimeType: image.type
            }
          },
          messageContent
        ]);
      } else {
        const chat = model.startChat({
          history: updatedContent,
          generationConfig: {},
        });
        result = await chat.sendMessage(messageContent);
      }

      const response = await result.response;
      const text = response.text();

      const newAIMessage = { content: text, isUser: false };
      setMessages(prevMessages => [...prevMessages, newAIMessage]);

      const finalContent = [...updatedContent, { role: "model", parts: [{ text }] }];
      setChatContent(finalContent);

    } catch (error: any) {
      console.error("Error generating response:", error);
      if (!handleApiError(error, "Gemini")) {
        toast({
          title: "Error",
          description: "Failed to generate response. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageDrop = (file: File) => {
    setSelectedImage(file);
    setShowImageEditor(true);
  };

  const handleAddComment = async (content: string, author: string) => {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) return;

    const newComment: Comment = {
      id: generateUUID(),
      content,
      author,
      authorId: user.uid,
      timestamp: Date.now(),
    };

    setComments(prevComments => [...prevComments, newComment]);
  };

  const handleAddReply = async (parentId: string, content: string) => {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) return;

    const reply: Comment = {
      id: generateUUID(),
      content,
      author: username || 'Anonymous',
      authorId: user.uid,
      timestamp: Date.now(),
    };
  };

  const isDeveloperMode = sessionStorage.getItem("isDeveloperMode") === "true";
  const username = sessionStorage.getItem("userName") || "";

  // Update chatId ref whenever currentChatId changes
  useEffect(() => {
    if (currentChatId) {
      console.log('Current chat ID updated:', currentChatId);
      chatId.current = currentChatId;

      // Update the URL with the current chat ID
      navigate(`/fullscreen?chatId=${currentChatId}`, { replace: true });
    }
  }, [currentChatId, navigate]);

  // Handle when a share link is generated
  const handleShareGenerated = (url: string) => {
    setShareUrl(url);
  };

  // Apply sorting to chat history
  const sortedChatHistory = useMemo(() => {
    return [...chatHistory].sort((a, b) => {
      if (sortBy === "newest") {
        return b.timestamp - a.timestamp;
      } else if (sortBy === "oldest") {
        return a.timestamp - b.timestamp;
      } else {
        // Sort by name (preview)
        return a.preview.localeCompare(b.preview);
      }
    });
  }, [chatHistory, sortBy]);

  // Add the togglePinChat function
  const togglePinChat = async (selectedChatId: string) => {
    try {
      // Find the chat to toggle with explicit type
      const chatToUpdate = chatHistory.find(chat => chat.id === selectedChatId) as ChatHistory | undefined;
      if (!chatToUpdate) return;

      // New pinned status is the opposite of current
      const newPinnedStatus = !chatToUpdate.isPinned;

      // If this is the current chat, update local state
      if (selectedChatId === chatId.current) {
        setIsPinned(newPinnedStatus);
      }

      // Create updated chat history with toggled isPinned value
      const updatedChatHistory = chatHistory.map(chat => {
        if (chat.id === selectedChatId) {
          return { ...chat, isPinned: newPinnedStatus };
        }
        return chat;
      });

      // Update local state
      setChatHistory(updatedChatHistory);

      // Save to Firebase
      if (user) {
        // Construct a clean object with only the needed properties
        await saveAIChat(selectedChatId, {
          userId: user.uid,
          messages: chatToUpdate.messages || [],
          isPinned: newPinnedStatus,
          isStarred: chatToUpdate.isStarred,
          updatedAt: Date.now(),
          preview: chatToUpdate.preview || "Chat",
          comments: chatToUpdate.comments || []
        }, true);

        toast({
          title: newPinnedStatus ? "Chat pinned" : "Chat unpinned",
          description: newPinnedStatus 
            ? "Chat added to pinned section" 
            : "Chat removed from pinned section",
        });
      }
    } catch (error) {
      console.error('Error toggling pin for chat:', error);
      toast({
        title: "Error",
        description: "Failed to update pin status for this chat.",
        variant: "destructive",
      });
    }
  };

  // Add the toggleStarChat function
  const toggleStarChat = async (selectedChatId: string) => {
    try {
      // Find the chat to toggle with explicit type
      const chatToUpdate = chatHistory.find(chat => chat.id === selectedChatId) as ChatHistory | undefined;
      if (!chatToUpdate) return;

      // Create updated chat history with toggled isStarred value
      const updatedChatHistory = chatHistory.map(chat => {
        if (chat.id === selectedChatId) {
          return { ...chat, isStarred: !chat.isStarred };
        }
        return chat;
      });

      // Update local state
      setChatHistory(updatedChatHistory);

      // Save to Firebase
      if (user) {
        // Construct a clean object with only the needed properties
        await saveAIChat(selectedChatId, {
          userId: user.uid,
          messages: chatToUpdate.messages || [],
          isPinned: chatToUpdate.isPinned,
          isStarred: !chatToUpdate.isStarred,
          updatedAt: Date.now(),
          preview: chatToUpdate.preview || "Chat",
          comments: chatToUpdate.comments || []
        }, true);

        toast({
          title: chatToUpdate.isStarred ? "Chat unstarred" : "Chat starred",
          description: chatToUpdate.isStarred 
            ? "Chat removed from starred section" 
            : "Chat added to starred section",
        });
      }
    } catch (error) {
      console.error('Error toggling star for chat:', error);
      toast({
        title: "Error",
        description: "Failed to update star status for this chat.",
        variant: "destructive",
      });
    }
  };

  // Return the JSX for the component
  return (
    <>
      {/* SEO Share links */}
      {shareUrl && chatId.current && (
        <SEOShareLinks
          chatId={chatId.current}
          shareUrl={shareUrl}
          chatData={{
            messages,
            username: user?.displayName || 'User',
          }}
        />
      )}

      <div className="min-h-screen flex flex-col">
        {/* Header */}
        <header className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-br from-background via-secondary to-background border-b shadow-sm fullscreen-chat-header">
          <div className="container mx-auto px-2 sm:px-6 lg:px-8 h-16 flex items-center justify-between relative">
            {/* Left section - Navigation buttons */}
            <div className="flex items-center space-x-1 sm:space-x-2 button-container">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className="h-9 w-9"
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Menu className="h-4 w-4 sm:h-5 sm:w-5" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Chat History</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleNewChat}
                className="h-9 w-9"
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Plus className="h-4 w-4 sm:h-5 sm:w-5" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>New Chat (Return to Home)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Button>
              {messages.length > 1 && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <ShareChatButton
                        chatId={chatId.current}
                        chatData={{
                          messages,
                          comments,
                          timestamp: Date.now(),
                          preview: messages[0]?.content.substring(0, 100) || "Chat session",
                        }}
                        showTextLabel={false}
                        onShareGenerated={handleShareGenerated}
                        className="hover:bg-primary/10 hover:text-primary h-9 w-9"
                      />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Share Chat</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              {messages.length > 1 && chatId.current && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          // Find current chat in chatHistory
                          const currentChat = chatHistory.find(chat => chat.id === chatId.current);
                          if (currentChat) {
                            togglePinChat(chatId.current);
                          }
                        }}
                        className={`hover:bg-primary/10 h-9 w-9 ${isPinned ? 'text-primary' : ''}`}
                      >
                        {isPinned ? 
                          <PinOff className="h-4 w-4 sm:h-5 sm:w-5" /> : 
                          <Pin className="h-4 w-4 sm:h-5 sm:w-5" />
                        }
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isPinned ? 'Unpin Chat' : 'Pin Chat'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            {/* Center section - Logo */}
            <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
              <Link to="/ai" className="flex items-center space-x-2 sm:space-x-3 hover:opacity-80 transition-all duration-300 hover:scale-105">
                <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-6 h-6 sm:w-8 sm:h-8 rounded-full shadow-lg transition-all duration-300" />
                <h1 className="text-xl sm:text-2xl md:text-3xl font-extrabold font-sans tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/80">
                  IsotopeAI
                </h1>
              </Link>
            </div>

            {/* Right section - Empty for now */}
            <div className="flex items-center justify-end">
              {/* Right section content can be added here if needed */}
            </div>
          </div>
        </header>

        {/* Sidebar */}
        <div
          className={cn(
            "fixed inset-y-0 left-0 z-50 w-[85vw] sm:w-72 bg-black/30 border-r border-white/10 backdrop-blur-xl transform transition-transform duration-300 ease-in-out shadow-xl",
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          )}
        >
          <div className="p-3 md:p-4 flex flex-col h-full">
            <div className="flex items-center justify-between mb-4 md:mb-6">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4 md:h-5 md:w-5 text-primary" />
                <h2 className="text-lg md:text-xl font-bold">Recent Chats</h2>
              </div>
              <div className="flex items-center space-x-1">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 md:h-10 md:w-10 rounded-full hover:bg-white/10"
                      aria-label="Sort chats"
                    >
                      <ArrowUpDown className="h-4 w-4 md:h-5 md:w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSortBy("newest")}>
                      Newest First
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                      Oldest First
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy("name")}>
                      By Name
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button
                  onClick={toggleSidebar}
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 md:h-10 md:w-10 rounded-full hover:bg-white/10"
                  aria-label="Close sidebar"
                >
                  <X className="h-4 w-4 md:h-5 md:w-5" />
                </Button>
              </div>
            </div>

            <div className="flex-grow overflow-auto">
              {loadingChatHistory ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : chatHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground text-sm">
                  No recent chats
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Pinned Chats Section */}
                  {sortedChatHistory.some(chat => chat.isPinned) && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 px-2">
                        <Pin className="h-3 w-3 text-primary" />
                        <h3 className="text-xs font-medium text-primary">Pinned Chats</h3>
                      </div>
                      <div className="space-y-2">
                        {sortedChatHistory.filter(chat => chat.isPinned).map((chat) => (
                          <div
                            key={chat.id}
                            className="p-3 rounded-lg bg-primary/5 hover:bg-primary/10 cursor-pointer transition-all duration-200 border border-primary/20 hover:border-primary/40"
                            onClick={() => loadChat(chat.id)}
                          >
                            <div className="flex justify-between">
                              <p className="font-medium line-clamp-2 text-sm">{chat.preview}</p>
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 hover:bg-primary/10"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    togglePinChat(chat.id);
                                  }}
                                >
                                  <PinOff className="h-3 w-3 text-primary" />
                                </Button>
                              </div>
                            </div>
                            <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(chat.timestamp).toLocaleDateString(undefined, {
                                month: 'short',
                                day: 'numeric'
                              })}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Regular Chats Section */}
                  <div className="space-y-2">
                    {sortedChatHistory.filter(chat => !chat.isPinned).length > 0 && (
                      <div className="flex items-center gap-2 px-2">
                        <MessageSquare className="h-3 w-3 text-muted-foreground" />
                        <h3 className="text-xs font-medium text-muted-foreground">All Chats</h3>
                      </div>
                    )}
                    <div className="space-y-2 md:space-y-3">
                      {sortedChatHistory.filter(chat => !chat.isPinned).map((chat) => (
                        <div
                          key={chat.id}
                          className="p-2 md:p-3 rounded-lg bg-background/60 hover:bg-accent/10 cursor-pointer transition-all duration-200 border border-border/50 hover:border-primary/30 group"
                          onClick={() => loadChat(chat.id)}
                        >
                          <div className="flex justify-between">
                            <p className="font-medium line-clamp-2 text-xs md:text-sm">{chat.preview}</p>
                            <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 hover:bg-primary/10"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  togglePinChat(chat.id);
                                }}
                              >
                                <Pin className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className={`h-6 w-6 hover:bg-yellow-500/10 ${chat.isStarred ? "text-yellow-500" : ""}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleStarChat(chat.id);
                                }}
                              >
                                <Star className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center gap-1 md:gap-2 mt-1 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(chat.timestamp).toLocaleDateString(undefined, {
                              month: 'short',
                              day: 'numeric'
                            })}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Button
              onClick={handleNewChat}
              variant="outline"
              className="mt-3 md:mt-4 w-full text-sm md:text-base py-1 md:py-2"
            >
              <Plus className="h-4 w-4 mr-2" /> New Chat
            </Button>
          </div>
        </div>

        {/* Chat Content (Scrollable Area) */}
        <main className="flex-1 pt-16 pb-24 overflow-y-auto" ref={chatInterfaceRef}>
          {messages.map((message, index) => (
            <ChatMessage
              key={index}
              content={message.content}
              isUser={message.isUser}
              image={message.image}
              hideCodeBlocks={!message.isUser}
            />
          ))}
          {isLoading && (
            <div className="flex justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          )}
        </main>

        {/* Input Area */}
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-card/90 backdrop-blur-md border-t shadow-lg z-10">
          <DragDropWrapper onImageDrop={handleImageDrop}>
            <SearchBar
              onSubmit={handleSendMessage}
              handleSendMessage={handleSendMessage}
              isChatVisible={true}
              selectedImage={selectedImage}
              setSelectedImage={setSelectedImage}
              showImageEditor={showImageEditor}
              setShowImageEditor={setShowImageEditor}
              onToolOutput={(output) => {
                const newUserMessage = {
                  content: `Used ${output.type} tool`,
                  isUser: true,
                };
                const newAIMessage = {
                  content: "```tool-output\n" + JSON.stringify(output, null, 2) + "\n```",
                  isUser: false,
                };
                setMessages(prevMessages => [...prevMessages, newUserMessage, newAIMessage]);
              }}
            />
          </DragDropWrapper>
        </div>
      </div>
    </>
  );
};
