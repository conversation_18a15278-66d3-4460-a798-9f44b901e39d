import { Settings as SettingsI<PERSON>, <PERSON>, Sun, LogOut, Trash2, Activity, Award, Palette, User, Mail, Link, X } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "./ui/dialog";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { useState, useEffect } from "react";
import { useToast } from "./ui/use-toast";
import { Alert, AlertDescription, AlertTitle } from "./ui/alert";
import { useTheme } from "@/hooks/use-theme";
import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarImage, AvatarFallback } from "./ui/avatar";
import { doc, getDoc, deleteDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db, updateUserProfile, checkUsernameAvailability, getUserStats, UserStats, deleteAIChat, getUserChatHistory, 
  linkWithEmailPassword, linkWithGoogle, unlinkProvider, getUserProviders, signInWithEmailPassword } from "@/utils/firebase";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog";
import { Loader2 } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Progress } from "./ui/progress";
import { Separator } from "./ui/separator";
import { useLocation, useNavigate } from "react-router-dom";
import { GoogleAuthProvider, linkWithPopup, getAuth } from "firebase/auth";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

export const Settings = () => {
  const { user, signOut } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [username, setUsername] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const { theme, toggleTheme } = useTheme();
  const [stats, setStats] = useState<UserStats | null>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const [providers, setProviders] = useState<string[]>([]);
  const [linkingPassword, setLinkingPassword] = useState("");
  const [linkingLoading, setLinkingLoading] = useState(false);
  const [linkingError, setLinkingError] = useState("");
  const [existingEmail, setExistingEmail] = useState("");
  const [existingPassword, setExistingPassword] = useState("");
  const [showExistingEmailForm, setShowExistingEmailForm] = useState(false);

  // Check if current route is a landing page
  const isLandingPage = location.pathname.includes('-landing') || location.pathname === '/';

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      setIsLoading(true);
      try {
        const userRef = doc(db, 'users', user.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const userData = userSnap.data();
          setUsername(userData.username || "");
        }
        
        // Fetch user stats
        const userStats = await getUserStats(user.uid);
        setStats(userStats);
        
        // Get user's auth providers
        setProviders(getUserProviders());
      } catch (error) {
        console.error('Error fetching user profile:', error);
        toast({
          title: "Error",
          description: "Failed to load user profile",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchUserProfile();
    }
  }, [user, isOpen]);

  const handleUsernameUpdate = async () => {
    if (!user || !username.trim() || username.trim().length < 3) return;

    setIsUpdating(true);
    try {
      // Check username availability before updating
      const isAvailable = await checkUsernameAvailability(username.trim());
      if (!isAvailable) {
        toast({
          title: "Error",
          description: "This username is already taken. Please choose a different one.",
          variant: "destructive",
        });
        return;
      }

      await updateUserProfile(user.uid, {
        username: username.trim(),
        photoURL: user.photoURL || "",
        displayName: user.displayName || username.trim(),
      });

      toast({
        title: "Profile Updated",
        description: "Your username has been successfully updated.",
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteChats = async () => {
    if (!user) return;
    
    setIsUpdating(true);
    try {
      // First get all user's chats
      const userChats = await getUserChatHistory(user.uid);
      
      // Delete each chat from Firebase
      await Promise.all(userChats.map(async (chat) => {
        await deleteAIChat(chat.id);
      }));
      
      // Clear localStorage as before
      localStorage.removeItem("chatHistoryList");
      localStorage.removeItem("currentChatId");
      
      toast({
        title: "Chats Deleted",
        description: "All chat history has been deleted successfully.",
      });
      setIsOpen(false);
    } catch (error) {
      console.error('Error deleting all chats:', error);
      toast({
        title: "Error",
        description: "Failed to delete all chats. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleLinkWithEmail = async () => {
    if (!user) return;
    if (!linkingPassword || linkingPassword.length < 6) {
      setLinkingError("Password must be at least 6 characters");
      return;
    }
    
    setLinkingLoading(true);
    setLinkingError("");
    
    try {
      await linkWithEmailPassword(linkingPassword);
      setProviders(getUserProviders());
      toast({
        title: "Account Linked",
        description: "Your account has been linked with email and password",
      });
      setLinkingPassword("");
    } catch (error: any) {
      console.error('Error linking with email:', error);
      
      // Handle case where email is already in use
      if (error.code === "auth/email-already-in-use") {
        setShowExistingEmailForm(true);
        setLinkingError("This email is already registered. Please sign in with that account to link them.");
      } else {
        setLinkingError(error.message || "Failed to link account with email");
      }
    } finally {
      setLinkingLoading(false);
    }
  };

  const handleLinkExistingEmail = async () => {
    if (!user || !existingEmail || !existingPassword) return;
    
    setLinkingLoading(true);
    setLinkingError("");
    
    try {
      // First sign in to Firebase with the existing email account
      const result = await signInWithEmailPassword(existingEmail, existingPassword);
      if (!result.user) throw new Error("Failed to authenticate with existing email");
      
      // Then use Firebase Auth API directly to link the accounts
      const auth = getAuth();
      const googleProvider = new GoogleAuthProvider();
      await linkWithPopup(result.user, googleProvider);
      
      setProviders(getUserProviders());
      toast({
        title: "Accounts Merged",
        description: "Your Google and email accounts have been successfully linked",
      });
      
      setExistingEmail("");
      setExistingPassword("");
      setShowExistingEmailForm(false);
    } catch (error: any) {
      console.error('Error linking existing email account:', error);
      setLinkingError(error.message || "Failed to link accounts. Please ensure your email and password are correct.");
    } finally {
      setLinkingLoading(false);
    }
  };
  
  const handleCancelLinkExisting = () => {
    setShowExistingEmailForm(false);
    setExistingEmail("");
    setExistingPassword("");
    setLinkingError("");
  };
  
  const handleLinkWithGoogle = async () => {
    if (!user) return;
    
    setLinkingLoading(true);
    setLinkingError("");
    
    try {
      await linkWithGoogle();
      setProviders(getUserProviders());
      toast({
        title: "Account Linked",
        description: "Your account has been linked with Google",
      });
    } catch (error: any) {
      console.error('Error linking with Google:', error);
      setLinkingError(error.message || "Failed to link account with Google");
    } finally {
      setLinkingLoading(false);
    }
  };
  
  const handleUnlinkProvider = async (providerId: string) => {
    if (!user) return;
    
    // Don't allow unlinking if there's only one provider
    if (providers.length <= 1) {
      toast({
        title: "Cannot Unlink",
        description: "You need at least one login method",
        variant: "destructive",
      });
      return;
    }
    
    setLinkingLoading(true);
    
    try {
      await unlinkProvider(providerId);
      setProviders(getUserProviders());
      toast({
        title: "Account Unlinked",
        description: "Provider has been successfully unlinked",
      });
    } catch (error: any) {
      console.error('Error unlinking provider:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to unlink provider",
        variant: "destructive",
      });
    } finally {
      setLinkingLoading(false);
    }
  };

  const navigateToSettings = () => {
    navigate('/settings');
  };

  if (!user || isLandingPage) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 right-4 z-[60] rounded-full"
        >
          <Avatar className="h-9 w-9">
            <AvatarImage src={user.photoURL || undefined} alt={user.displayName || "User"} />
            <AvatarFallback>
              {user.displayName?.[0]?.toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem className="flex items-center gap-2" onClick={() => navigate('/profile')}>
          <User className="h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="flex items-center gap-2" onClick={navigateToSettings}>
          <SettingsIcon className="h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="flex items-center gap-2" onClick={handleSignOut}>
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
