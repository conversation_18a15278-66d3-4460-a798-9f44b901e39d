import { create } from 'zustand'
import { User } from 'firebase/auth'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { persist } from 'zustand/middleware'
import { UserProfile } from '../types/user'

// Union type for both Firebase and Supabase users
type AuthUser = User | SupabaseUser | null;

interface UserState {
  user: AuthUser
  userProfile: UserProfile | null
  isLoading: boolean
  lastFetched: number | null
  authProvider: 'firebase' | 'supabase' | null // Track which auth provider is being used
  setUser: (user: AuthUser) => void
  setUserProfile: (profile: UserProfile | null) => void
  setLoading: (loading: boolean) => void
  setLastFetched: (timestamp: number | null) => void
  setAuthProvider: (provider: 'firebase' | 'supabase' | null) => void
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: null,
      userProfile: null,
      isLoading: true,
      lastFetched: null,
      authProvider: null,
      setUser: (user) => set({ user }),
      setUserProfile: (profile) => set({ userProfile: profile }),
      setLoading: (loading) => set({ isLoading: loading }),
      setLastFetched: (timestamp) => set({ lastFetched: timestamp }),
      setAuthProvider: (provider) => set({ authProvider: provider }),
    }),
    {
      name: 'user-storage',
    }
  )
)