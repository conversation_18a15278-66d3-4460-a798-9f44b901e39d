import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  collection,
  doc,
  setDoc,
  updateDoc,
  deleteDoc,
  onSnapshot,
  query,
  where,
  getDocs,
  arrayUnion,
  arrayRemove,
  Timestamp
} from 'firebase/firestore';
import { db } from '../utils/firebase';
import { TodoState, TodoItem, TodoColumn, TodoBoard } from '../types/todo';
import { v4 as uuidv4 } from 'uuid';

// Define enhanced drag source/destination type
interface EnhancedDragItem {
  droppableId: string;
  index: number;
  taskId?: string;
}

// Initial state with default columns
const initialBoard: TodoBoard = {
  tasks: {},
  columns: {
    'column-1': {
      id: 'column-1',
      title: 'Todo',
      taskIds: [],
    },
    'column-2': {
      id: 'column-2',
      title: 'In Progress',
      taskIds: [],
    },
    'column-3': {
      id: 'column-3',
      title: 'Done',
      taskIds: [],
    },
  },
  columnOrder: ['column-1', 'column-2', 'column-3'],
};

const initialState: TodoState = {
  board: initialBoard,
  loading: false,
  error: null,
};

export const useTodoStore = create<
  TodoState & {
    // Actions
    fetchTodos: (userId: string, groupId?: string) => void;
    addTask: (task: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
    updateTask: (taskId: string, updates: Partial<TodoItem>) => Promise<void>;
    deleteTask: (taskId: string) => Promise<void>;
    moveTask: (source: EnhancedDragItem, destination: EnhancedDragItem) => Promise<void>;
    addColumn: (title: string) => Promise<void>;
    updateColumn: (columnId: string, title: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;
    reset: () => void;
  }
>(
  persist(
    (set, get) => ({
  ...initialState,

  fetchTodos: (userId, groupId) => {
    set({ loading: true, error: null });

    // Always fetch personal todos for the user
    const todosQuery = query(
      collection(db, 'todos'),
      where('createdBy', '==', userId)
    );

    // Set up the listener but don't return it
    onSnapshot(
      todosQuery,
      (snapshot) => {
        const tasks: Record<string, TodoItem> = {};

        // Get current columns from state (which may include persisted custom columns)
        const columns = JSON.parse(JSON.stringify(get().board.columns));

        // Reset taskIds in all columns
        Object.keys(columns).forEach(columnId => {
          columns[columnId].taskIds = [];
        });

        snapshot.forEach((doc) => {
          const data = doc.data();

          // Ensure the task has an ID
          const task = {
            ...data,
            id: doc.id
          } as TodoItem;

          tasks[task.id] = task;

          // Find which column this task belongs to
          const columnId = data.columnId || 'column-1'; // Default to Todo column

          if (columns[columnId]) {
            columns[columnId].taskIds.push(task.id);
          } else {
            // If column doesn't exist (e.g., custom column was deleted), move to Todo
            columns['column-1'].taskIds.push(task.id);
          }
        });

        // Debug log to see what's being loaded
        console.log('Loaded tasks:', tasks);
        console.log('Columns state:', columns);
        console.log('Column order:', get().board.columnOrder);

        set({
          board: {
            tasks,
            columns,
            columnOrder: get().board.columnOrder,
          },
          loading: false,
        });
      },
      (error) => {
        console.error('Error fetching todos:', error);
        set({ error: error.message, loading: false });
      }
    );

    // Don't return anything
  },

  addTask: async (taskData) => {
    try {
      const taskId = uuidv4();
      const now = Date.now();

      // Create a clean task object without undefined values
      const newTask: TodoItem = {
        id: taskId,
        title: taskData.title,
        description: taskData.description || '',
        priority: taskData.priority || 'medium',
        createdAt: now,
        updatedAt: now,
        createdBy: taskData.createdBy,
      };

      // Add optional fields only if they exist
      if (taskData.dueDate) {
        newTask.dueDate = taskData.dueDate;
      }

      if (taskData.assignedTo) {
        newTask.assignedTo = taskData.assignedTo;
      }

      if (taskData.assignedToName) {
        newTask.assignedToName = taskData.assignedToName;
      }

      if (taskData.assignedToPhotoURL) {
        newTask.assignedToPhotoURL = taskData.assignedToPhotoURL;
      }


      // Add to Firestore with the columnId field
      const firestoreData = {
        ...newTask,
        columnId: 'column-1', // Default to Todo column
      };

      console.log('Adding task to Firestore:', firestoreData);
      await setDoc(doc(db, 'todos', taskId), firestoreData);

      // Also update the local state for immediate UI update
      const board = JSON.parse(JSON.stringify(get().board));
      board.tasks[taskId] = newTask;
      board.columns['column-1'].taskIds.push(taskId);
      set({ board });

      return Promise.resolve();
    } catch (error: any) {
      console.error('Error adding task:', error);
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  updateTask: async (taskId, updates) => {
    try {
      const taskRef = doc(db, 'todos', taskId);
      await updateDoc(taskRef, {
        ...updates,
        updatedAt: Date.now(),
      });

      return Promise.resolve();
    } catch (error: any) {
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  deleteTask: async (taskId) => {
    try {
      await deleteDoc(doc(db, 'todos', taskId));
      return Promise.resolve();
    } catch (error: any) {
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  moveTask: async (source: EnhancedDragItem, destination: EnhancedDragItem) => {
    try {
      // Early return if dropped in the same position
      if (
        source.droppableId === destination.droppableId &&
        source.index === destination.index
      ) {
        return Promise.resolve();
      }

      // Get the task ID directly from the enhanced source object
      const taskId = source.taskId;

      if (!taskId) {
        console.error('No taskId provided in source object');
        return Promise.reject(new Error('No taskId provided'));
      }

      // Log the task movement for debugging
      console.log('Moving task with ID:', taskId, 'from', source.droppableId, 'to', destination.droppableId);

      // Create a copy of the current board
      const board = JSON.parse(JSON.stringify(get().board));

      // Get the source and destination columns
      const sourceColumn = board.columns[source.droppableId];
      const destColumn = board.columns[destination.droppableId];

      // Basic validation
      if (!sourceColumn || !destColumn) {
        console.error('Invalid source or destination column');
        return Promise.reject(new Error('Invalid column'));
      }

      // Find the actual index of the task in the source column
      const actualSourceIndex = sourceColumn.taskIds.indexOf(taskId);

      // If the task is not found in the source column, log an error and return
      if (actualSourceIndex === -1) {
        console.error('Task not found in source column', { taskId, sourceColumn });
        return Promise.reject(new Error('Task not found in source column'));
      }

      // Remove from source column using the actual index
      sourceColumn.taskIds.splice(actualSourceIndex, 1);

      // Ensure destination index is valid
      // If the destination index is greater than the number of tasks in the column,
      // adjust it to be at the end of the column
      const safeDestIndex = Math.min(destination.index, destColumn.taskIds.length);

      // Insert into destination column
      destColumn.taskIds.splice(safeDestIndex, 0, taskId);

      // Update the state with the new board configuration
      set({ board });

      // Update in Firestore
      const taskRef = doc(db, 'todos', taskId);
      await updateDoc(taskRef, {
        columnId: destination.droppableId,
        updatedAt: Date.now(),
      });

      return Promise.resolve();
    } catch (error: any) {
      console.error('Error moving task:', error);
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  addColumn: async (title) => {
    try {
      const columnId = `column-${uuidv4()}`;
      const newColumn: TodoColumn = {
        id: columnId,
        title,
        taskIds: [],
      };

      // Update local state
      const board = JSON.parse(JSON.stringify(get().board));
      board.columns[columnId] = newColumn;
      board.columnOrder.push(columnId);

      set({ board });

      // We could store column configuration in Firestore as well
      // For simplicity, we're just updating the local state

      return Promise.resolve();
    } catch (error: any) {
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  updateColumn: async (columnId, title) => {
    try {
      // Update local state
      const board = JSON.parse(JSON.stringify(get().board));
      board.columns[columnId].title = title;

      set({ board });

      // We could update column configuration in Firestore as well

      return Promise.resolve();
    } catch (error: any) {
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  deleteColumn: async (columnId) => {
    try {
      // Don't allow deleting if it's one of the default columns
      if (['column-1', 'column-2', 'column-3'].includes(columnId)) {
        throw new Error("Cannot delete default columns");
      }

      // Update local state
      const board = JSON.parse(JSON.stringify(get().board));

      // Move tasks from this column to Todo column
      const tasksToMove = board.columns[columnId].taskIds;
      board.columns['column-1'].taskIds = [
        ...board.columns['column-1'].taskIds,
        ...tasksToMove
      ];

      // Delete the column
      delete board.columns[columnId];

      // Update column order
      board.columnOrder = board.columnOrder.filter(id => id !== columnId);

      set({ board });

      // Update tasks in Firestore to move them to Todo column
      const updatePromises = tasksToMove.map(taskId => {
        const taskRef = doc(db, 'todos', taskId);
        return updateDoc(taskRef, {
          columnId: 'column-1',
          updatedAt: Date.now(),
        });
      });

      await Promise.all(updatePromises);

      return Promise.resolve();
    } catch (error: any) {
      set({ error: error.message });
      return Promise.reject(error);
    }
  },

  reset: () => {
    set(initialState);
  },
}), {
  name: 'todo-board-storage', // Name for the localStorage key
  partialize: (state) => ({
    // Only persist the columns and columnOrder, not the tasks
    board: {
      columns: state.board.columns,
      columnOrder: state.board.columnOrder,
      tasks: {} // Don't persist tasks as they come from Firebase
    }
  }),
})
);
