import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Subject } from '@/components/productivity/SubjectManager'; // Assuming Subject interface is exported here

interface SubjectState {
  subjects: Subject[];
  isLoading: boolean;
  lastFetched: number | null;
  setSubjects: (subjects: Subject[]) => void;
  setLoading: (loading: boolean) => void;
  setLastFetched: (timestamp: number | null) => void;
  addSubject: (subject: Subject) => void;
  updateSubject: (subject: Subject) => void;
  deleteSubject: (subjectId: string) => void;
}

export const useSubjectStore = create<SubjectState>()(
  persist(
    (set) => ({
      subjects: [],
      isLoading: true,
      lastFetched: null,
      setSubjects: (subjects) => set({ subjects, isLoading: false, lastFetched: Date.now() }),
      setLoading: (loading) => set({ isLoading: loading }),
      setLastFetched: (timestamp) => set({ lastFetched: timestamp }),
      addSubject: (subject) => set((state) => ({
        subjects: [...state.subjects, subject],
        lastFetched: Date.now() // Update lastFetched on modification
      })),
      updateSubject: (subject) => set((state) => ({
        subjects: state.subjects.map((s) => (s.id === subject.id ? subject : s)),
        lastFetched: Date.now() // Update lastFetched on modification
      })),
      deleteSubject: (subjectId) => set((state) => ({
        subjects: state.subjects.filter((s) => s.id !== subjectId),
        lastFetched: Date.now() // Update lastFetched on modification
      })),
    }),
    {
      name: 'subject-storage',
    }
  )
);