import { Toaster } from "./components/ui/toaster";
import { Toaster as Sonner } from "./components/ui/sonner";
import { TooltipProvider } from "./components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect, lazy, Suspense } from "react";
import { app as firebaseApp } from "./utils/firebase"; // Import firebaseApp
import { getFunctions } from 'firebase/functions'; // Import Functions SDK
import { ReloadPrompt } from './components/ReloadPrompt';
import { OfflineIndicator } from './components/OfflineIndicator';
import { PWAPrompt } from './components/PWAPrompt';
import { Settings } from './components/Settings';
import { AuthProvider } from "./contexts/AuthContext";
import { SupabaseAuthProvider } from "./contexts/SupabaseAuthContext";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { BackgroundThemeProvider } from "./contexts/BackgroundThemeContext";
import { HelmetProvider } from 'react-helmet-async';
import { ActivityTracker } from "./components/ActivityTracker";

// Core components that should load immediately
import Index from "./pages/Index";
import Login from "./pages/Login";
import NotFound from './components/NotFound';
import { JoinHandler } from "./components/chat/JoinHandler";

// Lazy-loaded components
const AI = lazy(() => import("./pages/AI"));
const ProfileSetupPage = lazy(() => import("./pages/ProfileSetup"));
const Groups = lazy(() => import("./pages/Groups"));
const Productivity = lazy(() => import("./pages/Productivity"));
const Analytics = lazy(() => import("@/pages/Analytics"));
const Tasks = lazy(() => import("@/pages/Tasks"));
const MockTestAnalysis = lazy(() => import("@/pages/MockTestAnalysis"));
const QAPage = lazy(() => import("./pages/QAPage"));
const QALanding = lazy(() => import("./pages/landing/QALanding"));
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));
const TermsOfService = lazy(() => import("./pages/TermsOfService"));
const AboutUs = lazy(() => import("./pages/AboutUs"));
const ContactUs = lazy(() => import("./pages/ContactUs"));
const Ads = lazy(() => import("./pages/Ads"));
const MathTestComponent = lazy(() => import("./components/ui/MathTestComponent"));
const FullscreenChatWrapper = lazy(() => import("./components/FullscreenChatWrapper"));
const Changelog = lazy(() => import("./pages/Changelog"));
const SharedChatView = lazy(() => import("./components/SharedChatView"));
const SharedChatDirectory = lazy(() => import("./pages/SharedChatDirectory"));
const QADirectory = lazy(() => import('./pages/QADirectory'));
const SettingsPage = lazy(() => import("./pages/SettingsPage"));
const ProfilePage = lazy(() => import("./pages/ProfilePage"));
const Migration = lazy(() => import("./pages/Migration"));
const AuthCallback = lazy(() => import("./pages/AuthCallback"));
const DataMigration = lazy(() => import("./pages/DataMigration"));

// Lazy-loaded landing pages
const AILanding = lazy(() => import("./pages/landing/AILanding"));
const GroupsLanding = lazy(() => import("./pages/landing/GroupsLanding"));
const ProductivityLanding = lazy(() => import("./pages/landing/ProductivityLanding"));
const TasksLanding = lazy(() => import("./pages/landing/TasksLanding"));
const MockTestsLanding = lazy(() => import("./pages/landing/MockTestsLanding"));
const InviteLanding = lazy(() => import("./pages/landing/InviteLanding"));

declare global {
  interface Window {
    dataLayer: any[];
  }
}

const queryClient = new QueryClient();
const functions = getFunctions(firebaseApp); // Initialize Firebase Functions

// Check if we should use Supabase auth (can be controlled via environment variable)
const USE_SUPABASE_AUTH = import.meta.env.VITE_USE_SUPABASE_AUTH === 'true';

const App = () => {
  useEffect(() => {
    // We've removed the aggressive cache clearing to reduce bandwidth usage
    // The service worker now handles cache management more efficiently
  }, []);

  const AuthProviderComponent = USE_SUPABASE_AUTH ? SupabaseAuthProvider : AuthProvider;

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <HelmetProvider>
          <AuthProviderComponent>
            <BackgroundThemeProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <Settings />
                <Routes>
                  {/* Non-lazy loaded routes */}
                  <Route path="/" element={<Index />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/join" element={<JoinHandler />} />
                  <Route path="*" element={<NotFound />} />

                  {/* Auth callback route for Supabase OAuth */}
                  <Route path="/auth/callback" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <AuthCallback />
                    </Suspense>
                  } />

                  {/* Data migration route */}
                  <Route path="/data-migration" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <DataMigration />
                    </Suspense>
                  } />

                  {/* Lazy-loaded routes wrapped in Suspense */}
                  <Route path="/fullscreen" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <FullscreenChatWrapper />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/invite" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <InviteLanding />
                    </Suspense>
                  } />
                  <Route path="/i/:code" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <InviteLanding />
                    </Suspense>
                  } />
                  <Route path="/shared/:chatId" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <SharedChatView />
                    </Suspense>
                  } />
                  <Route path="/shared" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <SharedChatDirectory />
                    </Suspense>
                  } />

                  {/* Landing pages */}
                  <Route path="/ai-landing" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <AILanding />
                    </Suspense>
                  } />
                  <Route path="/groups-landing" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <GroupsLanding />
                    </Suspense>
                  } />
                  <Route path="/productivity-landing" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProductivityLanding />
                    </Suspense>
                  } />
                  <Route path="/tasks-landing" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <TasksLanding />
                    </Suspense>
                  } />
                  <Route path="/mocktest-landing" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <MockTestsLanding />
                    </Suspense>
                  } />
                  <Route path="/qa-landing" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <QALanding />
                    </Suspense>
                  } />

                  {/* Static pages */}
                  <Route path="/privacy-policy" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <PrivacyPolicy />
                    </Suspense>
                  } />
                  <Route path="/terms-of-service" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <TermsOfService />
                    </Suspense>
                  } />
                  <Route path="/about-us" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <AboutUs />
                    </Suspense>
                  } />
                  <Route path="/contact-us" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ContactUs />
                    </Suspense>
                  } />
                  <Route path="/math-test" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <MathTestComponent />
                    </Suspense>
                  } />

                  {/* Protected routes */}
                  <Route path="/profile-setup" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <ProfileSetupPage />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/ai/:chatId?" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <AI />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/groups" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <Groups />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/productivity" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <Productivity />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/tasks" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <Tasks />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/mock-tests" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <MockTestAnalysis />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/qa/*" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <QAPage />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/analytics" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <Analytics />
                    </Suspense>
                  } />
                  <Route path="/qa" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <QADirectory />
                    </Suspense>
                  } />
                  <Route path="/qa/:slug" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <QAPage />
                    </Suspense>
                  } />
                  <Route path="/terms" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <TermsOfService />
                    </Suspense>
                  } />
                  <Route path="/privacy" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <PrivacyPolicy />
                    </Suspense>
                  } />
                  <Route path="/ads" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <Ads />
                    </Suspense>
                  } />
                  <Route path="/changelog" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <Changelog />
                    </Suspense>
                  } />
                  <Route path="/settings" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <SettingsPage />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/profile" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <ProfilePage />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                  <Route path="/migration" element={
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
                      <ProtectedRoute>
                        <Migration />
                      </ProtectedRoute>
                    </Suspense>
                  } />
                </Routes>
              </TooltipProvider>
              <ReloadPrompt />
              <OfflineIndicator />
              <PWAPrompt />
              <ActivityTracker />
            </BackgroundThemeProvider>
          </AuthProviderComponent>
        </HelmetProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

export default App;
