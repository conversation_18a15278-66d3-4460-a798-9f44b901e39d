import { useState, useEffect } from 'react';
import { useUserStore } from '../stores/userStore';
import {
  getUserAIChats,
  subscribeToUserAIChats,
  AIChat
} from '../utils/supabaseAIChats';
import {
  getUserGroups,
  subscribeToUserGroups,
  ChatGroup
} from '../utils/supabaseGroups';
import {
  getUserTodos,
  subscribeToUserTodos,
  TodoItem
} from '../utils/supabaseTodos';
import {
  getUserSubjects,
  getUserExams,
  getUserStats,
  Subject,
  Exam,
  UserStats
} from '../utils/supabaseAnalytics';

// Hook for AI Chats
export const useSupabaseAIChats = (userId?: string) => {
  const [chats, setChats] = useState<AIChat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setChats([]);
      setLoading(false);
      return;
    }

    const fetchChats = async () => {
      try {
        setLoading(true);
        const userChats = await getUserAIChats(userId);
        setChats(userChats);
        setError(null);
      } catch (err) {
        console.error('Error fetching AI chats:', err);
        setError('Failed to load chats');
      } finally {
        setLoading(false);
      }
    };

    fetchChats();

    // Subscribe to real-time updates
    const unsubscribe = subscribeToUserAIChats(userId, (updatedChats) => {
      setChats(updatedChats);
    });

    return unsubscribe;
  }, [userId]);

  return { chats, loading, error, refetch: () => getUserAIChats(userId!) };
};

// Hook for Groups
export const useSupabaseGroups = (userId?: string) => {
  const [groups, setGroups] = useState<ChatGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setGroups([]);
      setLoading(false);
      return;
    }

    const fetchGroups = async () => {
      try {
        setLoading(true);
        const userGroups = await getUserGroups(userId);
        setGroups(userGroups);
        setError(null);
      } catch (err) {
        console.error('Error fetching groups:', err);
        setError('Failed to load groups');
      } finally {
        setLoading(false);
      }
    };

    fetchGroups();

    // Subscribe to real-time updates
    const unsubscribe = subscribeToUserGroups(userId, (updatedGroups) => {
      setGroups(updatedGroups);
    });

    return unsubscribe;
  }, [userId]);

  return { groups, loading, error, refetch: () => getUserGroups(userId!) };
};

// Hook for Todos
export const useSupabaseTodos = (userId?: string) => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setTodos([]);
      setLoading(false);
      return;
    }

    const fetchTodos = async () => {
      try {
        setLoading(true);
        const userTodos = await getUserTodos(userId);
        setTodos(userTodos);
        setError(null);
      } catch (err) {
        console.error('Error fetching todos:', err);
        setError('Failed to load todos');
      } finally {
        setLoading(false);
      }
    };

    fetchTodos();

    // Subscribe to real-time updates
    const unsubscribe = subscribeToUserTodos(userId, (updatedTodos) => {
      setTodos(updatedTodos);
    });

    return unsubscribe;
  }, [userId]);

  return { todos, loading, error, refetch: () => getUserTodos(userId!) };
};

// Hook for Subjects
export const useSupabaseSubjects = (userId?: string) => {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setSubjects([]);
      setLoading(false);
      return;
    }

    const fetchSubjects = async () => {
      try {
        setLoading(true);
        const userSubjects = await getUserSubjects(userId);
        setSubjects(userSubjects);
        setError(null);
      } catch (err) {
        console.error('Error fetching subjects:', err);
        setError('Failed to load subjects');
      } finally {
        setLoading(false);
      }
    };

    fetchSubjects();
  }, [userId]);

  return { subjects, loading, error, refetch: () => getUserSubjects(userId!) };
};

// Hook for Exams
export const useSupabaseExams = (userId?: string) => {
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setExams([]);
      setLoading(false);
      return;
    }

    const fetchExams = async () => {
      try {
        setLoading(true);
        const userExams = await getUserExams(userId);
        setExams(userExams);
        setError(null);
      } catch (err) {
        console.error('Error fetching exams:', err);
        setError('Failed to load exams');
      } finally {
        setLoading(false);
      }
    };

    fetchExams();
  }, [userId]);

  return { exams, loading, error, refetch: () => getUserExams(userId!) };
};

// Hook for User Stats
export const useSupabaseUserStats = (userId?: string) => {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setStats(null);
      setLoading(false);
      return;
    }

    const fetchStats = async () => {
      try {
        setLoading(true);
        const userStats = await getUserStats(userId);
        setStats(userStats);
        setError(null);
      } catch (err) {
        console.error('Error fetching user stats:', err);
        setError('Failed to load stats');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [userId]);

  return { stats, loading, error, refetch: () => getUserStats(userId!) };
};

// Combined hook for all user data
export const useSupabaseUserData = () => {
  const { user, authProvider } = useUserStore();
  const userId = user?.id || user?.uid;
  const isSupabase = authProvider === 'supabase';

  const chats = useSupabaseAIChats(isSupabase ? userId : undefined);
  const groups = useSupabaseGroups(isSupabase ? userId : undefined);
  const todos = useSupabaseTodos(isSupabase ? userId : undefined);
  const subjects = useSupabaseSubjects(isSupabase ? userId : undefined);
  const exams = useSupabaseExams(isSupabase ? userId : undefined);
  const stats = useSupabaseUserStats(isSupabase ? userId : undefined);

  const loading = chats.loading || groups.loading || todos.loading || 
                  subjects.loading || exams.loading || stats.loading;

  const error = chats.error || groups.error || todos.error || 
                subjects.error || exams.error || stats.error;

  return {
    chats: chats.chats,
    groups: groups.groups,
    todos: todos.todos,
    subjects: subjects.subjects,
    exams: exams.exams,
    stats: stats.stats,
    loading,
    error,
    isSupabase,
    refetch: {
      chats: chats.refetch,
      groups: groups.refetch,
      todos: todos.refetch,
      subjects: subjects.refetch,
      exams: exams.refetch,
      stats: stats.refetch
    }
  };
};
