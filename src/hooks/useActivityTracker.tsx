import { useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { updateUserLastActive } from '../utils/firebase';

/**
 * Hook to track user activity and update the lastActive timestamp
 * Triggers on key user interactions like clicks, page loads, and API calls
 */
export const useActivityTracker = () => {
  const { user } = useAuth();
  
  const updateActivity = useCallback(() => {
    if (user?.uid) {
      updateUserLastActive(user.uid).catch(error => {
        console.error('Error updating user activity:', error);
      });
    }
  }, [user]);
  
  useEffect(() => {
    // Update on component mount (page load)
    updateActivity();
    
    // Set up event listeners for user activity
    const handleUserActivity = () => {
      updateActivity();
    };
    
    // Throttle the updates to avoid excessive writes
    let timeout: NodeJS.Timeout | null = null;
    const throttledUpdateActivity = () => {
      if (timeout) return;
      timeout = setTimeout(() => {
        updateActivity();
        timeout = null;
      }, 5 * 60 * 1000); // Only update once every 5 minutes maximum
    };
    
    // Track clicks, scrolls, and keypress
    window.addEventListener('click', throttledUpdateActivity);
    window.addEventListener('scroll', throttledUpdateActivity);
    window.addEventListener('keypress', throttledUpdateActivity);
    
    // Update when tab becomes visible again
    document.addEventListener('visibilitychange', handleUserActivity);
    
    return () => {
      // Clean up event listeners
      window.removeEventListener('click', throttledUpdateActivity);
      window.removeEventListener('scroll', throttledUpdateActivity);
      window.removeEventListener('keypress', throttledUpdateActivity);
      document.removeEventListener('visibilitychange', handleUserActivity);
      
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [updateActivity]);
  
  return { updateActivity };
}; 