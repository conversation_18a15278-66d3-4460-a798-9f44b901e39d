import { useUserStore } from '../stores/userStore';

// Migration configuration
export const MIGRATION_CONFIG = {
  USE_SUPABASE_AUTH: import.meta.env.VITE_USE_SUPABASE_AUTH === 'true',
  USE_SUPABASE_DATA: import.meta.env.VITE_USE_SUPABASE_DATA === 'true',
  DEBUG_MIGRATION: import.meta.env.VITE_DEBUG_MIGRATION === 'true',
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true'
};

// Check if we should use Supabase for a specific feature
export const shouldUseSupabase = (feature?: 'auth' | 'data' | 'all'): boolean => {
  const { authProvider } = useUserStore.getState();
  
  switch (feature) {
    case 'auth':
      return MIGRATION_CONFIG.USE_SUPABASE_AUTH || authProvider === 'supabase';
    case 'data':
      return MIGRATION_CONFIG.USE_SUPABASE_DATA || authProvider === 'supabase';
    case 'all':
      return (MIGRATION_CONFIG.USE_SUPABASE_AUTH && MIGRATION_CONFIG.USE_SUPABASE_DATA) || authProvider === 'supabase';
    default:
      return authProvider === 'supabase';
  }
};

// Debug logging for migration
export const migrationLog = (message: string, data?: any) => {
  if (MIGRATION_CONFIG.DEBUG_MIGRATION) {
    console.log(`[MIGRATION] ${message}`, data);
  }
};

// Error logging for migration
export const migrationError = (message: string, error?: any) => {
  console.error(`[MIGRATION ERROR] ${message}`, error);
};

// Success logging for migration
export const migrationSuccess = (message: string, data?: any) => {
  if (MIGRATION_CONFIG.DEBUG_MIGRATION) {
    console.log(`[MIGRATION SUCCESS] ${message}`, data);
  }
};

// Get current user ID regardless of auth provider
export const getCurrentUserId = (): string | null => {
  const { user } = useUserStore.getState();
  return user?.id || user?.uid || null;
};

// Get current auth provider
export const getCurrentAuthProvider = (): 'firebase' | 'supabase' | null => {
  const { authProvider } = useUserStore.getState();
  return authProvider;
};

// Check if user is authenticated
export const isUserAuthenticated = (): boolean => {
  const { user } = useUserStore.getState();
  return !!user;
};

// Migration status tracking
export interface MigrationStatus {
  auth: 'pending' | 'in-progress' | 'completed' | 'failed';
  aiChats: 'pending' | 'in-progress' | 'completed' | 'failed';
  groups: 'pending' | 'in-progress' | 'completed' | 'failed';
  todos: 'pending' | 'in-progress' | 'completed' | 'failed';
  analytics: 'pending' | 'in-progress' | 'completed' | 'failed';
  overall: 'pending' | 'in-progress' | 'completed' | 'failed';
}

// Get migration status from localStorage
export const getMigrationStatus = (): MigrationStatus => {
  const stored = localStorage.getItem('migration_status');
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch (error) {
      migrationError('Failed to parse migration status from localStorage', error);
    }
  }
  
  return {
    auth: 'pending',
    aiChats: 'pending',
    groups: 'pending',
    todos: 'pending',
    analytics: 'pending',
    overall: 'pending'
  };
};

// Update migration status
export const updateMigrationStatus = (updates: Partial<MigrationStatus>) => {
  const current = getMigrationStatus();
  const updated = { ...current, ...updates };
  
  // Update overall status
  const statuses = [updated.auth, updated.aiChats, updated.groups, updated.todos, updated.analytics];
  if (statuses.every(status => status === 'completed')) {
    updated.overall = 'completed';
  } else if (statuses.some(status => status === 'failed')) {
    updated.overall = 'failed';
  } else if (statuses.some(status => status === 'in-progress')) {
    updated.overall = 'in-progress';
  } else {
    updated.overall = 'pending';
  }
  
  localStorage.setItem('migration_status', JSON.stringify(updated));
  migrationLog('Migration status updated', updated);
};

// Clear migration status
export const clearMigrationStatus = () => {
  localStorage.removeItem('migration_status');
  migrationLog('Migration status cleared');
};

// Check if migration is complete
export const isMigrationComplete = (): boolean => {
  const status = getMigrationStatus();
  return status.overall === 'completed';
};

// Data conversion helpers
export const convertFirebaseTimestamp = (timestamp: any): string => {
  if (!timestamp) return new Date().toISOString();
  
  if (timestamp.seconds) {
    return new Date(timestamp.seconds * 1000).toISOString();
  }
  
  if (typeof timestamp === 'string') {
    return timestamp;
  }
  
  if (timestamp instanceof Date) {
    return timestamp.toISOString();
  }
  
  return new Date().toISOString();
};

// Convert Firebase document to Supabase format
export const convertFirebaseDoc = (doc: any, idField: string = 'id'): any => {
  const converted = { ...doc };
  
  // Convert timestamps
  if (converted.createdAt) {
    converted.createdAt = convertFirebaseTimestamp(converted.createdAt);
  }
  if (converted.updatedAt) {
    converted.updatedAt = convertFirebaseTimestamp(converted.updatedAt);
  }
  if (converted.created_at) {
    converted.created_at = convertFirebaseTimestamp(converted.created_at);
  }
  if (converted.updated_at) {
    converted.updated_at = convertFirebaseTimestamp(converted.updated_at);
  }
  
  // Ensure ID field exists
  if (!converted[idField] && converted.id) {
    converted[idField] = converted.id;
  }
  
  return converted;
};

// Batch operation helper
export const batchOperation = async <T>(
  items: T[],
  operation: (item: T) => Promise<any>,
  batchSize: number = 10,
  onProgress?: (completed: number, total: number) => void
): Promise<any[]> => {
  const results: any[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchPromises = batch.map(operation);
    
    try {
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      if (onProgress) {
        onProgress(Math.min(i + batchSize, items.length), items.length);
      }
    } catch (error) {
      migrationError(`Batch operation failed for items ${i}-${i + batchSize}`, error);
      throw error;
    }
  }
  
  return results;
};

// Retry operation helper
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      migrationError(`Operation failed on attempt ${attempt}/${maxRetries}`, error);
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }
  
  throw lastError;
};

// Validate environment configuration
export const validateMigrationConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (MIGRATION_CONFIG.USE_SUPABASE_AUTH || MIGRATION_CONFIG.USE_SUPABASE_DATA) {
    if (!import.meta.env.VITE_SUPABASE_URL) {
      errors.push('VITE_SUPABASE_URL is required when using Supabase');
    }
    if (!import.meta.env.VITE_SUPABASE_ANON_KEY) {
      errors.push('VITE_SUPABASE_ANON_KEY is required when using Supabase');
    }
  }
  
  if (!MIGRATION_CONFIG.USE_SUPABASE_AUTH) {
    const firebaseVars = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_AUTH_DOMAIN',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_STORAGE_BUCKET',
      'VITE_FIREBASE_MESSAGING_SENDER_ID',
      'VITE_FIREBASE_APP_ID'
    ];
    
    firebaseVars.forEach(varName => {
      if (!import.meta.env[varName]) {
        errors.push(`${varName} is required when using Firebase`);
      }
    });
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Performance monitoring
export const trackMigrationPerformance = (operation: string, startTime: number) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  migrationLog(`Performance: ${operation} took ${duration.toFixed(2)}ms`);
  
  if (MIGRATION_CONFIG.ENABLE_ANALYTICS && typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'migration_performance', {
      operation,
      duration: Math.round(duration),
      auth_provider: getCurrentAuthProvider()
    });
  }
};
