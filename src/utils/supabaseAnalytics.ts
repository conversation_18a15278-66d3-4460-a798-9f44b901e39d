import { supabase } from '../integrations/supabase/client';
import { SupabaseUserProfile, updateUserProfile } from './supabaseAuth';

export interface StudySession {
  id: string;
  userId: string;
  subject: string;
  duration: number; // in minutes
  startTime: number; // timestamp
  endTime: number; // timestamp
  date: string; // YYYY-MM-DD format
  type: 'pomodoro' | 'stopwatch' | 'timer';
  notes?: string;
  completed: boolean;
}

export interface Subject {
  id: string;
  userId: string;
  name: string;
  color: string;
  createdAt: string;
  totalStudyTime?: number; // in minutes
}

export interface Exam {
  id: string;
  userId: string;
  name: string;
  date: string; // YYYY-MM-DD format
  totalMarks: number;
  totalMarksObtained: number;
  subjectMarks: Array<{
    subject: string;
    maxMarks: number;
    obtainedMarks: number;
  }>;
  notes?: string;
  createdAt: string;
}

export interface UserStats {
  totalStudyTime: number; // in minutes
  currentStreak: number; // days
  longestStreak: number; // days
  totalSessions: number;
  averageSessionDuration: number; // in minutes
  favoriteSubject?: string;
  weeklyGoal?: number; // in minutes
  dailyGoal?: number; // in minutes
  lastStudyDate?: string; // YYYY-MM-DD
}

// Study Sessions
export const createStudySession = async (sessionData: Omit<StudySession, 'id'>): Promise<StudySession | null> => {
  try {
    const newSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...sessionData
    };

    // Update user stats
    await updateUserStudyStats(sessionData.userId, sessionData.duration, sessionData.date);

    // Note: In the current schema, study sessions are stored in the users table as JSONB
    // We'll need to get the user profile and update the studySessions field
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('studySessions')
      .eq('id', sessionData.userId)
      .single();

    if (userError) {
      console.error('Error fetching user for study session:', userError);
      throw userError;
    }

    const currentSessions = userData.studySessions || {};
    const sessionKey = `${sessionData.date}_${newSession.id}`;
    currentSessions[sessionKey] = newSession;

    const { error } = await supabase
      .from('users')
      .update({ studySessions: currentSessions })
      .eq('id', sessionData.userId);

    if (error) {
      console.error('Error creating study session:', error);
      throw error;
    }

    return newSession;
  } catch (error) {
    console.error('Error in createStudySession:', error);
    return null;
  }
};

// Get user's study sessions
export const getUserStudySessions = async (userId: string, dateRange?: { start: string; end: string }): Promise<StudySession[]> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('studySessions')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching study sessions:', error);
      throw error;
    }

    const sessions = data.studySessions || {};
    let sessionList = Object.values(sessions) as StudySession[];

    // Filter by date range if provided
    if (dateRange) {
      sessionList = sessionList.filter(session => 
        session.date >= dateRange.start && session.date <= dateRange.end
      );
    }

    return sessionList.sort((a, b) => b.startTime - a.startTime);
  } catch (error) {
    console.error('Error in getUserStudySessions:', error);
    return [];
  }
};

// Update user study stats
export const updateUserStudyStats = async (userId: string, sessionDuration: number, sessionDate: string): Promise<boolean> => {
  try {
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('stats')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user stats:', userError);
      throw userError;
    }

    const currentStats = userData.stats || {};
    const updatedStats = {
      ...currentStats,
      totalStudyTime: (currentStats.totalStudyTime || 0) + sessionDuration,
      totalSessions: (currentStats.totalSessions || 0) + 1,
      lastStudyDate: sessionDate,
      averageSessionDuration: Math.round(((currentStats.totalStudyTime || 0) + sessionDuration) / ((currentStats.totalSessions || 0) + 1))
    };

    // Calculate streak
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    if (sessionDate === today || sessionDate === yesterday) {
      if (currentStats.lastStudyDate === yesterday && sessionDate === today) {
        updatedStats.currentStreak = (currentStats.currentStreak || 0) + 1;
      } else if (!currentStats.lastStudyDate || currentStats.lastStudyDate !== today) {
        updatedStats.currentStreak = 1;
      }
    } else {
      updatedStats.currentStreak = 1;
    }

    updatedStats.longestStreak = Math.max(updatedStats.longestStreak || 0, updatedStats.currentStreak);

    const { error } = await supabase
      .from('users')
      .update({ stats: updatedStats })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user stats:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updateUserStudyStats:', error);
    return false;
  }
};

// Subjects
export const createSubject = async (subjectData: Omit<Subject, 'id' | 'createdAt'>): Promise<Subject | null> => {
  try {
    const newSubject = {
      id: `subject_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...subjectData,
      createdAt: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('userSubjects')
      .insert(newSubject)
      .select()
      .single();

    if (error) {
      console.error('Error creating subject:', error);
      throw error;
    }

    return data as Subject;
  } catch (error) {
    console.error('Error in createSubject:', error);
    return null;
  }
};

export const getUserSubjects = async (userId: string): Promise<Subject[]> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .select('*')
      .eq('userId', userId)
      .order('createdAt', { ascending: false });

    if (error) {
      console.error('Error fetching user subjects:', error);
      throw error;
    }

    return data as Subject[];
  } catch (error) {
    console.error('Error in getUserSubjects:', error);
    return [];
  }
};

export const updateSubject = async (subjectId: string, updates: Partial<Subject>): Promise<Subject | null> => {
  try {
    const { data, error } = await supabase
      .from('userSubjects')
      .update(updates)
      .eq('id', subjectId)
      .select()
      .single();

    if (error) {
      console.error('Error updating subject:', error);
      throw error;
    }

    return data as Subject;
  } catch (error) {
    console.error('Error in updateSubject:', error);
    return null;
  }
};

export const deleteSubject = async (subjectId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('userSubjects')
      .delete()
      .eq('id', subjectId);

    if (error) {
      console.error('Error deleting subject:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteSubject:', error);
    return false;
  }
};

// Exams
export const createExam = async (examData: Omit<Exam, 'id' | 'createdAt'>): Promise<Exam | null> => {
  try {
    const newExam = {
      id: `exam_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...examData,
      createdAt: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('exams')
      .insert(newExam)
      .select()
      .single();

    if (error) {
      console.error('Error creating exam:', error);
      throw error;
    }

    return data as Exam;
  } catch (error) {
    console.error('Error in createExam:', error);
    return null;
  }
};

export const getUserExams = async (userId: string): Promise<Exam[]> => {
  try {
    const { data, error } = await supabase
      .from('exams')
      .select('*')
      .eq('userId', userId)
      .order('date', { ascending: false });

    if (error) {
      console.error('Error fetching user exams:', error);
      throw error;
    }

    return data as Exam[];
  } catch (error) {
    console.error('Error in getUserExams:', error);
    return [];
  }
};

export const updateExam = async (examId: string, updates: Partial<Exam>): Promise<Exam | null> => {
  try {
    const { data, error } = await supabase
      .from('exams')
      .update(updates)
      .eq('id', examId)
      .select()
      .single();

    if (error) {
      console.error('Error updating exam:', error);
      throw error;
    }

    return data as Exam;
  } catch (error) {
    console.error('Error in updateExam:', error);
    return null;
  }
};

export const deleteExam = async (examId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('exams')
      .delete()
      .eq('id', examId);

    if (error) {
      console.error('Error deleting exam:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteExam:', error);
    return false;
  }
};

// Analytics
export const getUserStats = async (userId: string): Promise<UserStats | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('stats')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }

    return data.stats as UserStats || null;
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return null;
  }
};

export const getStudyTimeBySubject = async (userId: string, dateRange?: { start: string; end: string }): Promise<Record<string, number>> => {
  try {
    const sessions = await getUserStudySessions(userId, dateRange);
    const subjectTime: Record<string, number> = {};

    sessions.forEach(session => {
      if (session.completed) {
        subjectTime[session.subject] = (subjectTime[session.subject] || 0) + session.duration;
      }
    });

    return subjectTime;
  } catch (error) {
    console.error('Error in getStudyTimeBySubject:', error);
    return {};
  }
};

export const getWeeklyStudyData = async (userId: string): Promise<Array<{ date: string; duration: number }>> => {
  try {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const sessions = await getUserStudySessions(userId, {
      start: startDate.toISOString().split('T')[0],
      end: endDate.toISOString().split('T')[0]
    });

    const dailyData: Record<string, number> = {};
    
    // Initialize all days with 0
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      dailyData[dateStr] = 0;
    }

    // Add session durations
    sessions.forEach(session => {
      if (session.completed) {
        dailyData[session.date] = (dailyData[session.date] || 0) + session.duration;
      }
    });

    return Object.entries(dailyData).map(([date, duration]) => ({ date, duration }));
  } catch (error) {
    console.error('Error in getWeeklyStudyData:', error);
    return [];
  }
};
