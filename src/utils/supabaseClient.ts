import { supabase } from '../integrations/supabase/client';

// Re-export all Supabase utilities for easy access
export * from './supabaseAuth';
export * from './supabaseUserDataManager';
export * from './supabaseAIChats';
export * from './supabaseGroups';
export * from './supabaseTodos';
export * from './supabaseAnalytics';

// Main Supabase client export
export { supabase };

// AI Chats operations
export const getAIChatsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('ai_chats')
    .select('*')
    .eq('user_id', userId)
    .order('updated_at', { ascending: false });

  if (error) {
    console.error('Error fetching AI chats from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveAIChatToSupabase = async (chatData: any) => {
  const { data, error } = await supabase
    .from('ai_chats')
    .upsert(chatData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving AI chat to Supabase:', error);
    throw error;
  }

  return data;
};

// Groups operations
export const getUserGroupsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('groups')
    .select('*')
    .contains('members', [userId])
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching groups from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveGroupToSupabase = async (groupData: any) => {
  const { data, error } = await supabase
    .from('groups')
    .upsert(groupData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving group to Supabase:', error);
    throw error;
  }

  return data;
};

// Messages operations
export const getGroupMessagesFromSupabase = async (groupId: string) => {
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .eq('group_id', groupId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Error fetching messages from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveMessageToSupabase = async (messageData: any) => {
  const { data, error } = await supabase
    .from('messages')
    .insert(messageData)
    .select()
    .single();

  if (error) {
    console.error('Error saving message to Supabase:', error);
    throw error;
  }

  return data;
};

// Todos operations
export const getUserTodosFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('todos')
    .select('*')
    .eq('created_by', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching todos from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveTodoToSupabase = async (todoData: any) => {
  const { data, error } = await supabase
    .from('todos')
    .upsert(todoData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving todo to Supabase:', error);
    throw error;
  }

  return data;
};

// User Subjects operations
export const getUserSubjectsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_subjects')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching subjects from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveSubjectToSupabase = async (subjectData: any) => {
  const { data, error } = await supabase
    .from('user_subjects')
    .upsert(subjectData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving subject to Supabase:', error);
    throw error;
  }

  return data;
};

// Exams operations
export const getUserExamsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('exams')
    .select('*')
    .eq('user_id', userId)
    .order('date', { ascending: true });

  if (error) {
    console.error('Error fetching exams from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveExamToSupabase = async (examData: any) => {
  const { data, error } = await supabase
    .from('exams')
    .upsert(examData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving exam to Supabase:', error);
    throw error;
  }

  return data;
};

// Study Sessions operations
export const getUserStudySessionsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('study_sessions')
    .select('*')
    .eq('user_id', userId)
    .order('start_time', { ascending: false });

  if (error) {
    console.error('Error fetching study sessions from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveStudySessionToSupabase = async (sessionData: any) => {
  const { data, error } = await supabase
    .from('study_sessions')
    .upsert(sessionData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving study session to Supabase:', error);
    throw error;
  }

  return data;
};

// Mock Tests operations
export const getUserMockTestsFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('mock_tests')
    .select('*')
    .eq('user_id', userId)
    .order('test_date', { ascending: false });

  if (error) {
    console.error('Error fetching mock tests from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveMockTestToSupabase = async (testData: any) => {
  const { data, error } = await supabase
    .from('mock_tests')
    .upsert(testData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving mock test to Supabase:', error);
    throw error;
  }

  return data;
};

// User Profile operations
export const getUserProfileFromSupabase = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
    console.error('Error fetching user profile from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveUserProfileToSupabase = async (userData: any) => {
  const { data, error } = await supabase
    .from('users')
    .upsert(userData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error saving user profile to Supabase:', error);
    throw error;
  }

  return data;
};

// Chat Comments operations
export const getChatCommentsFromSupabase = async (chatId: string) => {
  const { data, error } = await supabase
    .from('chat_comments')
    .select('*')
    .eq('chat_id', chatId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Error fetching chat comments from Supabase:', error);
    throw error;
  }

  return data;
};

export const saveChatCommentToSupabase = async (commentData: any) => {
  const { data, error } = await supabase
    .from('chat_comments')
    .insert(commentData)
    .select()
    .single();

  if (error) {
    console.error('Error saving chat comment to Supabase:', error);
    throw error;
  }

  return data;
};

// Real-time subscriptions
export const subscribeToUserData = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('user-data-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'users',
        filter: `id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToGroupMessages = (groupId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`group-${groupId}-messages`)
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'messages',
        filter: `group_id=eq.${groupId}`
      }, 
      callback
    )
    .subscribe();
};
