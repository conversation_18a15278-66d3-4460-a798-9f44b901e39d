import { supabase } from '../integrations/supabase/client';
import { ChatMessage } from '../types/qa';
import { Comment } from '../types/chat';

export interface AIChat {
  id: string;
  userId: string;
  createdBy?: string;
  title?: string;
  slug?: string;
  messages: ChatMessage[];
  createdAt: any;
  updatedAt: any;
  isPublic?: boolean;
  viewCount?: number;
  preview?: string;
  comments: Comment[];
  isPinned?: boolean;
  isStarred?: boolean;
  status?: string;
  tags?: string[];
}

// Create a new AI chat
export const createAIChat = async (userId: string, initialMessage?: ChatMessage): Promise<AIChat | null> => {
  try {
    const chatData = {
      id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      createdBy: userId,
      title: initialMessage?.content?.substring(0, 50) || 'New Chat',
      messages: initialMessage ? [initialMessage] : [],
      createdAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 },
      updatedAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 },
      isPublic: false,
      viewCount: 0,
      comments: [],
      isPinned: false,
      isStarred: false,
      status: 'approved',
      tags: []
    };

    const { data, error } = await supabase
      .from('aiChats')
      .insert(chatData)
      .select()
      .single();

    if (error) {
      console.error('Error creating AI chat:', error);
      throw error;
    }

    return data as AIChat;
  } catch (error) {
    console.error('Error in createAIChat:', error);
    return null;
  }
};

// Get AI chat by ID
export const getAIChat = async (chatId: string): Promise<AIChat | null> => {
  try {
    const { data, error } = await supabase
      .from('aiChats')
      .select('*')
      .eq('id', chatId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching AI chat:', error);
      throw error;
    }

    return data as AIChat | null;
  } catch (error) {
    console.error('Error in getAIChat:', error);
    return null;
  }
};

// Get user's AI chats
export const getUserAIChats = async (userId: string): Promise<AIChat[]> => {
  try {
    const { data, error } = await supabase
      .from('aiChats')
      .select('*')
      .eq('userId', userId)
      .order('updatedAt', { ascending: false });

    if (error) {
      console.error('Error fetching user AI chats:', error);
      throw error;
    }

    return data as AIChat[];
  } catch (error) {
    console.error('Error in getUserAIChats:', error);
    return [];
  }
};

// Update AI chat
export const updateAIChat = async (chatId: string, updates: Partial<AIChat>): Promise<AIChat | null> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 }
    };

    const { data, error } = await supabase
      .from('aiChats')
      .update(updateData)
      .eq('id', chatId)
      .select()
      .single();

    if (error) {
      console.error('Error updating AI chat:', error);
      throw error;
    }

    return data as AIChat;
  } catch (error) {
    console.error('Error in updateAIChat:', error);
    return null;
  }
};

// Add message to AI chat
export const addMessageToAIChat = async (chatId: string, message: ChatMessage): Promise<boolean> => {
  try {
    // First get the current chat
    const currentChat = await getAIChat(chatId);
    if (!currentChat) {
      throw new Error('Chat not found');
    }

    // Add the new message
    const updatedMessages = [...currentChat.messages, message];
    
    // Update the chat with new message
    const updated = await updateAIChat(chatId, {
      messages: updatedMessages,
      title: currentChat.title || message.content?.substring(0, 50) || 'New Chat'
    });

    return updated !== null;
  } catch (error) {
    console.error('Error adding message to AI chat:', error);
    return false;
  }
};

// Delete AI chat
export const deleteAIChat = async (chatId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('aiChats')
      .delete()
      .eq('id', chatId);

    if (error) {
      console.error('Error deleting AI chat:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteAIChat:', error);
    return false;
  }
};

// Get public AI chats
export const getPublicAIChats = async (limit: number = 20): Promise<AIChat[]> => {
  try {
    const { data, error } = await supabase
      .from('aiChats')
      .select('*')
      .eq('isPublic', true)
      .eq('status', 'approved')
      .order('updatedAt', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching public AI chats:', error);
      throw error;
    }

    return data as AIChat[];
  } catch (error) {
    console.error('Error in getPublicAIChats:', error);
    return [];
  }
};

// Share AI chat (make public)
export const shareAIChat = async (chatId: string, isPublic: boolean = true): Promise<boolean> => {
  try {
    const slug = isPublic ? `chat-${Date.now()}-${Math.random().toString(36).substr(2, 6)}` : null;
    
    const updated = await updateAIChat(chatId, {
      isPublic,
      slug,
      status: 'approved'
    });

    return updated !== null;
  } catch (error) {
    console.error('Error sharing AI chat:', error);
    return false;
  }
};

// Add comment to AI chat
export const addCommentToAIChat = async (chatId: string, comment: Comment): Promise<boolean> => {
  try {
    const currentChat = await getAIChat(chatId);
    if (!currentChat) {
      throw new Error('Chat not found');
    }

    const updatedComments = [...currentChat.comments, comment];
    
    const updated = await updateAIChat(chatId, {
      comments: updatedComments
    });

    return updated !== null;
  } catch (error) {
    console.error('Error adding comment to AI chat:', error);
    return false;
  }
};

// Subscribe to AI chat changes (real-time)
export const subscribeToAIChat = (chatId: string, callback: (chat: AIChat | null) => void) => {
  const subscription = supabase
    .channel(`ai-chat-${chatId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'aiChats',
        filter: `id=eq.${chatId}`
      },
      (payload) => {
        console.log('AI chat change:', payload);
        if (payload.eventType === 'DELETE') {
          callback(null);
        } else {
          callback(payload.new as AIChat);
        }
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

// Subscribe to user's AI chats
export const subscribeToUserAIChats = (userId: string, callback: (chats: AIChat[]) => void) => {
  const subscription = supabase
    .channel(`user-ai-chats-${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'aiChats',
        filter: `userId=eq.${userId}`
      },
      async () => {
        // Fetch updated chats when changes occur
        const chats = await getUserAIChats(userId);
        callback(chats);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

// Pin/Unpin AI chat
export const togglePinAIChat = async (chatId: string, isPinned: boolean): Promise<boolean> => {
  try {
    const updated = await updateAIChat(chatId, { isPinned });
    return updated !== null;
  } catch (error) {
    console.error('Error toggling pin AI chat:', error);
    return false;
  }
};

// Star/Unstar AI chat
export const toggleStarAIChat = async (chatId: string, isStarred: boolean): Promise<boolean> => {
  try {
    const updated = await updateAIChat(chatId, { isStarred });
    return updated !== null;
  } catch (error) {
    console.error('Error toggling star AI chat:', error);
    return false;
  }
};
