import { supabase } from '../integrations/supabase/client';

export interface ChatGroup {
  id: string;
  name: string;
  description?: string;
  members: string[];
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  isPublic?: boolean;
  inviteCode?: string;
}

export interface Message {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  senderPhotoURL?: string;
  timestamp: number;
  groupId: string;
  type?: 'text' | 'image' | 'file';
  metadata?: any;
}

// Create a new group
export const createGroup = async (groupData: Omit<ChatGroup, 'id' | 'createdAt' | 'updatedAt'>): Promise<ChatGroup | null> => {
  try {
    const newGroup = {
      id: `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...groupData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      inviteCode: Math.random().toString(36).substr(2, 8).toUpperCase()
    };

    const { data, error } = await supabase
      .from('groups')
      .insert(newGroup)
      .select()
      .single();

    if (error) {
      console.error('Error creating group:', error);
      throw error;
    }

    return data as ChatGroup;
  } catch (error) {
    console.error('Error in createGroup:', error);
    return null;
  }
};

// Get group by ID
export const getGroup = async (groupId: string): Promise<ChatGroup | null> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('id', groupId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching group:', error);
      throw error;
    }

    return data as ChatGroup | null;
  } catch (error) {
    console.error('Error in getGroup:', error);
    return null;
  }
};

// Get user's groups
export const getUserGroups = async (userId: string): Promise<ChatGroup[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .contains('members', [userId])
      .order('updatedAt', { ascending: false });

    if (error) {
      console.error('Error fetching user groups:', error);
      throw error;
    }

    return data as ChatGroup[];
  } catch (error) {
    console.error('Error in getUserGroups:', error);
    return [];
  }
};

// Update group
export const updateGroup = async (groupId: string, updates: Partial<ChatGroup>): Promise<ChatGroup | null> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('groups')
      .update(updateData)
      .eq('id', groupId)
      .select()
      .single();

    if (error) {
      console.error('Error updating group:', error);
      throw error;
    }

    return data as ChatGroup;
  } catch (error) {
    console.error('Error in updateGroup:', error);
    return null;
  }
};

// Add member to group
export const addMemberToGroup = async (groupId: string, userId: string): Promise<boolean> => {
  try {
    const group = await getGroup(groupId);
    if (!group) {
      throw new Error('Group not found');
    }

    if (group.members.includes(userId)) {
      return true; // Already a member
    }

    const updatedMembers = [...group.members, userId];
    const updated = await updateGroup(groupId, { members: updatedMembers });
    
    return updated !== null;
  } catch (error) {
    console.error('Error adding member to group:', error);
    return false;
  }
};

// Remove member from group
export const removeMemberFromGroup = async (groupId: string, userId: string): Promise<boolean> => {
  try {
    const group = await getGroup(groupId);
    if (!group) {
      throw new Error('Group not found');
    }

    const updatedMembers = group.members.filter(id => id !== userId);
    const updated = await updateGroup(groupId, { members: updatedMembers });
    
    return updated !== null;
  } catch (error) {
    console.error('Error removing member from group:', error);
    return false;
  }
};

// Delete group
export const deleteGroup = async (groupId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('groups')
      .delete()
      .eq('id', groupId);

    if (error) {
      console.error('Error deleting group:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteGroup:', error);
    return false;
  }
};

// Find group by invite code
export const findGroupByInviteCode = async (inviteCode: string): Promise<ChatGroup | null> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('inviteCode', inviteCode)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error finding group by invite code:', error);
      throw error;
    }

    return data as ChatGroup | null;
  } catch (error) {
    console.error('Error in findGroupByInviteCode:', error);
    return null;
  }
};

// Subscribe to group changes
export const subscribeToGroup = (groupId: string, callback: (group: ChatGroup | null) => void) => {
  const subscription = supabase
    .channel(`group-${groupId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'groups',
        filter: `id=eq.${groupId}`
      },
      (payload) => {
        console.log('Group change:', payload);
        if (payload.eventType === 'DELETE') {
          callback(null);
        } else {
          callback(payload.new as ChatGroup);
        }
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

// Subscribe to user's groups
export const subscribeToUserGroups = (userId: string, callback: (groups: ChatGroup[]) => void) => {
  const subscription = supabase
    .channel(`user-groups-${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'groups'
      },
      async () => {
        // Fetch updated groups when changes occur
        const groups = await getUserGroups(userId);
        callback(groups);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

// Chat Messages for Groups (stored in separate table/collection)
// Note: In the current schema, we don't have a separate messages table
// Messages would be stored in the chats table or we'd need to create a messages table

// For now, let's create a simple message system using the existing structure
export const sendMessageToGroup = async (groupId: string, message: Omit<Message, 'id' | 'timestamp'>): Promise<boolean> => {
  try {
    // In a real implementation, you'd want a separate messages table
    // For now, we'll update the group's updatedAt to indicate activity
    const updated = await updateGroup(groupId, {
      updatedAt: new Date().toISOString()
    });

    // You could also store messages in a JSONB field or create a separate table
    // This is a simplified implementation
    
    return updated !== null;
  } catch (error) {
    console.error('Error sending message to group:', error);
    return false;
  }
};

// Get public groups
export const getPublicGroups = async (limit: number = 20): Promise<ChatGroup[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('isPublic', true)
      .order('createdAt', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching public groups:', error);
      throw error;
    }

    return data as ChatGroup[];
  } catch (error) {
    console.error('Error in getPublicGroups:', error);
    return [];
  }
};
