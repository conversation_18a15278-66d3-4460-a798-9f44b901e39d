import { User as SupabaseUser } from '@supabase/supabase-js';
import { useUserStore } from '../stores/userStore';
import { 
  SupabaseUserProfile, 
  getUserProfileById, 
  createUserProfile, 
  updateUserProfile,
  linkUserToFirebaseData 
} from './supabaseAuth';

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Fetches the user profile from Supabase or cache.
 * @param userId The Supabase user ID to fetch.
 * @param forceRefresh Whether to bypass the cache and fetch fresh data.
 * @returns The user profile or null if not found.
 */
export const getSupabaseUserProfile = async (
  userId: string,
  forceRefresh: boolean = false
): Promise<SupabaseUserProfile | null> => {
  const { userProfile, lastFetched, setUserProfile, setLastFetched } = useUserStore.getState();

  // Check cache first (convert Firebase UserProfile to Supabase format for compatibility)
  if (userProfile && userProfile.uid === userId && lastFetched && !forceRefresh) {
    const now = Date.now();
    if (now - lastFetched < CACHE_DURATION) {
      console.log('Returning cached user profile for:', userId);
      // Convert Firebase format to Supabase format
      const supabaseProfile: SupabaseUserProfile = {
        id: userId,
        email: userProfile.email || '',
        username: userProfile.username || '',
        displayName: userProfile.displayName,
        photoURL: userProfile.photoURL,
        created_at: userProfile.createdAt,
        updated_at: userProfile.updatedAt,
        firebaseUid: userProfile.uid,
        ...userProfile
      };
      return supabaseProfile;
    }
  }

  // Fetch from Supabase
  try {
    console.log('Fetching user profile from Supabase for:', userId);
    const profileData = await getUserProfileById(userId);

    if (profileData) {
      // Convert Supabase format to Firebase format for store compatibility
      const firebaseCompatibleProfile = {
        uid: profileData.id,
        email: profileData.email,
        displayName: profileData.displayName,
        photoURL: profileData.photoURL,
        username: profileData.username,
        createdAt: profileData.created_at,
        updatedAt: profileData.updated_at,
        ...profileData
      };
      
      setUserProfile(firebaseCompatibleProfile);
      setLastFetched(Date.now());
      return profileData;
    } else {
      console.warn('No user profile found in Supabase for:', userId);
      setUserProfile(null);
      setLastFetched(Date.now());
      return null;
    }
  } catch (error) {
    console.error('Error fetching user profile from Supabase:', error);
    return null;
  }
};

/**
 * Creates or updates the user profile in Supabase.
 * @param user The Supabase Auth User object.
 * @param additionalData Any additional data to merge into the profile.
 */
export const updateSupabaseUserProfile = async (
  user: SupabaseUser,
  additionalData: Partial<SupabaseUserProfile> = {}
): Promise<SupabaseUserProfile | null> => {
  if (!user || !user.id) {
    console.error('User object is invalid for updating profile.');
    return null;
  }

  try {
    // Check if profile already exists
    const existingProfile = await getUserProfileById(user.id);
    
    let updatedProfile: SupabaseUserProfile | null;
    
    if (existingProfile) {
      // Update existing profile
      updatedProfile = await updateUserProfile(user.id, {
        email: user.email!,
        displayName: user.user_metadata?.full_name || existingProfile.displayName,
        photoURL: user.user_metadata?.avatar_url || existingProfile.photoURL,
        lastLogin: new Date().toISOString(),
        ...additionalData,
      });
    } else {
      // Create new profile
      updatedProfile = await createUserProfile(user, {
        username: additionalData.username || user.email?.split('@')[0] || `user_${user.id.substring(0, 5)}`,
        ...additionalData,
      });
    }

    if (updatedProfile) {
      console.log('User profile updated in Supabase for:', user.id);
      
      // Update the local cache immediately
      const refreshedProfile = await getSupabaseUserProfile(user.id, true);
      return refreshedProfile;
    }

    return null;
  } catch (error) {
    console.error('Error updating user profile in Supabase:', error);
    return null;
  }
};

/**
 * Handles user authentication and data continuity.
 * This function is called when auth state changes and handles linking to existing Firebase data.
 */
export const handleSupabaseUserAuthentication = async (
  authUser: SupabaseUser | null,
  usernameForLinking?: string
): Promise<SupabaseUserProfile | null> => {
  if (!authUser) {
    // Clear user data when signed out
    const { setUserProfile } = useUserStore.getState();
    setUserProfile(null);
    return null;
  }

  try {
    let profile = await getSupabaseUserProfile(authUser.id);
    
    if (!profile) {
      // If profile doesn't exist, check if we need to link to existing Firebase data
      if (usernameForLinking) {
        console.log(`Attempting to link user ${authUser.id} to existing data for username: ${usernameForLinking}`);
        profile = await linkUserToFirebaseData(authUser, usernameForLinking);
      } else {
        // Create a basic profile for new users
        console.log(`Profile not found for ${authUser.id}, creating basic profile.`);
        profile = await updateSupabaseUserProfile(authUser, {
          username: authUser.email?.split('@')[0] || `user_${authUser.id.substring(0, 5)}`
        });
      }
    } else {
      // Update existing profile with latest auth info
      profile = await updateSupabaseUserProfile(authUser);
    }

    return profile;
  } catch (error) {
    console.error('Error handling user authentication:', error);
    return null;
  }
};

/**
 * Migration helper: Find user by Firebase UID and link to Supabase user
 */
export const linkFirebaseUserToSupabase = async (
  supabaseUser: SupabaseUser,
  firebaseUid: string
): Promise<SupabaseUserProfile | null> => {
  try {
    // This would be used during migration to link existing Firebase users
    // For now, we'll create a profile with the Firebase UID stored
    const profile = await updateSupabaseUserProfile(supabaseUser, {
      firebaseUid,
      username: supabaseUser.email?.split('@')[0] || `user_${supabaseUser.id.substring(0, 5)}`
    });

    console.log(`Linked Firebase user ${firebaseUid} to Supabase user ${supabaseUser.id}`);
    return profile;
  } catch (error) {
    console.error('Error linking Firebase user to Supabase:', error);
    return null;
  }
};

/**
 * Check if user has completed profile setup (has username)
 */
export const checkSupabaseUserProfile = async (userId: string): Promise<boolean> => {
  try {
    const profile = await getUserProfileById(userId);
    return Boolean(profile?.username);
  } catch (error) {
    console.error('Error checking user profile:', error);
    return false;
  }
};

/**
 * Update user's last active timestamp
 */
export const updateUserLastActive = async (userId: string): Promise<void> => {
  try {
    await updateUserProfile(userId, {
      lastLogin: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error updating user last active:', error);
  }
};
