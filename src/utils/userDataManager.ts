import { doc, getDoc, serverTimestamp, setDoc } from 'firebase/firestore';
import { User } from 'firebase/auth';
import { db as firestore } from './firebase'; // Assuming firebase.ts exports firestore instance
import { useUserStore } from '../stores/userStore';
import { UserProfile } from '../types/user';

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Fetches the user profile from Firestore or cache.
 * @param userId The ID of the user to fetch.
 * @param forceRefresh Whether to bypass the cache and fetch fresh data.
 * @returns The user profile or null if not found.
 */
export const getUserProfile = async (
  userId: string,
  forceRefresh: boolean = false
): Promise<UserProfile | null> => {
  const { userProfile, lastFetched, setUserProfile, setLastFetched } = useUserStore.getState();

  // Check cache first
  if (userProfile && userProfile.uid === userId && lastFetched && !forceRefresh) {
    const now = Date.now();
    if (now - lastFetched < CACHE_DURATION) {
      console.log('Returning cached user profile for:', userId);
      return userProfile;
    }
  }

  // Fetch from Firestore
  try {
    console.log('Fetching user profile from Firestore for:', userId);
    const userDocRef = doc(firestore, 'users', userId);
    const userDocSnap = await getDoc(userDocRef);

    if (userDocSnap.exists()) {
      const profileData = userDocSnap.data() as UserProfile;
      // Ensure uid is part of the profile, useful if 'users' collection doesn't store it explicitly
      const fullProfile: UserProfile = { uid: userId, ...profileData };
      setUserProfile(fullProfile);
      setLastFetched(Date.now());
      return fullProfile;
    } else {
      console.warn('No user profile found in Firestore for:', userId);
      setUserProfile(null); // Clear profile if not found
      setLastFetched(Date.now()); // Update last fetched even if not found to prevent rapid re-fetching
      return null;
    }
  } catch (error) {
    console.error('Error fetching user profile:', error);
    // Optionally, clear cache on error or implement more sophisticated error handling
    // setUserProfile(null);
    // setLastFetched(null);
    return null; // Or re-throw error depending on desired behavior
  }
};

/**
 * Creates or updates the user profile in Firestore.
 * Typically called after user signs up or when profile info needs an update.
 * @param user The Firebase Auth User object.
 * @param additionalData Any additional data to merge into the profile.
 */
export const updateUserProfileInFirestore = async (
  user: User,
  additionalData: Partial<UserProfile> = {}
): Promise<UserProfile | null> => {
  if (!user || !user.uid) {
    console.error('User object is invalid for updating profile.');
    return null;
  }

  const userDocRef = doc(firestore, 'users', user.uid);
  const profileData: Partial<UserProfile> = {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    updatedAt: serverTimestamp(),
    ...additionalData,
  };

  try {
    // Check if document exists to set createdAt only once
    const docSnap = await getDoc(userDocRef);
    if (!docSnap.exists()) {
      profileData.createdAt = serverTimestamp();
    }

    await setDoc(userDocRef, profileData, { merge: true });
    console.log('User profile updated in Firestore for:', user.uid);
    
    // Update the local cache immediately
    const updatedProfile = await getUserProfile(user.uid, true); // Force refresh to get merged data
    return updatedProfile;

  } catch (error) {
    console.error('Error updating user profile in Firestore:', error);
    return null;
  }
};

// Example of how to integrate with AuthContext or similar
// This function would be called when auth state changes
export const handleUserAuthentication = async (authUser: User | null) => {
  if (authUser) {
    let profile = await getUserProfile(authUser.uid);
    if (!profile) {
      // If profile doesn't exist, create a basic one
      // This might happen for new users or if data was cleared
      console.log(`Profile not found for ${authUser.uid}, creating basic profile.`);
      profile = await updateUserProfileInFirestore(authUser, { username: authUser.displayName || `user_${authUser.uid.substring(0,5)}` });
    }
    // At this point, userStore should have the latest profile
  } else {
    // User logged out, clear the store
    useUserStore.getState().setUser(null);
    useUserStore.getState().setUserProfile(null);
    useUserStore.getState().setLastFetched(null);
  }
};
