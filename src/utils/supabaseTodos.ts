import { supabase } from '../integrations/supabase/client';

export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  updatedAt: number;
  dueDate?: number;
  assignedTo?: string;
  assignedToName?: string;
  assignedToPhotoUrl?: string;
  createdBy: string;
  groupId?: string;
  columnId: string;
  completed?: boolean;
  tags?: string[];
}

export interface TodoColumn {
  id: string;
  title: string;
  taskIds: string[];
  color?: string;
}

export interface TodoBoard {
  columns: Record<string, TodoColumn>;
  columnOrder: string[];
}

// Create a new todo
export const createTodo = async (todoData: Omit<TodoItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<TodoItem | null> => {
  try {
    const newTodo = {
      id: `todo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...todoData,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      columnId: todoData.columnId || 'column-1'
    };

    const { data, error } = await supabase
      .from('todos')
      .insert(newTodo)
      .select()
      .single();

    if (error) {
      console.error('Error creating todo:', error);
      throw error;
    }

    return data as TodoItem;
  } catch (error) {
    console.error('Error in createTodo:', error);
    return null;
  }
};

// Get todo by ID
export const getTodo = async (todoId: string): Promise<TodoItem | null> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('id', todoId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching todo:', error);
      throw error;
    }

    return data as TodoItem | null;
  } catch (error) {
    console.error('Error in getTodo:', error);
    return null;
  }
};

// Get user's todos
export const getUserTodos = async (userId: string): Promise<TodoItem[]> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('createdBy', userId)
      .order('updatedAt', { ascending: false });

    if (error) {
      console.error('Error fetching user todos:', error);
      throw error;
    }

    return data as TodoItem[];
  } catch (error) {
    console.error('Error in getUserTodos:', error);
    return [];
  }
};

// Get group todos
export const getGroupTodos = async (groupId: string): Promise<TodoItem[]> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('groupId', groupId)
      .order('updatedAt', { ascending: false });

    if (error) {
      console.error('Error fetching group todos:', error);
      throw error;
    }

    return data as TodoItem[];
  } catch (error) {
    console.error('Error in getGroupTodos:', error);
    return [];
  }
};

// Update todo
export const updateTodo = async (todoId: string, updates: Partial<TodoItem>): Promise<TodoItem | null> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: Date.now()
    };

    const { data, error } = await supabase
      .from('todos')
      .update(updateData)
      .eq('id', todoId)
      .select()
      .single();

    if (error) {
      console.error('Error updating todo:', error);
      throw error;
    }

    return data as TodoItem;
  } catch (error) {
    console.error('Error in updateTodo:', error);
    return null;
  }
};

// Delete todo
export const deleteTodo = async (todoId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('todos')
      .delete()
      .eq('id', todoId);

    if (error) {
      console.error('Error deleting todo:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteTodo:', error);
    return false;
  }
};

// Move todo to different column
export const moveTodo = async (todoId: string, newColumnId: string): Promise<boolean> => {
  try {
    const updated = await updateTodo(todoId, { columnId: newColumnId });
    return updated !== null;
  } catch (error) {
    console.error('Error moving todo:', error);
    return false;
  }
};

// Assign todo to user
export const assignTodo = async (todoId: string, assignedTo: string, assignedToName?: string, assignedToPhotoUrl?: string): Promise<boolean> => {
  try {
    const updated = await updateTodo(todoId, {
      assignedTo,
      assignedToName,
      assignedToPhotoUrl
    });
    return updated !== null;
  } catch (error) {
    console.error('Error assigning todo:', error);
    return false;
  }
};

// Mark todo as completed
export const completeTodo = async (todoId: string, completed: boolean = true): Promise<boolean> => {
  try {
    const updated = await updateTodo(todoId, { completed });
    return updated !== null;
  } catch (error) {
    console.error('Error completing todo:', error);
    return false;
  }
};

// Set todo due date
export const setTodoDueDate = async (todoId: string, dueDate: number): Promise<boolean> => {
  try {
    const updated = await updateTodo(todoId, { dueDate });
    return updated !== null;
  } catch (error) {
    console.error('Error setting todo due date:', error);
    return false;
  }
};

// Set todo priority
export const setTodoPriority = async (todoId: string, priority: 'low' | 'medium' | 'high'): Promise<boolean> => {
  try {
    const updated = await updateTodo(todoId, { priority });
    return updated !== null;
  } catch (error) {
    console.error('Error setting todo priority:', error);
    return false;
  }
};

// Subscribe to user todos
export const subscribeToUserTodos = (userId: string, callback: (todos: TodoItem[]) => void) => {
  const subscription = supabase
    .channel(`user-todos-${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'todos',
        filter: `createdBy=eq.${userId}`
      },
      async () => {
        // Fetch updated todos when changes occur
        const todos = await getUserTodos(userId);
        callback(todos);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

// Subscribe to group todos
export const subscribeToGroupTodos = (groupId: string, callback: (todos: TodoItem[]) => void) => {
  const subscription = supabase
    .channel(`group-todos-${groupId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'todos',
        filter: `groupId=eq.${groupId}`
      },
      async () => {
        // Fetch updated todos when changes occur
        const todos = await getGroupTodos(groupId);
        callback(todos);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

// Get todos by column
export const getTodosByColumn = async (userId: string, columnId: string): Promise<TodoItem[]> => {
  try {
    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('createdBy', userId)
      .eq('columnId', columnId)
      .order('updatedAt', { ascending: false });

    if (error) {
      console.error('Error fetching todos by column:', error);
      throw error;
    }

    return data as TodoItem[];
  } catch (error) {
    console.error('Error in getTodosByColumn:', error);
    return [];
  }
};

// Get overdue todos
export const getOverdueTodos = async (userId: string): Promise<TodoItem[]> => {
  try {
    const now = Date.now();
    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('createdBy', userId)
      .lt('dueDate', now)
      .eq('completed', false)
      .order('dueDate', { ascending: true });

    if (error) {
      console.error('Error fetching overdue todos:', error);
      throw error;
    }

    return data as TodoItem[];
  } catch (error) {
    console.error('Error in getOverdueTodos:', error);
    return [];
  }
};

// Get todos due today
export const getTodosDueToday = async (userId: string): Promise<TodoItem[]> => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime();
    const endOfDay = startOfDay + 24 * 60 * 60 * 1000 - 1;

    const { data, error } = await supabase
      .from('todos')
      .select('*')
      .eq('createdBy', userId)
      .gte('dueDate', startOfDay)
      .lte('dueDate', endOfDay)
      .eq('completed', false)
      .order('dueDate', { ascending: true });

    if (error) {
      console.error('Error fetching todos due today:', error);
      throw error;
    }

    return data as TodoItem[];
  } catch (error) {
    console.error('Error in getTodosDueToday:', error);
    return [];
  }
};
