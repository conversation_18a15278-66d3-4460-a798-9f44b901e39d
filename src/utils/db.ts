import { 
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  onSnapshot
} from 'firebase/firestore';
import { db } from './firebase';
import { Message, ChatGroup } from '../stores/chatStore';
import { UserProgress } from '../stores/gamificationStore';

// Study Sessions
export interface StudySession {
  id?: string;
  userId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  subject?: string;
  notes?: string;
}

export const startStudySession = async (userId: string, subject?: string): Promise<string> => {
  const session: StudySession = {
    userId,
    startTime: Date.now(),
    subject
  };
  const docRef = await addDoc(collection(db, 'studySessions'), session);
  return docRef.id;
};

export const endStudySession = async (sessionId: string) => {
  const endTime = Date.now();
  const sessionRef = doc(db, 'studySessions', sessionId);
  const sessionDoc = await getDoc(sessionRef);
  
  if (sessionDoc.exists()) {
    const session = sessionDoc.data() as StudySession;
    const duration = (endTime - session.startTime) / (1000 * 60 * 60); // Convert to hours
    await updateDoc(sessionRef, {
      endTime,
      duration
    });
    return duration;
  }
  return 0;
};

// Groups
export const createGroup = async (group: Omit<ChatGroup, 'id'>): Promise<string> => {
  console.log('Creating group with data:', group);
  
  // Ensure isPublic is explicitly set
  const groupData = {
    ...group,
    isPublic: group.isPublic === undefined ? false : group.isPublic
  };
  
  const docRef = await addDoc(collection(db, 'groups'), groupData);
  
  // Verify the group was created correctly
  const createdDoc = await getDoc(docRef);
  console.log('Created group data:', createdDoc.data());
  
  return docRef.id;
};

export const updateGroup = async (groupId: string, updates: Partial<Omit<ChatGroup, 'id'>>): Promise<void> => {
  console.log(`Updating group ${groupId} with:`, updates);
  const groupRef = doc(db, 'groups', groupId);
  
  // Ensure we're updating with the exact fields provided
  await updateDoc(groupRef, updates);
  
  // Verify the update was successful
  const updatedDoc = await getDoc(groupRef);
  console.log(`Group ${groupId} after update:`, updatedDoc.data());
};

export const getGroup = async (groupId: string): Promise<ChatGroup | null> => {
  const groupRef = doc(db, 'groups', groupId);
  const groupDoc = await getDoc(groupRef);
  if (groupDoc.exists()) {
    return { id: groupDoc.id, ...groupDoc.data() } as ChatGroup;
  }
  return null;
};

export const getUserGroups = async (userId: string): Promise<ChatGroup[]> => {
  const q = query(
    collection(db, 'groups'),
    where('members', 'array-contains', userId),
    orderBy('createdAt', 'desc')
  );
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as ChatGroup);
};

export const subscribeToGroupMessages = (groupId: string, callback: (messages: Message[]) => void) => {
  const q = query(
    collection(db, `groups/${groupId}/messages`),
    orderBy('timestamp', 'asc')
  );
  return onSnapshot(q, (snapshot) => {
    const messages = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Message);
    callback(messages);
  });
};

export const sendMessage = async (message: Omit<Message, 'id'>) => {
  await addDoc(collection(db, `groups/${message.groupId}/messages`), message);
};

// Tasks (Kanban)
export interface Task {
  id?: string;
  title: string;
  description?: string;
  status: 'todo' | 'inProgress' | 'done';
  userId: string;
  groupId?: string;
  createdAt: number;
  updatedAt: number;
  dueDate?: number;
  priority?: 'low' | 'medium' | 'high';
}

export const createTask = async (task: Omit<Task, 'id'>): Promise<string> => {
  const docRef = await addDoc(collection(db, 'tasks'), task);
  return docRef.id;
};

export const updateTask = async (taskId: string, updates: Partial<Task>) => {
  const taskRef = doc(db, 'tasks', taskId);
  await updateDoc(taskRef, { ...updates, updatedAt: Date.now() });
};

export const deleteTask = async (taskId: string) => {
  await deleteDoc(doc(db, 'tasks', taskId));
};

export const getUserTasks = async (userId: string): Promise<Task[]> => {
  const q = query(
    collection(db, 'tasks'),
    where('userId', '==', userId),
    orderBy('createdAt', 'desc')
  );
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Task);
};

export const getGroupTasks = async (groupId: string): Promise<Task[]> => {
  const q = query(
    collection(db, 'tasks'),
    where('groupId', '==', groupId),
    orderBy('createdAt', 'desc')
  );
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Task);
};

// User Progress
export const updateUserProgress = async (userId: string, progress: Partial<UserProgress>) => {
  const userRef = doc(db, 'users', userId);
  await updateDoc(userRef, { progress });
};

export const getUserProgress = async (userId: string): Promise<UserProgress | null> => {
  const userRef = doc(db, 'users', userId);
  const userDoc = await getDoc(userRef);
  if (userDoc.exists()) {
    return userDoc.data().progress as UserProgress;
  }
  return null;
}; 