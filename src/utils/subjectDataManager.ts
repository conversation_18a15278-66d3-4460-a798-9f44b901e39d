import { doc, getDoc, setDoc, updateDoc, arrayUnion, arrayRemove, writeBatch } from 'firebase/firestore';
import { db } from './firebase'; // Assuming firebase.ts exports firestore instance as 'firestore'
import { useSubjectStore } from '../stores/subjectStore';
import { Subject } from '@/components/productivity/SubjectManager';
import { toast } from '@/components/ui/use-toast';

const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds

/**
 * Fetches subjects for a user from Firestore or cache.
 * @param userId The ID of the user.
 * @param forceRefresh Whether to bypass the cache.
 * @returns Array of subjects.
 */
export const getSubjects = async (userId: string, forceRefresh: boolean = false): Promise<Subject[]> => {
  const { subjects, lastFetched, setSubjects, setLoading } = useSubjectStore.getState();

  if (!forceRefresh && subjects.length > 0 && lastFetched && (Date.now() - lastFetched < CACHE_DURATION)) {
    console.log('Returning cached subjects for user:', userId);
    return subjects;
  }

  setLoading(true);
  try {
    console.log('Fetching subjects from Firestore for user:', userId);
    const userSubjectsRef = doc(db, 'userSubjects', userId);
    const userSubjectsSnap = await getDoc(userSubjectsRef);

    if (userSubjectsSnap.exists()) {
      const data = userSubjectsSnap.data();
      // Handle potential old format (defaultSubjects/customSubjects)
      if (data.defaultSubjects || data.customSubjects) {
        const migratedSubjects: Subject[] = [
          ...(data.defaultSubjects || []).map((name: string) => ({
            id: name.toLowerCase().replace(/\s+/g, '-') + '-' + Math.random().toString(36).substring(2, 7),
            name,
            color: '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0'),
          })),
          ...(data.customSubjects || []).map((name: string) => ({
            id: name.toLowerCase().replace(/\s+/g, '-') + '-' + Math.random().toString(36).substring(2, 7),
            name,
            color: '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0'),
          })),
        ];
        await setDoc(userSubjectsRef, { subjects: migratedSubjects }); // Save migrated format
        setSubjects(migratedSubjects);
        return migratedSubjects;
      } else if (data.subjects) {
        const subjectsWithColors = (data.subjects as Subject[]).map(subject => ({
          ...subject,
          color: subject.color || '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0'),
        }));
        setSubjects(subjectsWithColors);
        return subjectsWithColors;
      }
    }
    // If document doesn't exist or no subjects field, initialize
    await setDoc(userSubjectsRef, { subjects: [] });
    setSubjects([]);
    return [];
  } catch (error) {
    console.error('Error fetching subjects:', error);
    toast({
      title: "Error",
      description: "Failed to load subjects from Firestore.",
      variant: "destructive",
    });
    return []; // Return empty array or cached subjects if available
  } finally {
    setLoading(false); // Ensure loading is false in all cases
  }
};

/**
 * Adds a new subject to Firestore and updates the cache.
 * @param userId The ID of the user.
 * @param newSubject The subject to add.
 * @returns True if successful, false otherwise.
 */
export const addSubjectToFirestore = async (userId: string, newSubject: Subject): Promise<boolean> => {
  const { subjects, addSubject: addSubjectToStore } = useSubjectStore.getState();
  if (subjects.some(s => s.name.toLowerCase() === newSubject.name.toLowerCase())) {
    toast({
      title: "Subject already exists",
      description: "Please use a different name.",
      variant: "destructive",
    });
    return false;
  }

  try {
    const userSubjectsRef = doc(db, 'userSubjects', userId);
    await updateDoc(userSubjectsRef, {
      subjects: arrayUnion(newSubject)
    });
    addSubjectToStore(newSubject); // Update local store
    toast({ title: "Subject added", description: `${newSubject.name} has been added.` });
    return true;
  } catch (error) {
    console.error('Error adding subject to Firestore:', error);
    toast({ title: "Error", description: "Failed to add subject.", variant: "destructive" });
    return false;
  }
};

/**
 * Updates an existing subject in Firestore and cache.
 * @param userId The ID of the user.
 * @param updatedSubject The subject with updated information.
 * @returns True if successful, false otherwise.
 */
export const updateSubjectInFirestore = async (userId: string, updatedSubject: Subject): Promise<boolean> => {
  const { subjects, updateSubject: updateSubjectInStore } = useSubjectStore.getState();
  try {
    const userSubjectsRef = doc(db, 'userSubjects', userId);
    // Firestore update for an item in an array is tricky. 
    // Best way is to read, modify, and write the whole array.
    const docSnap = await getDoc(userSubjectsRef);
    if (docSnap.exists()) {
      const currentSubjects = (docSnap.data().subjects || []) as Subject[];
      const subjectIndex = currentSubjects.findIndex(s => s.id === updatedSubject.id);
      if (subjectIndex === -1) {
        toast({ title: "Error", description: "Subject not found for update.", variant: "destructive" });
        return false;
      }
      currentSubjects[subjectIndex] = updatedSubject;
      await setDoc(userSubjectsRef, { subjects: currentSubjects });
      updateSubjectInStore(updatedSubject); // Update local store
      toast({ title: "Subject updated", description: `${updatedSubject.name} has been updated.` });
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error updating subject in Firestore:', error);
    toast({ title: "Error", description: "Failed to update subject.", variant: "destructive" });
    return false;
  }
};

/**
 * Deletes a subject from Firestore and cache.
 * Also updates studySessions to remove/rename the subject.
 * @param userId The ID of the user.
 * @param subjectId The ID of the subject to delete.
 * @param newSubjectName Optional. If provided, renames the subject in sessions instead of deleting.
 * @returns True if successful, false otherwise.
 */
export const deleteSubjectInFirestore = async (userId: string, subjectId: string, newSubjectName?: string): Promise<boolean> => {
  const { deleteSubject: deleteSubjectFromStore } = useSubjectStore.getState();
  try {
    const batch = writeBatch(db);
    const userSubjectsRef = doc(db, 'userSubjects', userId);
    const userProfileRef = doc(db, 'users', userId);

    // 1. Remove subject from userSubjects collection
    const subjectDocSnap = await getDoc(userSubjectsRef);
    if (subjectDocSnap.exists()) {
      const currentSubjects = (subjectDocSnap.data().subjects || []) as Subject[];
      const updatedSubjectsArray = currentSubjects.filter(s => s.id !== subjectId);
      batch.set(userSubjectsRef, { subjects: updatedSubjectsArray });
    }

    // 2. Update studySessions in the user's profile document
    const userProfileSnap = await getDoc(userProfileRef);
    if (userProfileSnap.exists()) {
      const userData = userProfileSnap.data();
      const studySessions = userData.studySessions || {};
      const updatedStudySessions: { [key: string]: any } = {};
      let sessionsModified = false;

      for (const sessionId in studySessions) {
        const session = studySessions[sessionId];
        if (session.subjectId === subjectId || session.subject === subjectId) { // Check both subjectId and old subject name
          sessionsModified = true;
          if (newSubjectName) {
            // Rename subject in session
            updatedStudySessions[sessionId] = { ...session, subject: newSubjectName, subjectId: undefined }; // Clear old subjectId if any
          } else {
            // Option 1: Remove session (if subject is critical)
            // delete updatedStudySessions[sessionId]; // This would delete the session
            // Option 2: Mark as 'Uncategorized' or similar
            updatedStudySessions[sessionId] = { ...session, subject: 'Uncategorized', subjectId: undefined }; 
          }
        } else {
          updatedStudySessions[sessionId] = session;
        }
      }
      if (sessionsModified) {
        batch.update(userProfileRef, { studySessions: updatedStudySessions });
      }
    }

    await batch.commit();
    deleteSubjectFromStore(subjectId);
    toast({ title: "Subject removed", description: `The subject has been removed${newSubjectName ? ` and sessions updated to ${newSubjectName}` : ''}.` });
    return true;
  } catch (error) {
    console.error('Error deleting subject in Firestore:', error);
    toast({ title: "Error", description: "Failed to delete subject.", variant: "destructive" });
    return false;
  }
};
