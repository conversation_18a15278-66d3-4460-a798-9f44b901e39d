import { supabase } from '../integrations/supabase/client';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { UserProfile } from '../types/user';

// Enhanced UserProfile interface for Supabase
export interface SupabaseUserProfile extends Omit<UserProfile, 'uid'> {
  id: string; // Supabase user ID
  firebaseUid?: string; // Original Firebase UID for data linking
  username: string; // Required for data continuity
  email: string;
  created_at?: string;
  updated_at?: string;
}

// Authentication utilities
export const signInWithGoogleSupabase = async () => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      console.error('Supabase Google sign in error:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error signing in with Google via Supabase:', error);
    throw error;
  }
};

export const signUpWithEmailPasswordSupabase = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) {
      console.error('Supabase email sign up error:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error signing up with email/password via Supabase:', error);
    throw error;
  }
};

export const signInWithEmailPasswordSupabase = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Supabase email sign in error:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error signing in with email/password via Supabase:', error);
    throw error;
  }
};

export const signOutSupabase = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Supabase sign out error:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error signing out from Supabase:', error);
    throw error;
  }
};

// User profile management with Firebase data continuity
export const getUserProfileByUsername = async (username: string): Promise<SupabaseUserProfile | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error fetching user profile by username:', error);
      throw error;
    }

    return data as SupabaseUserProfile | null;
  } catch (error) {
    console.error('Error in getUserProfileByUsername:', error);
    return null;
  }
};

export const getUserProfileById = async (userId: string): Promise<SupabaseUserProfile | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching user profile by ID:', error);
      throw error;
    }

    return data as SupabaseUserProfile | null;
  } catch (error) {
    console.error('Error in getUserProfileById:', error);
    return null;
  }
};

export const checkUsernameAvailabilitySupabase = async (username: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('username', username)
      .single();

    if (error && error.code === 'PGRST116') {
      // Username not found, so it's available
      return true;
    }

    if (error) {
      console.error('Error checking username availability:', error);
      throw error;
    }

    // Username found, so it's not available
    return false;
  } catch (error) {
    console.error('Error in checkUsernameAvailabilitySupabase:', error);
    return false;
  }
};

export const createUserProfile = async (
  user: SupabaseUser,
  profileData: Partial<SupabaseUserProfile>
): Promise<SupabaseUserProfile | null> => {
  try {
    const userProfile: Partial<SupabaseUserProfile> = {
      id: user.id,
      email: user.email!,
      displayName: user.user_metadata?.full_name || user.email?.split('@')[0],
      photoURL: user.user_metadata?.avatar_url || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      welcomeEmailSent: false,
      stats: {},
      progress: {},
      studySessions: {},
      mockTests: {},
      ...profileData,
    };

    const { data, error } = await supabase
      .from('users')
      .insert(userProfile)
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }

    return data as SupabaseUserProfile;
  } catch (error) {
    console.error('Error in createUserProfile:', error);
    return null;
  }
};

export const updateUserProfile = async (
  userId: string,
  profileData: Partial<SupabaseUserProfile>
): Promise<SupabaseUserProfile | null> => {
  try {
    const updateData = {
      ...profileData,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }

    return data as SupabaseUserProfile;
  } catch (error) {
    console.error('Error in updateUserProfile:', error);
    return null;
  }
};

// Data continuity function: Link Supabase user to existing Firebase data
export const linkUserToFirebaseData = async (
  supabaseUser: SupabaseUser,
  username: string,
  firebaseUid?: string
): Promise<SupabaseUserProfile | null> => {
  try {
    // Check if user already exists in Supabase by username
    let existingProfile = await getUserProfileByUsername(username);
    
    if (existingProfile) {
      // Update existing profile with Supabase user ID and auth info
      const updatedProfile = await updateUserProfile(existingProfile.id, {
        id: supabaseUser.id, // Update to new Supabase ID
        email: supabaseUser.email!,
        displayName: supabaseUser.user_metadata?.full_name || existingProfile.displayName,
        photoURL: supabaseUser.user_metadata?.avatar_url || existingProfile.photoURL,
        firebaseUid: firebaseUid || existingProfile.firebaseUid,
        lastLogin: new Date().toISOString(),
      });
      
      console.log(`Linked Supabase user ${supabaseUser.id} to existing data for username: ${username}`);
      return updatedProfile;
    } else {
      // Create new profile with Firebase UID for potential data migration
      const newProfile = await createUserProfile(supabaseUser, {
        username,
        firebaseUid,
      });
      
      console.log(`Created new profile for username: ${username} with Supabase ID: ${supabaseUser.id}`);
      return newProfile;
    }
  } catch (error) {
    console.error('Error linking user to Firebase data:', error);
    return null;
  }
};

// Auth state change handler
export const onAuthStateChangeSupabase = (callback: (user: SupabaseUser | null) => void) => {
  return supabase.auth.onAuthStateChange(async (event, session) => {
    console.log('Supabase auth state change:', event, session?.user?.id);
    
    if (session?.user) {
      // Update last login timestamp
      await updateUserProfile(session.user.id, {
        lastLogin: new Date().toISOString(),
      });
    }
    
    callback(session?.user || null);
  });
};
