import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  addDoc,
  doc,
  updateDoc,
  arrayUnion,
  onSnapshot,
  query,
  orderBy,
  Timestamp,
  getDoc,
  arrayRemove,
  getDocs,
  where,
  setDoc,
  deleteDoc,
  increment,
  limit,
  startAfter,
  DocumentData,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import {
  getAuth,
  GoogleAuthProvider,
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User,
  setPersistence,
  browserLocalPersistence,
  signInWithRedirect,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  EmailAuthProvider,
  linkWithPopup,
  linkWithCredential,
  unlink,
  reauthenticateWithCredential,
  AuthCredential
} from 'firebase/auth';

import { Comment } from '../types/chat';
import { SharedChatSEO, ChatMessage, PublicQnA } from '../types/qa';
import { sendWelcomeEmail } from './emailUtils';

// Validate environment variables
const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
] as const;

const missingEnvVars = requiredEnvVars.filter(
  varName => !import.meta.env[varName]
);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
};

if (!firebaseConfig.projectId) {
  throw new Error('Firebase projectId is undefined. Please check your environment variables.');
}

let app;
try {
  app = initializeApp(firebaseConfig);
  console.log('Firebase initialized successfully');
} catch (error: any) {
  console.error("Firebase initialization error:", error.message);
  if (error.message.includes('already exists')) {
    console.log('Attempting to initialize with unique name');
    app = initializeApp(firebaseConfig, `doubtgpt-${Date.now()}`);
  } else {
    throw error;
  }
}

export { app }; // Export the initialized app instance
export const db = getFirestore(app);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// Add scopes
googleProvider.addScope('https://www.googleapis.com/auth/userinfo.email');
googleProvider.addScope('https://www.googleapis.com/auth/userinfo.profile');

// Configure basic Google OAuth flow
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Check if user has completed profile setup
export const checkUserProfile = async (uid: string): Promise<boolean> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);
    if (!userSnap.exists()) return false;
    const userData = userSnap.data();
    return Boolean(userData?.username); // Profile is complete if username exists
  } catch (error) {
    console.error('Error checking user profile:', error);
    return false;
  }
};



// Update user profile
export const updateUserProfile = async (uid: string, profileData: {
  username?: string;
  photoURL?: string;
  displayName?: string;
  backgroundImage?: string;
  bio?: string;
  location?: string;
}) => {
  const userRef = doc(db, 'users', uid);
  await setDoc(userRef, {
    ...profileData,
    updatedAt: new Date().toISOString(),
  }, { merge: true });
};

// Check username availability
export const checkUsernameAvailability = async (username: string): Promise<boolean> => {
  const usersRef = collection(db, 'users');
  const q = query(usersRef, where('username', '==', username));
  const querySnapshot = await getDocs(q);
  return querySnapshot.empty;
};

// Find user ID by username (case-insensitive for flexibility, but store exact case)
export const findUserIdByUsername = async (username: string): Promise<string | null> => {
  if (!username) return null;
  const usersRef = collection(db, 'users');
  // Firestore queries are case-sensitive. If usernames are stored with exact casing,
  // we might need a different approach (like storing a lowercase version for searching)
  // or query based on the exact match for now.
  // Let's assume exact match for now. Consider adding a lowercase field later if needed.
  const q = query(usersRef, where('username', '==', username), limit(1));
  try {
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      return querySnapshot.docs[0].id; // Return the user ID
    }
    return null; // Username not found
  } catch (error) {
    console.error(`Error finding user ID for username ${username}:`, error);
    return null;
  }
};


export const signInWithGoogle = async () => {
  try {
    // First ensure we're using the right persistence
    await setPersistence(auth, browserLocalPersistence);

    // Try popup first
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;

      if (!user) {
        throw new Error('No user data available after sign-in');
      }

      // Check if user has completed profile setup
      const hasProfile = await checkUserProfile(user.uid);

      // If no profile, create basic user doc
      if (!hasProfile) {
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);
        const userData = userDoc.data();

        // Check if welcome email was already sent
        const welcomeEmailSent = userData?.welcomeEmailSent === true;

        await setDoc(userRef, {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName || 'Anonymous',
          photoURL: user.photoURL || '',
          createdAt: new Date().toISOString(),
          lastLogin: new Date().toISOString(),
          // Flag to track if welcome email was sent
          welcomeEmailSent: true
        }, { merge: true });

        // Send welcome email only if it wasn't sent before
        if (!welcomeEmailSent && user.email) {
          try {
            await sendWelcomeEmail(user.email, user.displayName || 'there');
          } catch (emailError) {
            console.error('Error sending welcome email:', emailError);
            // Continue with sign-in even if email fails
          }
        }
      }

      return { user, hasProfile };
    } catch (popupError: any) {
      console.log('Popup sign-in failed, trying redirect:', popupError);
      if (popupError.code === 'auth/popup-blocked' ||
          popupError.code === 'auth/internal-error' ||
          popupError.code === 'auth/cancelled-popup-request') {
        // If popup fails, try redirect
        await signInWithRedirect(auth, googleProvider);
        return null; // User will be redirected, so return null
      }
      throw popupError; // Re-throw other errors
    }
  } catch (error: any) {
    console.error('Error signing in with Google:', error);
    if (error.code === 'auth/internal-error') {
      throw new Error('Unable to connect to Google. Please check your internet connection and try again.');
    }
    throw error;
  }
};

export const subscribeToComments = (chatId: string, callback: (comments: Comment[]) => void) => {
  if (!chatId) {
    console.warn('No chatId provided to subscribeToComments');
    callback([]);
    return () => {};
  }

  try {
    console.log(`Setting up comments subscription for chat ${chatId}`);
    const q = query(collection(db, `chats/${chatId}/comments`), orderBy('timestamp', 'desc'));
    return onSnapshot(q,
      (snapshot) => {
        // Simplified logic: Map docs directly, assuming user info is embedded
        const comments = snapshot.docs.map(doc => {
          const data = doc.data();
          // Helper to convert timestamps recursively
          const convertTimestamps = (item: any): any => {
            if (item && typeof item.toMillis === 'function') {
              return item.toMillis();
            }
            if (item && typeof item === 'object') {
              if (Array.isArray(item)) {
                return item.map(convertTimestamps);
              }
              const newItem: { [key: string]: any } = {};
              for (const key in item) {
                if (key === 'timestamp' && item[key] && typeof item[key].toMillis === 'function') {
                  newItem[key] = item[key].toMillis();
                } else if (key === 'replies' && Array.isArray(item[key])) {
                   newItem[key] = item[key].map(convertTimestamps);
                } else {
                  newItem[key] = convertTimestamps(item[key]);
                }
              }
              return newItem;
            }
            return item;
          };

          const processedData = convertTimestamps({
            id: doc.id,
            ...data,
            // Ensure top-level timestamp is converted
            timestamp: data.timestamp?.toMillis() || Date.now()
          });

          return processedData as Comment;
        });

        console.log(`Received ${comments.length} comments for chat ${chatId} (using embedded user data)`);
        callback(comments);
      },
      (error) => {
        console.error(`Error in comments subscription for chat ${chatId}:`, error);
        callback([]);
      }
    );
  } catch (error) {
    console.error(`Error setting up comments subscription for chat ${chatId}:`, error);
    callback([]);
    return () => {};
  }
};

export const addComment = async (chatId: string, content: string, author: string) => {
  if (!chatId) throw new Error('No chatId provided to addComment');

  const auth = getAuth();
  const user = auth.currentUser;
  if (!user) throw new Error('You must be logged in to add a comment');

  // Get user's profile data
  const userRef = doc(db, 'users', user.uid);
  const userSnap = await getDoc(userRef);
  const userData = userSnap.exists() ? userSnap.data() : { username: 'Anonymous', photoURL: '' }; // Provide defaults

  const commentsRef = collection(db, `chats/${chatId}/comments`);

  // --- Mention Detection Logic ---
  const mentionedUsernames = content.match(/@(\w+)/g)?.map(m => m.substring(1)) || [];
  const mentionedUserIds = (
    await Promise.all(
      mentionedUsernames.map(username => findUserIdByUsername(username))
    )
  ).filter((id): id is string => id !== null); // Filter out nulls and ensure type is string[]

  return addDoc(commentsRef, {
    content,
    author: userData.username, // Use fetched username
    authorId: user.uid,
    authorUsername: userData.username, // Embed username
    authorPhotoURL: userData.photoURL || '', // Embed photoURL
    timestamp: Timestamp.now(),
    replies: [],
    mentionedUserIds: mentionedUserIds.length > 0 ? mentionedUserIds : null // Store mentioned IDs or null
  });
};

// Use browser's built-in crypto API to generate UUIDs
function generateUUID() {
  // Check if crypto.randomUUID is available (modern browsers)
  if (typeof window !== 'undefined' && window.crypto && window.crypto.randomUUID) {
    return window.crypto.randomUUID();
  }

  // Fallback for older browsers
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const addReply = async (chatId: string, parentId: string, content: string, author: string) => {
  if (!chatId) throw new Error('No chatId provided to addReply');
  if (!parentId) throw new Error('No parentId provided to addReply');

  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    throw new Error('User not authenticated');
  }

  // Get user's profile data
  const userRef = doc(db, 'users', user.uid);
  const userSnap = await getDoc(userRef);
  const userData = userSnap.exists() ? userSnap.data() : { username: 'Anonymous', photoURL: '' }; // Provide defaults

  const reply = {
    id: generateUUID(),
    content,
    author: userData.username, // Use fetched username
    authorId: user.uid,  // Store the user's ID
    authorUsername: userData.username, // Embed username
    authorPhotoURL: userData.photoURL || '', // Embed photoURL
    timestamp: Timestamp.now(),
    parentId,
    replies: [],
    mentionedUserIds: [] // Initialize for replies
  };

  // --- Mention Detection Logic for Replies ---
  const mentionedUsernamesInReply = content.match(/@(\w+)/g)?.map(m => m.substring(1)) || [];
  const mentionedUserIdsInReply = (
    await Promise.all(
      mentionedUsernamesInReply.map(username => findUserIdByUsername(username))
    )
  ).filter((id): id is string => id !== null);

  if (mentionedUserIdsInReply.length > 0) {
    reply.mentionedUserIds = mentionedUserIdsInReply;
  }

  // Get the root comment and all its replies
  const commentRef = doc(db, `chats/${chatId}/comments`, parentId.split('_')[0]);
  const commentSnap = await getDoc(commentRef);

  if (!commentSnap.exists()) {
    throw new Error('Parent comment not found');
  }

  const commentData = commentSnap.data();
  let replies = commentData.replies || [];

  // Function to add reply to the correct parent in the reply tree
  const addReplyToTree = (replyList: any[], targetId: string, newReply: any): boolean => {
    for (let i = 0; i < replyList.length; i++) {
      if (replyList[i].id === targetId) {
        replyList[i].replies = replyList[i].replies || [];
        replyList[i].replies.push(newReply);
        return true;
      }
      if (replyList[i].replies && replyList[i].replies.length > 0) {
        if (addReplyToTree(replyList[i].replies, targetId, newReply)) {
          return true;
        }
      }
    }
    return false;
  };

  // If replying to the root comment
  if (parentId === commentSnap.id) {
    replies.push(reply);
  } else {
    // If replying to a nested reply
    const success = addReplyToTree(replies, parentId, reply);
    if (!success) {
      throw new Error('Parent reply not found');
    }
  }

  // Update the comment with the new reply structure
  return updateDoc(commentRef, { replies });
};

export async function deleteComment(chatId: string, commentId: string): Promise<void> {
  try {
    const commentRef = doc(db, `chats/${chatId}/comments`, commentId);
    await deleteDoc(commentRef);
    console.log('Comment deleted from Firebase');
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
}

export async function deleteReply(chatId: string, commentId: string, replyId: string, parentReplyId?: string): Promise<void> {
  try {
    const commentRef = doc(db, `chats/${chatId}/comments`, commentId);
    const commentSnap = await getDoc(commentRef);

    if (!commentSnap.exists()) {
      throw new Error('Comment not found');
    }

    const commentData = commentSnap.data();
    let replies = commentData.replies || [];

    // Function to remove reply from the reply tree
    const removeReplyFromTree = (replyList: any[], targetId: string, parentId?: string): boolean => {
      // If we have a parent ID, we need to find that parent first
      if (parentId) {
        for (let i = 0; i < replyList.length; i++) {
          if (replyList[i].id === parentId) {
            if (replyList[i].replies) {
              const replyIndex = replyList[i].replies.findIndex((r: any) => r.id === targetId);
              if (replyIndex !== -1) {
                replyList[i].replies.splice(replyIndex, 1);
                return true;
              }
            }
          }
          if (replyList[i].replies && replyList[i].replies.length > 0) {
            if (removeReplyFromTree(replyList[i].replies, targetId, parentId)) {
              return true;
            }
          }
        }
      } else {
        // Direct reply to the comment
        const replyIndex = replyList.findIndex(reply => reply.id === targetId);
        if (replyIndex !== -1) {
          replyList.splice(replyIndex, 1);
          return true;
        }
        // Search in nested replies
        for (let i = 0; i < replyList.length; i++) {
          if (replyList[i].replies && replyList[i].replies.length > 0) {
            if (removeReplyFromTree(replyList[i].replies, targetId)) {
              return true;
            }
          }
        }
      }
      return false;
    };

    const success = removeReplyFromTree(replies, replyId, parentReplyId);
    if (success) {
      await updateDoc(commentRef, { replies });
      console.log('Reply deleted from Firebase');
    } else {
      throw new Error('Reply not found');
    }
  } catch (error) {
    console.error('Error deleting reply:', error);
    throw error;
  }
}

export const signOut = async () => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, async (user) => {
    if (user) {
      // Update lastActive timestamp when user is authenticated
      await updateUserLastActive(user.uid);
    }
    callback(user);
  });
};

// Add email/password authentication functions
export const signUpWithEmailPassword = async (email: string, password: string) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create initial user document
    const userRef = doc(db, 'users', user.uid);
    await setDoc(userRef, {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName || email.split('@')[0],
      photoURL: user.photoURL || '',
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      // Flag to track if welcome email was sent
      welcomeEmailSent: true
    }, { merge: true });

    // Send welcome email
    try {
      const displayName = user.displayName || email.split('@')[0];
      await sendWelcomeEmail(email, displayName);
    } catch (emailError) {
      console.error('Error sending welcome email:', emailError);
      // Continue with sign-up even if email fails
    }

    return { user };
  } catch (error: any) {
    console.error('Error signing up with email/password:', error);
    throw error;
  }
};

export const signInWithEmailPassword = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return { user: userCredential.user };
  } catch (error: any) {
    console.error('Error signing in with email/password:', error);
    throw error;
  }
};

// Define user stats interface
export interface UserStats {
  questionsAsked: number;
  currentStreak: number;
  longestStreak: number;
  lastActive: string;
  totalChats: number;
  totalComments: number;
  totalReplies: number;
  memberSince: string;
  lastLogin: string;
  profileViews: number;
  rank: string;
  badges: string[];
}

// Get user stats
export const getUserStats = async (uid: string): Promise<UserStats> => {
  const userRef = doc(db, 'users', uid);
  const userSnap = await getDoc(userRef);

  if (!userSnap.exists()) {
    return {
      questionsAsked: 0,
      currentStreak: 0,
      longestStreak: 0,
      lastActive: new Date().toISOString(),
      totalChats: 0,
      totalComments: 0,
      totalReplies: 0,
      memberSince: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      profileViews: 0,
      rank: '',
      badges: []
    };
  }

  const userData = userSnap.data();
  return {
    questionsAsked: userData.stats?.questionsAsked || 0,
    currentStreak: userData.stats?.currentStreak || 0,
    longestStreak: userData.stats?.longestStreak || 0,
    lastActive: userData.stats?.lastActive || new Date().toISOString(),
    totalChats: userData.stats?.totalChats || 0,
    totalComments: userData.stats?.totalComments || 0,
    totalReplies: userData.stats?.totalReplies || 0,
    memberSince: userData.stats?.memberSince || new Date().toISOString(),
    lastLogin: userData.stats?.lastLogin || new Date().toISOString(),
    profileViews: userData.stats?.profileViews || 0,
    rank: userData.stats?.rank || '',
    badges: userData.stats?.badges || []
  };
};

// Update user stats
export const updateUserStats = async (uid: string, updates: Partial<UserStats>) => {
  const userRef = doc(db, 'users', uid);
  const userSnap = await getDoc(userRef);

  if (!userSnap.exists()) return;

  const currentStats = userSnap.data().stats || {};
  const today = new Date();
  const lastActive = new Date(currentStats.lastActive || 0);
  const diffDays = Math.floor((today.getTime() - lastActive.getTime()) / (1000 * 60 * 60 * 24));

  let updatedStats = { ...currentStats, ...updates };

  // Update streak
  if (diffDays === 0) {
    // Same day, no streak update needed
  } else if (diffDays === 1) {
    // Next day, increment streak
    updatedStats.currentStreak = (currentStats.currentStreak || 0) + 1;
    updatedStats.longestStreak = Math.max(updatedStats.currentStreak, currentStats.longestStreak || 0);
  } else {
    // Streak broken
    updatedStats.currentStreak = 1;
  }

  updatedStats.lastActive = today.toISOString();

  await setDoc(userRef, {
    stats: updatedStats,
    updatedAt: today.toISOString(),
  }, { merge: true });

  return updatedStats;
};

// Increment questions asked count
export const incrementQuestionsAsked = async (uid: string) => {
  const userRef = doc(db, 'users', uid);
  const userSnap = await getDoc(userRef);

  if (!userSnap.exists()) return;

  const currentStats = userSnap.data().stats || {};
  const updatedStats = await updateUserStats(uid, {
    questionsAsked: (currentStats.questionsAsked || 0) + 1
  });

  return updatedStats;
};

// Create a slug from a chat title
export const createSlugFromTitle = (title: string): string => {
  const maxLength = 80; // Maximum slug length

  // Convert to lowercase, replace non-alphanumeric with hyphens
  let slug = title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove non-word characters except spaces and hyphens
    .replace(/[\s_-]+/g, '-') // Replace spaces, underscores, and hyphens with single hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading and trailing hyphens

  // Truncate if too long, ensuring we don't cut in the middle of a word
  if (slug.length > maxLength) {
    slug = slug.substring(0, maxLength).replace(/-[^-]*$/, '');
  }

  // Make unique by adding timestamp hash at the end if needed
  const timestamp = Date.now().toString(36);
  return `${slug}-${timestamp.slice(-4)}`;
};

// Generate a title for a chat based on its first message
export const generateChatTitle = (message: string): string => {
  // Truncate long messages
  const truncated = message.length > 60
    ? message.substring(0, 57) + '...'
    : message;

  // Remove special characters that might cause issues in URLs
  return truncated
    .replace(/[^\w\s.,?!-]/g, '') // Keep only alphanumeric, spaces, and basic punctuation
    .trim();
};

// Get chat by slug (for SEO pages)
export const getChatBySlug = async (slug: string): Promise<SharedChatSEO | null> => {
  try {
    // First, query aiChats for documents with the given slug
    const chatsRef = collection(db, 'aiChats');
    const q = query(chatsRef, where('slug', '==', slug), limit(1));

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) {
      return null;
    }

    const chatDoc = querySnapshot.docs[0];
    const chatData = chatDoc.data();

    // Increment view count
    await updateDoc(chatDoc.ref, {
      viewCount: increment(1),
      updatedAt: Timestamp.now()
    });

    // Convert to SharedChatSEO format
    const sharedChat: SharedChatSEO = {
      id: chatDoc.id,
      title: chatData.title || (chatData.messages && chatData.messages.length > 0
        ? generateChatTitle(chatData.messages[0].content)
        : 'Chat Conversation'),
      slug: chatData.slug || slug,
      createdAt: chatData.createdAt ?
        (typeof chatData.createdAt.toMillis === 'function' ?
          chatData.createdAt.toMillis() : chatData.createdAt) :
        Date.now(),
      updatedAt: chatData.updatedAt ?
        (typeof chatData.updatedAt.toMillis === 'function' ?
          chatData.updatedAt.toMillis() : chatData.updatedAt) :
        Date.now(),
      userId: chatData.userId,
      viewCount: (chatData.viewCount || 0) + 1, // Include the increment we just made
      chatMessages: chatData.messages || [],
      status: 'approved', // Default status
      image: chatData.messages && chatData.messages.length > 0 ?
        chatData.messages[0].image : undefined,
      tags: chatData.tags || []
    };

    return { ...sharedChat, requiresAuth: false };
  } catch (error) {
    console.error('Error getting chat by slug:', error);
    return null;
  }
};

// Get recent shared chats for display in directory
export const getRecentSharedChats = async (limitCount = 30, lastDoc: QueryDocumentSnapshot<DocumentData> | null = null): Promise<{ chats: SharedChatSEO[], lastDoc: QueryDocumentSnapshot<DocumentData> | null }> => {
  try {
    // Explicitly use collection from non-authenticated context
    const chatsRef = collection(db, 'aiChats');
    console.log('Firebase: Getting recent shared chats, limit:', limitCount);

    // Handle larger limits with multiple queries if needed
    const MAX_LIMIT = 100; // Firebase query limit per request

    if (limitCount <= MAX_LIMIT) {
      // Standard query when limit is within range
      let q;
      if (lastDoc) {
        // Try to fetch all chats instead of filtering by isPublic for testing purposes
        // The actual permission control happens at the Firebase security rules level
        q = query(
          chatsRef,
          orderBy('updatedAt', 'desc'),
          startAfter(lastDoc),
          limit(limitCount)
        );
      } else {
        q = query(
          chatsRef,
          orderBy('updatedAt', 'desc'),
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      console.log('Firebase: Got docs count:', querySnapshot.docs.length);
      const lastVisible = querySnapshot.docs.length > 0 ? querySnapshot.docs[querySnapshot.docs.length - 1] : null;

      const chats = querySnapshot.docs.map(doc => {
        const data = doc.data() as DocumentData;
        return {
          id: doc.id,
          title: data.title || (data.messages && data.messages.length > 0
            ? generateChatTitle(data.messages[0].content)
            : 'Chat Conversation'),
          slug: data.slug || createSlugFromTitle(data.title || 'chat-conversation'),
          createdAt: data.createdAt ?
            (typeof data.createdAt.toMillis === 'function' ?
              data.createdAt.toMillis() : data.createdAt) :
            Date.now(),
          updatedAt: data.updatedAt ?
            (typeof data.updatedAt.toMillis === 'function' ?
              data.updatedAt.toMillis() : data.updatedAt) :
            Date.now(),
          userId: data.userId,
          viewCount: data.viewCount || 0,
          chatMessages: data.messages || [],
          status: (data.status as 'approved' | 'pending' | 'rejected') || 'approved',
          image: data.messages && data.messages.length > 0 ?
            data.messages[0].image : undefined,
          tags: data.tags || []
        } as SharedChatSEO;
      });

      return { chats, lastDoc: lastVisible };
    } else {
      // For larger limits, we need to split into multiple queries
      console.log('Firebase: Large limit requested, using multiple queries');
      let allChats: SharedChatSEO[] = [];
      let currentLastDoc = lastDoc;
      let remainingCount = limitCount;

      while (remainingCount > 0) {
        const batchSize = Math.min(remainingCount, MAX_LIMIT);
        let q;

        if (currentLastDoc) {
          q = query(
            chatsRef,
            orderBy('updatedAt', 'desc'),
            startAfter(currentLastDoc),
            limit(batchSize)
          );
        } else {
          q = query(
            chatsRef,
            orderBy('updatedAt', 'desc'),
            limit(batchSize)
          );
        }

        const querySnapshot = await getDocs(q);
        console.log(`Firebase: Batch query got ${querySnapshot.docs.length} docs`);

        if (querySnapshot.empty) {
          break; // No more results
        }

        const batchChats = querySnapshot.docs.map(doc => {
          const data = doc.data() as DocumentData;
          return {
            id: doc.id,
            title: data.title || (data.messages && data.messages.length > 0
              ? generateChatTitle(data.messages[0].content)
              : 'Chat Conversation'),
            slug: data.slug || createSlugFromTitle(data.title || 'chat-conversation'),
            createdAt: data.createdAt ?
              (typeof data.createdAt.toMillis === 'function' ?
                data.createdAt.toMillis() : data.createdAt) :
              Date.now(),
            updatedAt: data.updatedAt ?
              (typeof data.updatedAt.toMillis === 'function' ?
                data.updatedAt.toMillis() : data.updatedAt) :
              Date.now(),
            userId: data.userId,
            viewCount: data.viewCount || 0,
            chatMessages: data.messages || [],
            status: (data.status as 'approved' | 'pending' | 'rejected') || 'approved',
            image: data.messages && data.messages.length > 0 ?
              data.messages[0].image : undefined,
            tags: data.tags || []
          } as SharedChatSEO;
        });

        allChats = [...allChats, ...batchChats];
        remainingCount -= batchChats.length;

        // If we got fewer results than requested, we've reached the end
        if (querySnapshot.docs.length < batchSize) {
          break;
        }

        // Update the last document reference for the next batch
        currentLastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
      }

      return {
        chats: allChats,
        lastDoc: allChats.length > 0 ? currentLastDoc : null
      };
    }
  } catch (error) {
    console.error('Error getting recent shared chats:', error);
    return { chats: [], lastDoc: null };
  }
};

// Get popular shared chats for display in directory
export const getPopularSharedChats = async (limitCount = 30, lastDoc: QueryDocumentSnapshot<DocumentData> | null = null): Promise<{ chats: SharedChatSEO[], lastDoc: QueryDocumentSnapshot<DocumentData> | null }> => {
  try {
    // Explicitly use collection from non-authenticated context
    const chatsRef = collection(db, 'aiChats');
    console.log('Firebase: Getting popular shared chats, limit:', limitCount);

    // Handle larger limits with multiple queries if needed
    const MAX_LIMIT = 100; // Firebase query limit per request

    if (limitCount <= MAX_LIMIT) {
      // Standard query when limit is within range
      let q;
      if (lastDoc) {
        q = query(
          chatsRef,
          orderBy('viewCount', 'desc'), // Order by view count first
          startAfter(lastDoc),
          limit(limitCount)
        );
      } else {
        q = query(
          chatsRef,
          orderBy('viewCount', 'desc'), // Order by view count first
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      console.log('Firebase: Got docs count:', querySnapshot.docs.length);
      const lastVisible = querySnapshot.docs.length > 0 ? querySnapshot.docs[querySnapshot.docs.length - 1] : null;

      const chats = querySnapshot.docs.map(doc => {
        const data = doc.data() as DocumentData;
        return {
          id: doc.id,
          title: data.title || (data.messages && data.messages.length > 0
            ? generateChatTitle(data.messages[0].content)
            : 'Chat Conversation'),
          slug: data.slug || createSlugFromTitle(data.title || 'chat-conversation'),
          createdAt: data.createdAt ?
            (typeof data.createdAt.toMillis === 'function' ?
              data.createdAt.toMillis() : data.createdAt) :
            Date.now(),
          updatedAt: data.updatedAt ?
            (typeof data.updatedAt.toMillis === 'function' ?
              data.updatedAt.toMillis() : data.updatedAt) :
            Date.now(),
          userId: data.userId,
          viewCount: data.viewCount || 0, // Ensure we have a viewCount, defaulting to 0
          chatMessages: data.messages || [],
          status: (data.status as 'approved' | 'pending' | 'rejected') || 'approved',
          image: data.messages && data.messages.length > 0 ?
            data.messages[0].image : undefined,
          tags: data.tags || []
        } as SharedChatSEO;
      });

      return { chats, lastDoc: lastVisible };
    } else {
      // For larger limits, we need to split into multiple queries
      console.log('Firebase: Large limit requested, using multiple queries');
      let allChats: SharedChatSEO[] = [];
      let currentLastDoc = lastDoc;
      let remainingCount = limitCount;

      while (remainingCount > 0) {
        const batchSize = Math.min(remainingCount, MAX_LIMIT);
        let q;

        if (currentLastDoc) {
          q = query(
            chatsRef,
            orderBy('viewCount', 'desc'),
            startAfter(currentLastDoc),
            limit(batchSize)
          );
        } else {
          q = query(
            chatsRef,
            orderBy('viewCount', 'desc'),
            limit(batchSize)
          );
        }

        const querySnapshot = await getDocs(q);
        console.log(`Firebase: Batch query got ${querySnapshot.docs.length} docs`);

        if (querySnapshot.empty) {
          break; // No more results
        }

        const batchChats = querySnapshot.docs.map(doc => {
          const data = doc.data() as DocumentData;
          return {
            id: doc.id,
            title: data.title || (data.messages && data.messages.length > 0
              ? generateChatTitle(data.messages[0].content)
              : 'Chat Conversation'),
            slug: data.slug || createSlugFromTitle(data.title || 'chat-conversation'),
            createdAt: data.createdAt ?
              (typeof data.createdAt.toMillis === 'function' ?
                data.createdAt.toMillis() : data.createdAt) :
              Date.now(),
            updatedAt: data.updatedAt ?
              (typeof data.updatedAt.toMillis === 'function' ?
                data.updatedAt.toMillis() : data.updatedAt) :
              Date.now(),
            userId: data.userId,
            viewCount: data.viewCount || 0, // Ensure we have a viewCount, defaulting to 0
            chatMessages: data.messages || [],
            status: (data.status as 'approved' | 'pending' | 'rejected') || 'approved',
            image: data.messages && data.messages.length > 0 ?
              data.messages[0].image : undefined,
            tags: data.tags || []
          } as SharedChatSEO;
        });

        allChats = [...allChats, ...batchChats];
        remainingCount -= batchChats.length;

        // If we got fewer results than requested, we've reached the end
        if (querySnapshot.docs.length < batchSize) {
          break;
        }

        // Update the last document reference for the next batch
        currentLastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
      }

      return {
        chats: allChats,
        lastDoc: allChats.length > 0 ? currentLastDoc : null
      };
    }
  } catch (error) {
    console.error('Error getting popular shared chats:', error);
    return { chats: [], lastDoc: null };
  }
};

// Get total count of shared chats (for pagination)
export const getSharedChatCount = async (): Promise<number> => {
  try {
    // Explicitly use collection from non-authenticated context
    const chatsRef = collection(db, 'aiChats');
    console.log('Firebase: Getting shared chat count');

    // Get all chats count - permissions will be handled by Firebase security rules
    const q = query(chatsRef);
    const querySnapshot = await getDocs(q);

    console.log('Firebase: Total chat count:', querySnapshot.size);
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting shared chat count:', error);
    return 0;
  }
};

// Helper function to format dates into a structured object
const formatDateToStructure = (date = new Date()) => {
  return {
    day: date.getDate(),
    month: date.getMonth() + 1, // JavaScript months are 0-indexed
    year: date.getFullYear(),
    timestamp: date.getTime(), // Keep timestamp for sorting/comparisons
    formatted: date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  };
};

// Modified saveAIChat to add slug when saving public chats
export const saveAIChat = async (chatId: string, chatData: any, isPublic: boolean = false) => {
  try {
    // Helper function to recursively filter out undefined values from objects
    const removeUndefined = (obj: any): any => {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(item => removeUndefined(item));
      }

      return Object.fromEntries(
        Object.entries(obj)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => [key, removeUndefined(value)])
      );
    };

    // If there's an existing createdAt, remove it
    const { createdAt: _, ...dataWithoutCreatedAt } = chatData;

    // For public chats, generate a title and slug if not already present
    let title = chatData.title;
    let slug = chatData.slug;

    if (isPublic && chatData.messages && chatData.messages.length > 0) {
      // Generate a title from the first message if not already set
      if (!title) {
        const firstMsg = chatData.messages.find((msg: any) => msg.isUser === true);
        if (firstMsg) {
          title = generateChatTitle(firstMsg.content);
        }
      }

      // Generate a slug from the title if not already set
      if (!slug && title) {
        slug = createSlugFromTitle(title);
      }
    }

    // Create structured date format instead of Firebase Timestamp
    const now = new Date();
    const structuredDate = formatDateToStructure(now);

    const filteredData = removeUndefined({
      ...dataWithoutCreatedAt,
      isPublic,
      title,
      slug,
      createdAt: structuredDate, // Store as structured date instead of Timestamp
      updatedAt: structuredDate,
      viewCount: chatData.viewCount || 0
    });

    const chatRef = doc(db, 'aiChats', chatId);
    await setDoc(chatRef, filteredData, { merge: true });

    return chatId;
  } catch (error) {
    console.error('Error saving AI chat:', error);
    throw error;
  }
};

// Get AI chat by ID
export const getAIChat = async (chatId: string) => {
  if (!chatId) {
    console.error('Firebase: getAIChat called with empty chatId');
    throw new Error('Chat ID is required');
  }

  try {
    console.log('Firebase: Getting AI chat with ID:', chatId);
    const chatRef = doc(db, 'aiChats', chatId);
    const chatSnap = await getDoc(chatRef);

    if (!chatSnap.exists()) {
      console.log('Firebase: Chat not found with ID:', chatId);
      throw new Error('Chat not found');
    }

    const chatData = chatSnap.data();
    console.log('Firebase: Retrieved chat, public status:', chatData.isPublic ? 'public' : 'private');

    // Return all chats, regardless of isPublic flag
    return { ...chatData, id: chatId };
  } catch (error) {
    console.error('Firebase: Error getting AI chat:', error);
    throw error;
  }
};

// Report AI chat functionality has been removed

// Get user's shared chats
export const getUserSharedChats = async (userId: string) => {
  try {
    const chatsRef = collection(db, 'aiChats');
    const q = query(
      chatsRef,
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting user shared chats:', error);
    throw error;
  }
};

// Get public shared chats for the shared chats directory
export const getPublicSharedChats = async (limitCount = 100) => {
  try {
    const chatsRef = collection(db, 'aiChats');
    const q = query(
      chatsRef,
      orderBy('viewCount', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting public shared chats:', error);
    throw error;
  }
};

// Get user's chat history from Firebase
export const getUserChatHistory = async (userId: string) => {
  try {
    const q = query(
      collection(db, 'aiChats'),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const chats = querySnapshot.docs.map(doc => {
      const data = doc.data();
      let timestamp;

      // Handle different timestamp formats
      if (data.updatedAt && typeof data.updatedAt.toMillis === 'function') {
        // It's a Firestore Timestamp
        timestamp = data.updatedAt.toMillis();
      } else if (data.updatedAt && data.updatedAt._seconds) {
        // It's a Firestore Timestamp in serialized form
        timestamp = data.updatedAt._seconds * 1000;
      } else if (data.createdAt && typeof data.createdAt.toMillis === 'function') {
        // Fall back to createdAt if updatedAt is not available
        timestamp = data.createdAt.toMillis();
      } else if (data.createdAt && data.createdAt._seconds) {
        timestamp = data.createdAt._seconds * 1000;
      } else if (data.timestamp) {
        // Use timestamp field if available
        timestamp = data.timestamp;
      } else {
        // Fallback to current time
        timestamp = Date.now();
      }

      return {
        id: doc.id,
        ...data,
        timestamp: timestamp
      };
    });

    return chats;
  } catch (error) {
    console.error('Error getting user chat history:', error);
    throw error;
  }
};

// Delete a chat from Firebase
export const deleteAIChat = async (chatId: string) => {
  try {
    const chatRef = doc(db, 'aiChats', chatId);
    await deleteDoc(chatRef);
    return true;
  } catch (error) {
    console.error('Error deleting AI chat:', error);
    throw error;
  }
};

// Toggle starred status for a chat
export const toggleChatStarred = async (chatId: string, isStarred: boolean) => {
  try {
    const chatRef = doc(db, 'aiChats', chatId);
    await updateDoc(chatRef, {
      isStarred,
      updatedAt: Timestamp.now(),
    });
    return true;
  } catch (error) {
    console.error('Error updating starred status:', error);
    throw error;
  }
};

// Toggle pinned status for a chat
export const toggleChatPinned = async (chatId: string, isPinned: boolean) => {
  try {
    const chatRef = doc(db, 'aiChats', chatId);
    await updateDoc(chatRef, {
      isPinned,
      updatedAt: Timestamp.now(),
    });
    return true;
  } catch (error) {
    console.error('Error updating pinned status:', error);
    throw error;
  }
};

// Modify incrementChatViewCount to use structured date
export const incrementChatViewCount = async (chatId: string) => {
  if (!chatId) {
    console.error('Firebase: incrementChatViewCount called with empty chatId');
    return false;
  }

  try {
    console.log('Firebase: Incrementing view count for chat:', chatId);
    const chatRef = doc(db, 'aiChats', chatId);

    // First check if document exists
    const docSnap = await getDoc(chatRef);
    if (!docSnap.exists()) {
      console.error('Firebase: Cannot increment view count - chat not found:', chatId);
      return false;
    }

    await updateDoc(chatRef, {
      viewCount: increment(1),
      updatedAt: formatDateToStructure() // Use structured date
    });

    console.log('Firebase: View count incremented successfully');
    return true;
  } catch (error) {
    console.error('Firebase: Error incrementing view count:', error);
    return false;
  }
};

// Migration function to fix createdAt fields in existing documents
export const migrateAIChatsTimestamps = async () => {
  try {
    console.log("Starting migration of AI chats timestamps...");
    const q = query(collection(db, 'aiChats'));
    const querySnapshot = await getDocs(q);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();

      // Check if createdAt is not a proper Firestore Timestamp
      const needsMigration = !data.createdAt ||
                             typeof data.createdAt.toMillis !== 'function';

      if (needsMigration) {
        console.log(`Migrating document ${docSnapshot.id}`);

        // Use timestamp if available, otherwise use current time
        const timestamp = data.timestamp || Date.now();
        const createdAt = Timestamp.fromMillis(
          typeof timestamp === 'number' ? timestamp : Date.now()
        );

        // Update the document with proper Timestamp objects
        await updateDoc(docSnapshot.ref, {
          createdAt: createdAt,
          updatedAt: Timestamp.now()
        });

        migratedCount++;
      } else {
        skippedCount++;
      }
    }

    console.log(`Migration completed: ${migratedCount} documents migrated, ${skippedCount} documents skipped.`);
    return { migratedCount, skippedCount };
  } catch (error) {
    console.error("Error during migration:", error);
    throw error;
  }
};

// Get user profile data
export const getUserProfile = async (uid: string) => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return null;
    }

    return userSnap.data();
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

// Check if user is the special Developer user
export const isDeveloperUser = async (uid: string): Promise<boolean> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return false;
    }

    const userData = userSnap.data();
    return userData?.username === "Developer";
  } catch (error) {
    console.error('Error checking if user is developer:', error);
    return false;
  }
};

// Update subject color in Firebase
export const updateSubjectColor = async (uid: string, subjectId: string, color: string) => {
  try {
    const userSubjectsRef = doc(db, 'userSubjects', uid);
    const userSubjectsSnap = await getDoc(userSubjectsRef);

    if (!userSubjectsSnap.exists()) {
      console.error('User subjects not found');
      return false;
    }

    const userData = userSubjectsSnap.data();
    const subjects = userData.subjects || [];

    const updatedSubjects = subjects.map((subject: any) => {
      if (subject.id === subjectId) {
        return { ...subject, color: color };
      }
      return subject;
    });

    await setDoc(userSubjectsRef, { subjects: updatedSubjects }, { merge: true });
    return true;
  } catch (error) {
    console.error('Error updating subject color:', error);
    return false;
  }
};

export const getRecentPublicQnAs = async (limitCount = 10): Promise<PublicQnA[]> => {
  try {
    const qnasRef = collection(db, 'publicQnAs');
    const q = query(qnasRef,
      where('status', '==', 'approved'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);

    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        questionText: data.questionText || '',
        answerText: data.answerText || '',
        slug: data.slug || '',
        createdAt: data.createdAt || 0,
        updatedAt: data.updatedAt || 0,
        userId: data.userId || '',
        viewCount: data.viewCount || 0,
        tags: data.tags || []
      } as PublicQnA;
    });
  } catch (error) {
    console.error('Error fetching recent public Q&As:', error);
    return [];
  }
};

export const getPopularPublicQnAs = async (limitCount = 10): Promise<PublicQnA[]> => {
  try {
    const qnasRef = collection(db, 'publicQnAs');
    const q = query(qnasRef,
      where('status', '==', 'approved'),
      orderBy('viewCount', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);

    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        questionText: data.questionText || '',
        answerText: data.answerText || '',
        slug: data.slug || '',
        createdAt: data.createdAt || 0,
        updatedAt: data.updatedAt || 0,
        userId: data.userId || '',
        viewCount: data.viewCount || 0,
        tags: data.tags || []
      } as PublicQnA;
    });
  } catch (error) {
    console.error('Error fetching popular public Q&As:', error);
    return [];
  }
};

export const getPublicQnABySlug = async (slug: string): Promise<PublicQnA | null> => {
  try {
    if (!slug) {
      console.error('Invalid slug provided');
      return null;
    }

    const qnasRef = collection(db, 'publicQnAs');
    const q = query(qnasRef, where('slug', '==', slug), limit(1));
    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      console.log(`No Q&A found with slug: ${slug}`);
      return null;
    }

    const doc = snapshot.docs[0];
    const data = doc.data();

    return {
      id: doc.id,
      questionText: data.questionText || '',
      answerText: data.answerText || '',
      slug: data.slug || '',
      createdAt: data.createdAt || 0,
      updatedAt: data.updatedAt || 0,
      userId: data.userId || '',
      viewCount: data.viewCount || 0,
      tags: data.tags || []
    } as PublicQnA;
  } catch (error) {
    console.error('Error fetching Q&A by slug:', error);
    return null;
  }
};

// Get oldest shared chats for display in directory (ordered from oldest to newest)
export const getOldestSharedChats = async (limitCount = 30, lastDoc: QueryDocumentSnapshot<DocumentData> | null = null): Promise<{ chats: SharedChatSEO[], lastDoc: QueryDocumentSnapshot<DocumentData> | null }> => {
  try {
    // Explicitly use collection from non-authenticated context
    const chatsRef = collection(db, 'aiChats');
    console.log('Firebase: Getting oldest shared chats, limit:', limitCount);

    // Handle larger limits with multiple queries if needed
    const MAX_LIMIT = 100; // Firebase query limit per request

    if (limitCount <= MAX_LIMIT) {
      // Standard query when limit is within range
      let q;
      if (lastDoc) {
        q = query(
          chatsRef,
          orderBy('createdAt', 'asc'), // Order from oldest to newest
          startAfter(lastDoc),
          limit(limitCount)
        );
      } else {
        q = query(
          chatsRef,
          orderBy('createdAt', 'asc'), // Order from oldest to newest
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      console.log('Firebase: Got docs count:', querySnapshot.docs.length);
      const lastVisible = querySnapshot.docs.length > 0 ? querySnapshot.docs[querySnapshot.docs.length - 1] : null;

      const chats = querySnapshot.docs.map(doc => {
        const data = doc.data() as DocumentData;
        return {
          id: doc.id,
          title: data.title || (data.messages && data.messages.length > 0
            ? generateChatTitle(data.messages[0].content)
            : 'Chat Conversation'),
          slug: data.slug || createSlugFromTitle(data.title || 'chat-conversation'),
          createdAt: data.createdAt ?
            (typeof data.createdAt.toMillis === 'function' ?
              data.createdAt.toMillis() : data.createdAt) :
            Date.now(),
          updatedAt: data.updatedAt ?
            (typeof data.updatedAt.toMillis === 'function' ?
              data.updatedAt.toMillis() : data.updatedAt) :
            Date.now(),
          userId: data.userId,
          viewCount: data.viewCount || 0,
          chatMessages: data.messages || [],
          status: (data.status as 'approved' | 'pending' | 'rejected') || 'approved',
          image: data.messages && data.messages.length > 0 ?
            data.messages[0].image : undefined,
          tags: data.tags || []
        } as SharedChatSEO;
      });

      return { chats, lastDoc: lastVisible };
    } else {
      // For larger limits, we need to split into multiple queries
      console.log('Firebase: Large limit requested, using multiple queries');
      let allChats: SharedChatSEO[] = [];
      let currentLastDoc = lastDoc;
      let remainingCount = limitCount;

      while (remainingCount > 0) {
        const batchSize = Math.min(remainingCount, MAX_LIMIT);
        let q;

        if (currentLastDoc) {
          q = query(
            chatsRef,
            orderBy('createdAt', 'asc'), // Order from oldest to newest
            startAfter(currentLastDoc),
            limit(batchSize)
          );
        } else {
          q = query(
            chatsRef,
            orderBy('createdAt', 'asc'), // Order from oldest to newest
            limit(batchSize)
          );
        }

        const querySnapshot = await getDocs(q);
        console.log(`Firebase: Batch query got ${querySnapshot.docs.length} docs`);

        if (querySnapshot.empty) {
          break; // No more results
        }

        const batchChats = querySnapshot.docs.map(doc => {
          const data = doc.data() as DocumentData;
          return {
            id: doc.id,
            title: data.title || (data.messages && data.messages.length > 0
              ? generateChatTitle(data.messages[0].content)
              : 'Chat Conversation'),
            slug: data.slug || createSlugFromTitle(data.title || 'chat-conversation'),
            createdAt: data.createdAt ?
              (typeof data.createdAt.toMillis === 'function' ?
                data.createdAt.toMillis() : data.createdAt) :
              Date.now(),
            updatedAt: data.updatedAt ?
              (typeof data.updatedAt.toMillis === 'function' ?
                data.updatedAt.toMillis() : data.updatedAt) :
              Date.now(),
            userId: data.userId,
            viewCount: data.viewCount || 0,
            chatMessages: data.messages || [],
            status: (data.status as 'approved' | 'pending' | 'rejected') || 'approved',
            image: data.messages && data.messages.length > 0 ?
              data.messages[0].image : undefined,
            tags: data.tags || []
          } as SharedChatSEO;
        });

        allChats = [...allChats, ...batchChats];
        remainingCount -= batchChats.length;

        // If we got fewer results than requested, we've reached the end
        if (querySnapshot.docs.length < batchSize) {
          break;
        }

        // Update the last document reference for the next batch
        currentLastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
      }

      return {
        chats: allChats,
        lastDoc: allChats.length > 0 ? currentLastDoc : null
      };
    }
  } catch (error) {
    console.error('Error getting oldest shared chats:', error);
    return { chats: [], lastDoc: null };
  }
};

// Link account with Email & Password
export const linkWithEmailPassword = async (
  password: string
): Promise<User> => {
  const user = auth.currentUser;
  if (!user || !user.email) {
    throw new Error('User not logged in or email not available');
  }

  try {
    const credential = EmailAuthProvider.credential(user.email, password);
    const result = await linkWithCredential(user, credential);
    return result.user;
  } catch (error: any) {
    console.error('Error linking with email/password:', error);
    throw error;
  }
};

// Link account with Google
export const linkWithGoogle = async (): Promise<User> => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('User not logged in');
  }

  try {
    const result = await linkWithPopup(user, googleProvider);
    return result.user;
  } catch (error: any) {
    console.error('Error linking with Google:', error);
    throw error;
  }
};

// Unlink a specific provider
export const unlinkProvider = async (providerId: string): Promise<User> => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('User not logged in');
  }

  try {
    return await unlink(user, providerId);
  } catch (error: any) {
    console.error('Error unlinking provider:', error);
    throw error;
  }
};

// Get current user's providers
export const getUserProviders = (): string[] => {
  const user = auth.currentUser;
  if (!user) {
    return [];
  }

  return user.providerData.map(provider => provider.providerId);
};

// Re-authenticate user with password
export const reauthenticateWithPassword = async (
  email: string,
  password: string
): Promise<User> => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('User not logged in');
  }

  try {
    const credential = EmailAuthProvider.credential(email, password);
    const result = await reauthenticateWithCredential(user, credential);
    return result.user;
  } catch (error: any) {
    console.error('Error re-authenticating with password:', error);
    throw error;
  }
};

// Get user's daily target hours
export const getUserDailyTargetHours = async (uid: string): Promise<number> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return 6; // Default value if user document doesn't exist
    }

    const userData = userSnap.data();
    return userData.dailyTargetHours || 6; // Default to 6 hours if not set
  } catch (error) {
    console.error('Error getting user daily target hours:', error);
    return 6; // Default value in case of error
  }
};

// Update user's daily target hours
export const updateUserDailyTargetHours = async (uid: string, hours: number): Promise<void> => {
  try {
    const userRef = doc(db, 'users', uid);
    await setDoc(userRef, {
      dailyTargetHours: hours,
      updatedAt: new Date().toISOString(),
    }, { merge: true });
  } catch (error) {
    console.error('Error updating user daily target hours:', error);
    throw error;
  }
};

// Get user's daily motivation
export const getUserDailyMotivation = async (uid: string): Promise<{quote: string, author: string}> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return {
        quote: "The expert in anything was once a beginner. The secret of getting ahead is getting started.",
        author: "Mark Twain"
      }; // Default value if user document doesn't exist
    }

    const userData = userSnap.data();
    return {
      quote: userData.dailyMotivationQuote || "The expert in anything was once a beginner. The secret of getting ahead is getting started.",
      author: userData.dailyMotivationAuthor || "Mark Twain"
    }; // Default values if not set
  } catch (error) {
    console.error('Error getting user daily motivation:', error);
    return {
      quote: "The expert in anything was once a beginner. The secret of getting ahead is getting started.",
      author: "Mark Twain"
    }; // Default value in case of error
  }
};

// Update user's daily motivation
export const updateUserDailyMotivation = async (uid: string, motivation: {quote: string, author: string}): Promise<void> => {
  try {
    const userRef = doc(db, 'users', uid);
    await setDoc(userRef, {
      dailyMotivationQuote: motivation.quote,
      dailyMotivationAuthor: motivation.author,
      updatedAt: new Date().toISOString(),
    }, { merge: true });
  } catch (error) {
    console.error('Error updating user daily motivation:', error);
    throw error;
  }
};

// Upload and update user profile picture
export const updateUserProfilePicture = async (uid: string, file: File): Promise<string> => {
  try {
    // Import dynamically to avoid circular dependencies
    const { uploadImageToCloudinary } = await import('./imageUtils');

    // Upload to Cloudinary
    const result = await uploadImageToCloudinary(file, {
      folder: 'profile_pictures',
      tags: ['profile', 'user', uid]
    });

    // Update user profile with new photo URL
    await updateUserProfile(uid, {
      photoURL: result.secure_url
    });

    return result.secure_url;
  } catch (error) {
    console.error('Error updating profile picture:', error);
    throw error;
  }
};

// Upload and update user background image
export const updateUserBackgroundImage = async (uid: string, file: File): Promise<string> => {
  try {
    // Import dynamically to avoid circular dependencies
    const { uploadImageToCloudinary } = await import('./imageUtils');

    // Upload to Cloudinary
    const result = await uploadImageToCloudinary(file, {
      folder: 'background_images',
      tags: ['background', 'user', uid]
    });

    // Update user profile with new background image URL
    await updateUserProfile(uid, {
      backgroundImage: result.secure_url
    });

    return result.secure_url;
  } catch (error) {
    console.error('Error updating background image:', error);
    throw error;
  }
};

// Increment profile view count
export const incrementProfileViews = async (uid: string): Promise<void> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) return;

    const currentStats = userSnap.data().stats || {};
    const updatedStats = {
      ...currentStats,
      profileViews: (currentStats.profileViews || 0) + 1
    };

    await setDoc(userRef, {
      stats: updatedStats,
      updatedAt: new Date().toISOString(),
    }, { merge: true });
  } catch (error) {
    console.error('Error incrementing profile views:', error);
  }
};

// Calculate and update user reputation and rank
export const updateUserReputation = async (uid: string, pointsToAdd: number = 0): Promise<void> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) return;

    const currentStats = userSnap.data().stats || {};
    const currentPoints = currentStats.reputationPoints || 0;
    const newPoints = currentPoints + pointsToAdd;

    // Determine rank based on points
    let rank = 'Beginner';
    if (newPoints >= 1000) rank = 'Expert';
    else if (newPoints >= 500) rank = 'Advanced';
    else if (newPoints >= 100) rank = 'Intermediate';

    const updatedStats = {
      ...currentStats,
      reputationPoints: newPoints,
      rank
    };

    await setDoc(userRef, {
      stats: updatedStats,
      updatedAt: new Date().toISOString(),
    }, { merge: true });
  } catch (error) {
    console.error('Error updating user reputation:', error);
  }
};

// Get extended user profile with all data
export const getExtendedUserProfile = async (uid: string): Promise<any> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      return null;
    }

    // Get user stats
    const stats = await getUserStats(uid);

    // Increment profile views
    await incrementProfileViews(uid);

    // Return combined data
    return {
      ...userSnap.data(),
      stats
    };
  } catch (error) {
    console.error('Error getting extended user profile:', error);
    return null;
  }
};

// Update user's lastActive timestamp
export const updateUserLastActive = async (uid: string): Promise<void> => {
  if (!uid) return;

  try {
    const now = new Date().toISOString();

    // Get current user stats
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      console.warn(`User document ${uid} not found when updating lastActive`);
      return;
    }

    // Update both places where lastActive might be stored
    await updateDoc(userRef, {
      'lastLogin': now,
      'stats.lastActive': now
    });

    console.log(`Updated lastActive timestamp for user ${uid}`);
  } catch (error) {
    console.error('Error updating user lastActive timestamp:', error);
  }
};


