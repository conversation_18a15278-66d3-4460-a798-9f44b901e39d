import { 
  collection, 
  addDoc, 
  doc, 
  updateDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit as firestoreLimit,
  startAfter,
  increment,
  arrayUnion,
  arrayRemove,
  setDoc,
  deleteDoc,
  Timestamp,
  serverTimestamp,
  QueryDocumentSnapshot,
  DocumentData
} from 'firebase/firestore';
import { db } from './firebase';
import { 
  Question, 
  Answer, 
  Comment, 
  QASearchParams, 
  QASearchResult,
  QAMetadata
} from '../types/qa';

// Collection references
const questionsCollection = collection(db, 'questions');
const answersCollection = collection(db, 'answers');
const commentsCollection = collection(db, 'comments');
const metadataCollection = collection(db, 'qaMetadata');

// Question operations
export const addQuestion = async (questionData: Omit<Question, 'id' | 'views' | 'upvotes' | 'downvotes' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const newQuestion = {
      ...questionData,
      views: 0,
      upvotes: 0,
      downvotes: 0,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    
    const docRef = await addDoc(questionsCollection, newQuestion);
    
    // Update metadata
    await updateQAMetadata(questionData);
    
    return docRef.id;
  } catch (error) {
    console.error('Error adding question:', error);
    throw error;
  }
};

export const getQuestion = async (questionId: string): Promise<Question | null> => {
  try {
    const questionDoc = await getDoc(doc(db, 'questions', questionId));
    
    if (!questionDoc.exists()) {
      return null;
    }
    
    // Increment view count
    await updateDoc(doc(db, 'questions', questionId), {
      views: increment(1)
    });
    
    return { id: questionDoc.id, ...questionDoc.data() } as Question;
  } catch (error) {
    console.error('Error getting question:', error);
    throw error;
  }
};

export const updateQuestion = async (questionId: string, questionData: Partial<Question>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'questions', questionId), {
      ...questionData,
      updatedAt: Date.now()
    });
    
    // Update metadata if necessary
    if (questionData.subject || questionData.topic || questionData.subtopic || questionData.tags) {
      const fullQuestion = await getQuestion(questionId);
      if (fullQuestion) {
        await updateQAMetadata(fullQuestion);
      }
    }
  } catch (error) {
    console.error('Error updating question:', error);
    throw error;
  }
};

export const deleteQuestion = async (questionId: string): Promise<void> => {
  try {
    // Delete the question
    await deleteDoc(doc(db, 'questions', questionId));
    
    // Delete associated answers
    const answersQuery = query(answersCollection, where('questionId', '==', questionId));
    const answersSnapshot = await getDocs(answersQuery);
    
    answersSnapshot.forEach(async (answerDoc) => {
      await deleteDoc(doc(db, 'answers', answerDoc.id));
    });
    
    // Delete associated comments
    const commentsQuery = query(commentsCollection, where('questionId', '==', questionId));
    const commentsSnapshot = await getDocs(commentsQuery);
    
    commentsSnapshot.forEach(async (commentDoc) => {
      await deleteDoc(doc(db, 'comments', commentDoc.id));
    });
  } catch (error) {
    console.error('Error deleting question:', error);
    throw error;
  }
};

export const upvoteQuestion = async (questionId: string, userId: string): Promise<void> => {
  try {
    const questionRef = doc(db, 'questions', questionId);
    const questionDoc = await getDoc(questionRef);
    
    if (!questionDoc.exists()) {
      throw new Error('Question not found');
    }
    
    const questionData = questionDoc.data();
    const upvotedBy = questionData.upvotedBy || [];
    const downvotedBy = questionData.downvotedBy || [];
    
    // Check if user already upvoted
    if (upvotedBy.includes(userId)) {
      // Remove upvote
      await updateDoc(questionRef, {
        upvotes: increment(-1),
        upvotedBy: arrayRemove(userId)
      });
    } else {
      // Add upvote
      await updateDoc(questionRef, {
        upvotes: increment(1),
        upvotedBy: arrayUnion(userId)
      });
      
      // Remove downvote if exists
      if (downvotedBy.includes(userId)) {
        await updateDoc(questionRef, {
          downvotes: increment(-1),
          downvotedBy: arrayRemove(userId)
        });
      }
    }
  } catch (error) {
    console.error('Error upvoting question:', error);
    throw error;
  }
};

export const downvoteQuestion = async (questionId: string, userId: string): Promise<void> => {
  try {
    const questionRef = doc(db, 'questions', questionId);
    const questionDoc = await getDoc(questionRef);
    
    if (!questionDoc.exists()) {
      throw new Error('Question not found');
    }
    
    const questionData = questionDoc.data();
    const upvotedBy = questionData.upvotedBy || [];
    const downvotedBy = questionData.downvotedBy || [];
    
    // Check if user already downvoted
    if (downvotedBy.includes(userId)) {
      // Remove downvote
      await updateDoc(questionRef, {
        downvotes: increment(-1),
        downvotedBy: arrayRemove(userId)
      });
    } else {
      // Add downvote
      await updateDoc(questionRef, {
        downvotes: increment(1),
        downvotedBy: arrayUnion(userId)
      });
      
      // Remove upvote if exists
      if (upvotedBy.includes(userId)) {
        await updateDoc(questionRef, {
          upvotes: increment(-1),
          upvotedBy: arrayRemove(userId)
        });
      }
    }
  } catch (error) {
    console.error('Error downvoting question:', error);
    throw error;
  }
};

// Answer operations
export const addAnswer = async (answerData: Omit<Answer, 'id' | 'upvotes' | 'downvotes' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const newAnswer = {
      ...answerData,
      upvotes: 0,
      downvotes: 0,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    
    const docRef = await addDoc(answersCollection, newAnswer);
    return docRef.id;
  } catch (error) {
    console.error('Error adding answer:', error);
    throw error;
  }
};

export const getAnswer = async (answerId: string): Promise<Answer | null> => {
  try {
    const answerDoc = await getDoc(doc(db, 'answers', answerId));
    
    if (!answerDoc.exists()) {
      return null;
    }
    
    return { id: answerDoc.id, ...answerDoc.data() } as Answer;
  } catch (error) {
    console.error('Error getting answer:', error);
    throw error;
  }
};

export const getAnswersForQuestion = async (questionId: string): Promise<Answer[]> => {
  try {
    const answersQuery = query(
      answersCollection, 
      where('questionId', '==', questionId),
      orderBy('isVerified', 'desc'),
      orderBy('upvotes', 'desc')
    );
    
    const answersSnapshot = await getDocs(answersQuery);
    
    return answersSnapshot.docs.map(doc => ({ 
      id: doc.id, 
      ...doc.data() 
    })) as Answer[];
  } catch (error) {
    console.error('Error getting answers for question:', error);
    throw error;
  }
};

export const updateAnswer = async (answerId: string, answerData: Partial<Answer>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'answers', answerId), {
      ...answerData,
      updatedAt: Date.now()
    });
  } catch (error) {
    console.error('Error updating answer:', error);
    throw error;
  }
};

export const deleteAnswer = async (answerId: string): Promise<void> => {
  try {
    // Delete the answer
    await deleteDoc(doc(db, 'answers', answerId));
    
    // Delete associated comments
    const commentsQuery = query(commentsCollection, where('answerId', '==', answerId));
    const commentsSnapshot = await getDocs(commentsQuery);
    
    commentsSnapshot.forEach(async (commentDoc) => {
      await deleteDoc(doc(db, 'comments', commentDoc.id));
    });
  } catch (error) {
    console.error('Error deleting answer:', error);
    throw error;
  }
};

export const upvoteAnswer = async (answerId: string, userId: string): Promise<void> => {
  try {
    const answerRef = doc(db, 'answers', answerId);
    const answerDoc = await getDoc(answerRef);
    
    if (!answerDoc.exists()) {
      throw new Error('Answer not found');
    }
    
    const answerData = answerDoc.data();
    const upvotedBy = answerData.upvotedBy || [];
    const downvotedBy = answerData.downvotedBy || [];
    
    // Check if user already upvoted
    if (upvotedBy.includes(userId)) {
      // Remove upvote
      await updateDoc(answerRef, {
        upvotes: increment(-1),
        upvotedBy: arrayRemove(userId)
      });
    } else {
      // Add upvote
      await updateDoc(answerRef, {
        upvotes: increment(1),
        upvotedBy: arrayUnion(userId)
      });
      
      // Remove downvote if exists
      if (downvotedBy.includes(userId)) {
        await updateDoc(answerRef, {
          downvotes: increment(-1),
          downvotedBy: arrayRemove(userId)
        });
      }
    }
  } catch (error) {
    console.error('Error upvoting answer:', error);
    throw error;
  }
};

export const downvoteAnswer = async (answerId: string, userId: string): Promise<void> => {
  try {
    const answerRef = doc(db, 'answers', answerId);
    const answerDoc = await getDoc(answerRef);
    
    if (!answerDoc.exists()) {
      throw new Error('Answer not found');
    }
    
    const answerData = answerDoc.data();
    const upvotedBy = answerData.upvotedBy || [];
    const downvotedBy = answerData.downvotedBy || [];
    
    // Check if user already downvoted
    if (downvotedBy.includes(userId)) {
      // Remove downvote
      await updateDoc(answerRef, {
        downvotes: increment(-1),
        downvotedBy: arrayRemove(userId)
      });
    } else {
      // Add downvote
      await updateDoc(answerRef, {
        downvotes: increment(1),
        downvotedBy: arrayUnion(userId)
      });
      
      // Remove upvote if exists
      if (upvotedBy.includes(userId)) {
        await updateDoc(answerRef, {
          upvotes: increment(-1),
          upvotedBy: arrayRemove(userId)
        });
      }
    }
  } catch (error) {
    console.error('Error downvoting answer:', error);
    throw error;
  }
};

// Comment operations
export const addComment = async (commentData: Omit<Comment, 'id' | 'timestamp'>): Promise<string> => {
  try {
    const newComment = {
      ...commentData,
      timestamp: Date.now()
    };
    
    const docRef = await addDoc(commentsCollection, newComment);
    return docRef.id;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

export const getCommentsForQuestion = async (questionId: string): Promise<Comment[]> => {
  try {
    const commentsQuery = query(
      commentsCollection, 
      where('questionId', '==', questionId),
      where('parentId', '==', null),
      orderBy('timestamp', 'desc')
    );
    
    const commentsSnapshot = await getDocs(commentsQuery);
    
    const comments = commentsSnapshot.docs.map(doc => ({ 
      id: doc.id, 
      ...doc.data() 
    })) as Comment[];
    
    // Get replies for each comment
    for (const comment of comments) {
      const repliesQuery = query(
        commentsCollection,
        where('parentId', '==', comment.id),
        orderBy('timestamp', 'asc')
      );
      
      const repliesSnapshot = await getDocs(repliesQuery);
      
      comment.replies = repliesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Comment[];
    }
    
    return comments;
  } catch (error) {
    console.error('Error getting comments for question:', error);
    throw error;
  }
};

export const getCommentsForAnswer = async (answerId: string): Promise<Comment[]> => {
  try {
    const commentsQuery = query(
      commentsCollection, 
      where('answerId', '==', answerId),
      where('parentId', '==', null),
      orderBy('timestamp', 'desc')
    );
    
    const commentsSnapshot = await getDocs(commentsQuery);
    
    const comments = commentsSnapshot.docs.map(doc => ({ 
      id: doc.id, 
      ...doc.data() 
    })) as Comment[];
    
    // Get replies for each comment
    for (const comment of comments) {
      const repliesQuery = query(
        commentsCollection,
        where('parentId', '==', comment.id),
        orderBy('timestamp', 'asc')
      );
      
      const repliesSnapshot = await getDocs(repliesQuery);
      
      comment.replies = repliesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Comment[];
    }
    
    return comments;
  } catch (error) {
    console.error('Error getting comments for answer:', error);
    throw error;
  }
};

export const updateComment = async (commentId: string, content: string): Promise<void> => {
  try {
    await updateDoc(doc(db, 'comments', commentId), {
      content
    });
  } catch (error) {
    console.error('Error updating comment:', error);
    throw error;
  }
};

export const deleteComment = async (commentId: string): Promise<void> => {
  try {
    // Delete the comment
    await deleteDoc(doc(db, 'comments', commentId));
    
    // Delete replies
    const repliesQuery = query(commentsCollection, where('parentId', '==', commentId));
    const repliesSnapshot = await getDocs(repliesQuery);
    
    repliesSnapshot.forEach(async (replyDoc) => {
      await deleteDoc(doc(db, 'comments', replyDoc.id));
    });
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

// Search operations
export const searchQuestions = async (searchParams: QASearchParams): Promise<QASearchResult> => {
  try {
    let q = query(questionsCollection);
    
    // Apply filters
    if (searchParams.subject) {
      q = query(q, where('subject', '==', searchParams.subject));
    }
    
    if (searchParams.topic) {
      q = query(q, where('topic', '==', searchParams.topic));
    }
    
    if (searchParams.difficulty) {
      q = query(q, where('difficulty', '==', searchParams.difficulty));
    }
    
    if (searchParams.questionType) {
      q = query(q, where('questionType', '==', searchParams.questionType));
    }
    
    if (searchParams.examType) {
      q = query(q, where('examRelevance', 'array-contains', searchParams.examType));
    }
    
    // Order by most recent
    q = query(q, orderBy('createdAt', 'desc'));
    
    // Apply pagination
    const limitValue = searchParams.limit || 10;
    q = query(q, firestoreLimit(limitValue + 1)); // Get one extra to check if there are more
    
    // Apply offset if provided
    let lastDoc: QueryDocumentSnapshot<DocumentData> | null = null;
    if (searchParams.offset) {
      // Get the document at the offset
      const offsetQuery = query(
        questionsCollection,
        orderBy('createdAt', 'desc'),
        firestoreLimit(1),
        startAfter(searchParams.offset)
      );
      
      const offsetSnapshot = await getDocs(offsetQuery);
      if (!offsetSnapshot.empty) {
        lastDoc = offsetSnapshot.docs[0];
        q = query(q, startAfter(lastDoc));
      }
    }
    
    const snapshot = await getDocs(q);
    
    // Check if there are more results
    const hasMore = snapshot.docs.length > limitValue;
    
    // Remove the extra document if there are more
    const docs = hasMore ? snapshot.docs.slice(0, limitValue) : snapshot.docs;
    
    // Convert to Question objects
    const questions = docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Question[];
    
    // If there's a text query, filter the results client-side
    // This is a simple implementation - for production, consider using a dedicated search service
    let filteredQuestions = questions;
    if (searchParams.query) {
      const query = searchParams.query.toLowerCase();
      filteredQuestions = questions.filter(question => 
        question.title.toLowerCase().includes(query) ||
        question.content.toLowerCase().includes(query) ||
        question.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    return {
      questions: filteredQuestions,
      total: filteredQuestions.length,
      hasMore
    };
  } catch (error) {
    console.error('Error searching questions:', error);
    throw error;
  }
};

// Metadata operations
export const getQAMetadata = async (): Promise<QAMetadata> => {
  try {
    const metadataDoc = await getDoc(doc(metadataCollection, 'metadata'));
    
    if (!metadataDoc.exists()) {
      // Initialize with default values if not exists
      const defaultMetadata: QAMetadata = {
        subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology'],
        topics: {
          'Physics': [],
          'Chemistry': [],
          'Mathematics': [],
          'Biology': []
        },
        subtopics: {},
        tags: []
      };
      
      await setDoc(doc(metadataCollection, 'metadata'), defaultMetadata as any);
      return defaultMetadata;
    }
    
    return metadataDoc.data() as QAMetadata;
  } catch (error) {
    console.error('Error getting QA metadata:', error);
    throw error;
  }
};

const updateQAMetadata = async (question: Partial<Question>): Promise<void> => {
  try {
    const metadataRef = doc(metadataCollection, 'metadata');
    const metadataDoc = await getDoc(metadataRef);
    
    if (!metadataDoc.exists()) {
      await getQAMetadata(); // Initialize with default values
      return;
    }
    
    const metadata = metadataDoc.data() as QAMetadata;
    let updated = false;
    
    // Update topics if subject and topic are provided
    if (question.subject && question.topic) {
      if (!metadata.topics[question.subject].includes(question.topic)) {
        metadata.topics[question.subject].push(question.topic);
        updated = true;
      }
    }
    
    // Update subtopics if topic and subtopic are provided
    if (question.topic && question.subtopic) {
      if (!metadata.subtopics[question.topic]) {
        metadata.subtopics[question.topic] = [];
      }
      
      if (!metadata.subtopics[question.topic].includes(question.subtopic)) {
        metadata.subtopics[question.topic].push(question.subtopic);
        updated = true;
      }
    }
    
    // Update tags
    if (question.tags) {
      question.tags.forEach(tag => {
        if (!metadata.tags.includes(tag)) {
          metadata.tags.push(tag);
          updated = true;
        }
      });
    }
    
    if (updated) {
      await updateDoc(metadataRef, metadata as any);
    }
  } catch (error) {
    console.error('Error updating QA metadata:', error);
    throw error;
  }
};

// Related questions operations
export const getRelatedQuestions = async (questionId: string, limit = 5): Promise<Question[]> => {
  try {
    const questionDoc = await getDoc(doc(db, 'questions', questionId));
    
    if (!questionDoc.exists()) {
      return [];
    }
    
    const question = { id: questionDoc.id, ...questionDoc.data() } as Question;
    
    // If the question has related questions, fetch them
    if (question.relatedQuestions && question.relatedQuestions.length > 0) {
      const relatedQuestions: Question[] = [];
      
      for (const relatedId of question.relatedQuestions.slice(0, limit)) {
        const relatedDoc = await getDoc(doc(db, 'questions', relatedId));
        
        if (relatedDoc.exists()) {
          relatedQuestions.push({ id: relatedDoc.id, ...relatedDoc.data() } as Question);
        }
      }
      
      return relatedQuestions;
    }
    
    // Otherwise, find questions with the same topic
    const relatedQuery = query(
      questionsCollection,
      where('subject', '==', question.subject),
      where('topic', '==', question.topic),
      where('id', '!=', questionId),
      orderBy('id'),
      orderBy('upvotes', 'desc'),
      firestoreLimit(limit)
    );
    
    const relatedSnapshot = await getDocs(relatedQuery);
    
    return relatedSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Question[];
  } catch (error) {
    console.error('Error getting related questions:', error);
    throw error;
  }
}; 