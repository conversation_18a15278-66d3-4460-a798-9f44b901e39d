import { createContext, useContext, useEffect, useState } from 'react';
import { User, getAuth, getRedirectResult } from 'firebase/auth';
import {
  signInWithGoogle as firebaseSignInWithGoogle,
  signOut as firebaseSignOut,
  onAuthStateChange,
  // checkUserProfile, // Removed as userDataManager will handle profile logic
  signUpWithEmailPassword as firebaseSignUpWithEmailPassword,
  signInWithEmailPassword as firebaseSignInWithEmailPassword
} from '../utils/firebase';
import { useLocation, useNavigate } from 'react-router-dom';
import { handleUserAuthentication, updateUserProfileInFirestore, getUserProfile } from '../utils/userDataManager'; // Added import
import { useUserStore } from '../stores/userStore'; // Added import

interface SignInResult {
  user: User;
  hasProfile: boolean;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signInWithGoogle: () => Promise<SignInResult | null>;
  signInWithEmailPassword: (email: string, password: string) => Promise<SignInResult>;
  signUpWithEmailPassword: (email: string, password: string) => Promise<SignInResult>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Function to identify user in Featurebase
const identifyFeaturebaseUser = (user: User) => {
  if (typeof window.Featurebase === 'function') {
    window.Featurebase(
      "identify",
      {
        organization: "isotopeai",
        email: user.email || undefined,
        name: user.displayName || undefined,
        userId: user.uid,
        profilePicture: user.photoURL || undefined,
      },
      (err) => {
        if (err) {
          console.error("Featurebase identification error:", err);
        }
      }
    );
  }
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    let mounted = true;
    let authStateListenerSetup = false; // Flag to ensure listener is set up only once

    // Handle redirect result from authentication providers
    const handleRedirectResult = async () => {
      setLoading(true); // Start loading indicator
      try {
        // Add a timeout for getRedirectResult (e.g., 5 seconds)
        const redirectResultPromise = getRedirectResult(getAuth());
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Redirect result timeout')), 5000)
        );

        // Explicitly type the expected result structure from Firebase redirect
        type RedirectResultType = import('firebase/auth').UserCredential | null;
        const result = await Promise.race([redirectResultPromise, timeoutPromise]) as RedirectResultType;

        if (result?.user) {
          console.log("Redirect result processed successfully for user:", result.user.uid);
          // User is signed in via redirect
          // setUser(result.user); // User state will be set by handleUserAuthentication
          identifyFeaturebaseUser(result.user);
          await handleUserAuthentication(result.user); // Use new data manager

          // Profile check and navigation logic (can be further refined with userDataManager)
          const userProfile = useUserStore.getState().userProfile; // Get profile from store
          const searchParams = new URLSearchParams(location.search);
          const returnUrl = searchParams.get('returnUrl');
          const pendingInviteCode = localStorage.getItem('pendingInviteCode');

          if (!userProfile || !userProfile.username) { // Check for a more complete profile indicator like username
            navigate('/profile-setup', { replace: true });
          } else if (pendingInviteCode) {
            navigate(`/join?code=${pendingInviteCode}`, { replace: true });
          } else if (returnUrl) {
            navigate(returnUrl, { replace: true });
          } else {
            navigate('/ai', { replace: true });
          }
          return true; // Indicate redirect was handled
        } else {
          console.log("No redirect result found or user is null.");
          return false; // Indicate no redirect was handled
        }
      } catch (error: any) {
        if (error.message === 'Redirect result timeout') {
          console.warn('getRedirectResult timed out. Proceeding without redirect result.');
        } else {
          console.error('Error handling redirect result:', error);
          // Optionally show a toast message to the user here
        }
        return false; // Indicate redirect handling failed or timed out
      } finally {
        // Ensure loading is set to false only after auth state listener is potentially set up
        // setLoading(false); // Moved loading=false to after listener setup
      }
    };

    // Function to set up the auth state listener
    const setupAuthStateListener = () => {
      if (!mounted || authStateListenerSetup) return; // Prevent multiple setups
      authStateListenerSetup = true;

      const unsubscribe = onAuthStateChange(async (currentUser) => {
        if (!mounted) return;

        console.log("onAuthStateChange triggered. Current user:", currentUser?.uid);

        // Only update state if the user has actually changed
        // This prevents unnecessary updates if the redirect handler already set the user
        // Only update state if the user has actually changed
        if (currentUser?.uid !== user?.uid) {
          setUser(currentUser); // Set the user state in AuthContext
        }
        await handleUserAuthentication(currentUser);

        if (currentUser) {
          identifyFeaturebaseUser(currentUser);
          const userProfile = useUserStore.getState().userProfile;
          const isOnProfileSetup = location.pathname === '/profile-setup';
          const isOnLandingPage = location.pathname === '/';
          const pendingInviteCode = localStorage.getItem('pendingInviteCode');

          if (!userProfile || !userProfile.username) { // Check for a more complete profile indicator
            if (!isOnProfileSetup) {
              navigate('/profile-setup', { replace: true });
            }
          } else if (pendingInviteCode) {
            navigate(`/join?code=${pendingInviteCode}`, { replace: true });
          } else if (isOnProfileSetup || isOnLandingPage) {
            navigate('/ai', { replace: true });
          }
        } else {
          // User is null (logged out), handleUserAuthentication already clears the store.
          setUser(null); // Clear user state in AuthContext
        }

        // Set loading to false once the auth state is confirmed and processed
        setLoading(false);
      });

      // Return the unsubscribe function for cleanup
      return unsubscribe;
    };

    // Handle redirect result first, then set up auth state listener
    handleRedirectResult().then((redirectHandled) => {
      if (mounted) {
        // If redirect was handled, user state is already set.
        // If not handled, or timed out, rely on onAuthStateChange.
        // In either case, set up the listener now.
        const unsubscribe = setupAuthStateListener();

        // If redirect wasn't handled and listener isn't set up yet (edge case), ensure loading stops
        if (!redirectHandled && !authStateListenerSetup) {
           setLoading(false);
        }

        // Cleanup function
        return () => {
          mounted = false;
          if (unsubscribe) {
            unsubscribe();
          }
        };
      }
    });

    // Initial cleanup function in case the component unmounts before async operations complete
    return () => {
      mounted = false;
      // Note: The actual unsubscribe is handled within the .then() block's return
    };
  // Removed 'user' from dependency array to prevent re-running effect on user state change within the effect itself.
  // Added location.search to dependencies as returnUrl is derived from it.
  }, [navigate, location.pathname, location.search, user]); // Added 'user' to dependencies to ensure setUser updates trigger effect re-runs if needed.

  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      // Check for returnUrl in query parameters
      const searchParams = new URLSearchParams(location.search);
      const returnUrl = searchParams.get('returnUrl');

      const userCredential = await firebaseSignInWithGoogle();
      if (userCredential?.user) {
        identifyFeaturebaseUser(userCredential.user);
        await handleUserAuthentication(userCredential.user); // Ensure profile is loaded/cached
        setUser(userCredential.user); // Set user state after successful sign-in
        const userProfile = useUserStore.getState().userProfile;

        if (!userProfile || !userProfile.username) {
          navigate('/profile-setup', { replace: true });
          return { user: userCredential.user, hasProfile: false };
        }
        // If returnUrl is present, navigate there (returnUrl is in scope from function start)
        if (returnUrl) {
          navigate(returnUrl, { replace: true });
        } else {
          navigate('/ai', { replace: true }); // Default navigation
        }
        return { user: userCredential.user, hasProfile: true };
      }
      return null; // If userCredential or userCredential.user is null
    } catch (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithEmailPassword = async (email: string, password: string) => {
    try {
      setLoading(true);
      const userCredential = await firebaseSignInWithEmailPassword(email, password);
      if (userCredential?.user) {
        identifyFeaturebaseUser(userCredential.user);
        await handleUserAuthentication(userCredential.user); // Ensure profile is loaded/cached
        setUser(userCredential.user); // Set user state after successful sign-in
        const userProfile = useUserStore.getState().userProfile;

        if (!userProfile || !userProfile.username) {
          navigate('/profile-setup', { replace: true });
          return { user: userCredential.user, hasProfile: false };
        } else {
          navigate('/ai', { replace: true });
          return { user: userCredential.user, hasProfile: true };
        }
      }
      throw new Error('Failed to sign in');
    } catch (error) {
      console.error('Error signing in with email/password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmailPassword = async (email: string, password: string) => {
    try {
      setLoading(true);
      const userCredential = await firebaseSignUpWithEmailPassword(email, password);
      if (userCredential?.user) {
        identifyFeaturebaseUser(userCredential.user);
        // Create user profile in Firestore after sign up
        await updateUserProfileInFirestore(userCredential.user, { username: userCredential.user.email?.split('@')[0] || `user_${userCredential.user.uid.substring(0,5)}` });
        await handleUserAuthentication(userCredential.user); // Load the newly created profile into store
        setUser(userCredential.user); // Set user state after successful sign-up

        navigate('/profile-setup', { replace: true });
        return { user: userCredential.user, hasProfile: false }; // hasProfile is false initially, setup completes it
      }
      throw new Error('Failed to sign up');
    } catch (error) {
      console.error('Error signing up with email/password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut();
      setUser(null); // Clear user state on sign out
      navigate('/', { replace: true });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signInWithGoogle,
    signInWithEmailPassword,
    signUpWithEmailPassword,
    signOut
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
