import { createContext, useContext, useEffect, useState } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import {
  signInWithGoogleSupabase,
  signOutSupabase,
  onAuthStateChangeSupabase,
  signUpWithEmailPasswordSupabase,
  signInWithEmailPasswordSupabase,
  SupabaseUserProfile
} from '../utils/supabaseAuth';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  handleSupabaseUserAuthentication, 
  updateSupabaseUserProfile, 
  getSupabaseUserProfile,
  checkSupabaseUserProfile 
} from '../utils/supabaseUserDataManager';
import { useUserStore } from '../stores/userStore';

interface SupabaseAuthContextType {
  user: SupabaseUser | null;
  userProfile: SupabaseUserProfile | null;
  loading: boolean;
  signInWithGoogle: () => Promise<{ user: SupabaseUser | null; hasProfile: boolean }>;
  signInWithEmailPassword: (email: string, password: string) => Promise<{ user: SupabaseUser | null; hasProfile: boolean }>;
  signUpWithEmailPassword: (email: string, password: string) => Promise<{ user: SupabaseUser | null; hasProfile: boolean }>;
  signOut: () => Promise<void>;
  linkToExistingData: (username: string) => Promise<void>;
}

const SupabaseAuthContext = createContext<SupabaseAuthContextType | undefined>(undefined);

// Function to identify user in Featurebase
const identifyFeaturebaseUser = (user: SupabaseUser, profile?: SupabaseUserProfile) => {
  if (typeof window.Featurebase === 'function') {
    window.Featurebase(
      "identify",
      {
        organization: "isotopeai",
        email: user.email || undefined,
        name: profile?.displayName || user.user_metadata?.full_name || undefined,
        userId: user.id,
        profilePicture: profile?.photoURL || user.user_metadata?.avatar_url || undefined,
      },
      (err) => {
        if (err) {
          console.error("Featurebase identification error:", err);
        }
      }
    );
  }
};

export function SupabaseAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [userProfile, setUserProfile] = useState<SupabaseUserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { setUser: setStoreUser, setUserProfile: setStoreUserProfile, setAuthProvider } = useUserStore();

  // Get return URL from query params
  const searchParams = new URLSearchParams(location.search);
  const returnUrl = searchParams.get('returnUrl');

  useEffect(() => {
    console.log('Setting up Supabase auth state listener');
    setAuthProvider('supabase');

    const { data: { subscription } } = onAuthStateChangeSupabase(async (supabaseUser) => {
      console.log('Supabase auth state changed:', supabaseUser?.id);
      setLoading(true);

      if (supabaseUser) {
        setUser(supabaseUser);
        setStoreUser(supabaseUser);

        // Get or create user profile
        const profile = await handleSupabaseUserAuthentication(supabaseUser);
        setUserProfile(profile);
        
        // Convert to Firebase-compatible format for store
        if (profile) {
          const firebaseCompatibleProfile = {
            uid: profile.id,
            email: profile.email,
            displayName: profile.displayName,
            photoURL: profile.photoURL,
            username: profile.username,
            createdAt: profile.created_at,
            updatedAt: profile.updated_at,
            ...profile
          };
          setStoreUserProfile(firebaseCompatibleProfile);
        }

        identifyFeaturebaseUser(supabaseUser, profile || undefined);

        // Handle navigation based on profile completeness
        if (!profile?.username) {
          console.log('User needs to complete profile setup');
          navigate('/profile-setup', { replace: true });
        } else {
          // Check for pending invite codes or return URL
          const pendingInviteCode = localStorage.getItem('pendingInviteCode');
          
          if (pendingInviteCode) {
            localStorage.removeItem('pendingInviteCode');
            navigate(`/groups/join/${pendingInviteCode}`, { replace: true });
          } else if (returnUrl) {
            navigate(returnUrl, { replace: true });
          } else {
            navigate('/ai', { replace: true });
          }
        }
      } else {
        setUser(null);
        setUserProfile(null);
        setStoreUser(null);
        setStoreUserProfile(null);
      }

      setLoading(false);
    });

    return () => {
      console.log('Cleaning up Supabase auth state listener');
      subscription.unsubscribe();
    };
  }, [navigate, location.search, setStoreUser, setStoreUserProfile, setAuthProvider]);

  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      const result = await signInWithGoogleSupabase();
      
      // Note: With OAuth, the user will be redirected and auth state will be handled by the listener
      // Return placeholder values as the actual user will be set by the auth state change
      return { user: null, hasProfile: false };
    } catch (error) {
      console.error('Error signing in with Google:', error);
      setLoading(false);
      throw error;
    }
  };

  const signInWithEmailPassword = async (email: string, password: string) => {
    try {
      setLoading(true);
      const userCredential = await signInWithEmailPasswordSupabase(email, password);
      
      if (userCredential?.user) {
        const profile = await handleSupabaseUserAuthentication(userCredential.user);
        identifyFeaturebaseUser(userCredential.user, profile || undefined);
        
        setUser(userCredential.user);
        setUserProfile(profile);
        setStoreUser(userCredential.user);
        
        if (profile) {
          const firebaseCompatibleProfile = {
            uid: profile.id,
            email: profile.email,
            displayName: profile.displayName,
            photoURL: profile.photoURL,
            username: profile.username,
            createdAt: profile.created_at,
            updatedAt: profile.updated_at,
            ...profile
          };
          setStoreUserProfile(firebaseCompatibleProfile);
        }

        const hasProfile = Boolean(profile?.username);
        
        if (!hasProfile) {
          navigate('/profile-setup', { replace: true });
        } else if (returnUrl) {
          navigate(returnUrl, { replace: true });
        } else {
          navigate('/ai', { replace: true });
        }

        return { user: userCredential.user, hasProfile };
      }
      
      throw new Error('Failed to sign in');
    } catch (error) {
      console.error('Error signing in with email/password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmailPassword = async (email: string, password: string) => {
    try {
      setLoading(true);
      const userCredential = await signUpWithEmailPasswordSupabase(email, password);
      
      if (userCredential?.user) {
        identifyFeaturebaseUser(userCredential.user);
        
        // Create user profile after sign up
        const profile = await updateSupabaseUserProfile(userCredential.user, { 
          username: userCredential.user.email?.split('@')[0] || `user_${userCredential.user.id.substring(0,5)}` 
        });
        
        setUser(userCredential.user);
        setUserProfile(profile);
        setStoreUser(userCredential.user);
        
        if (profile) {
          const firebaseCompatibleProfile = {
            uid: profile.id,
            email: profile.email,
            displayName: profile.displayName,
            photoURL: profile.photoURL,
            username: profile.username,
            createdAt: profile.created_at,
            updatedAt: profile.updated_at,
            ...profile
          };
          setStoreUserProfile(firebaseCompatibleProfile);
        }

        navigate('/profile-setup', { replace: true });
        return { user: userCredential.user, hasProfile: false };
      }
      
      throw new Error('Failed to sign up');
    } catch (error) {
      console.error('Error signing up with email/password:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await signOutSupabase();
      setUser(null);
      setUserProfile(null);
      setStoreUser(null);
      setStoreUserProfile(null);
      navigate('/', { replace: true });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const linkToExistingData = async (username: string) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    try {
      setLoading(true);
      const profile = await handleSupabaseUserAuthentication(user, username);
      setUserProfile(profile);
      
      if (profile) {
        const firebaseCompatibleProfile = {
          uid: profile.id,
          email: profile.email,
          displayName: profile.displayName,
          photoURL: profile.photoURL,
          username: profile.username,
          createdAt: profile.created_at,
          updatedAt: profile.updated_at,
          ...profile
        };
        setStoreUserProfile(firebaseCompatibleProfile);
      }
      
      console.log(`Successfully linked user to existing data for username: ${username}`);
    } catch (error) {
      console.error('Error linking to existing data:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    userProfile,
    loading,
    signInWithGoogle,
    signInWithEmailPassword,
    signUpWithEmailPassword,
    signOut,
    linkToExistingData
  };

  return (
    <SupabaseAuthContext.Provider value={value}>
      {!loading && children}
    </SupabaseAuthContext.Provider>
  );
}

export function useSupabaseAuth() {
  const context = useContext(SupabaseAuthContext);
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return context;
}
