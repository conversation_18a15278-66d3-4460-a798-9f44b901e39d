import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { checkUsernameAvailabilitySupabase, getUserProfileByUsername } from '../utils/supabaseAuth';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Loader2, Database, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';

export default function DataMigration() {
  const [username, setUsername] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [existingDataFound, setExistingDataFound] = useState<boolean | null>(null);
  const { user, linkToExistingData } = useSupabaseAuth();
  const navigate = useNavigate();

  const checkForExistingData = async () => {
    if (!username.trim()) {
      setError('Please enter your username');
      return;
    }

    setLoading(true);
    setError('');
    setExistingDataFound(null);

    try {
      // Check if username exists in Supabase (migrated data)
      const existingProfile = await getUserProfileByUsername(username.trim());
      
      if (existingProfile) {
        setExistingDataFound(true);
      } else {
        // Check if username is available (meaning no data exists)
        const isAvailable = await checkUsernameAvailabilitySupabase(username.trim());
        setExistingDataFound(!isAvailable);
      }
    } catch (err) {
      console.error('Error checking for existing data:', err);
      setError('Failed to check for existing data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLinkData = async () => {
    if (!username.trim()) {
      setError('Please enter your username');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await linkToExistingData(username.trim());
      setSuccess(true);
      
      // Navigate to dashboard after successful linking
      setTimeout(() => {
        navigate('/ai', { replace: true });
      }, 2000);
    } catch (err) {
      console.error('Error linking data:', err);
      setError('Failed to link your data. Please try again or contact support.');
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    navigate('/profile-setup', { replace: true });
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access data migration.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/')} className="w-full">
              Go to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-green-700">Data Linked Successfully!</CardTitle>
            <CardDescription>
              Your existing data has been linked to your new account. Redirecting to dashboard...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <Database className="w-12 h-12 text-blue-600 mx-auto mb-4" />
          <CardTitle className="text-2xl">Link Your Existing Data</CardTitle>
          <CardDescription>
            If you had an account with IsotopeAI before, enter your username to link your existing data to this new account.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Your Previous Username
              </label>
              <Input
                id="username"
                type="text"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
                className="w-full"
              />
            </div>

            {existingDataFound !== null && (
              <Alert variant={existingDataFound ? "default" : "destructive"}>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {existingDataFound 
                    ? "✅ Existing data found for this username! You can link it to your account."
                    : "❌ No existing data found for this username. You can create a new profile or try a different username."
                  }
                </AlertDescription>
              </Alert>
            )}

            <div className="flex gap-3">
              <Button
                onClick={checkForExistingData}
                disabled={loading || !username.trim()}
                variant="outline"
                className="flex-1"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : null}
                Check for Data
              </Button>

              {existingDataFound && (
                <Button
                  onClick={handleLinkData}
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <ArrowRight className="w-4 h-4 mr-2" />
                  )}
                  Link Data
                </Button>
              )}
            </div>
          </div>

          <div className="border-t pt-6">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Don't have existing data or want to start fresh?
              </p>
              <Button
                onClick={handleSkip}
                variant="ghost"
                className="text-blue-600 hover:text-blue-700"
              >
                Create New Profile Instead
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              What happens when you link data?
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Your AI chats and conversations will be restored</li>
              <li>• Study sessions and analytics will be preserved</li>
              <li>• Group memberships and todos will be maintained</li>
              <li>• All your subjects and exam data will be available</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
