import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '../integrations/supabase/client';
import { handleSupabaseUserAuthentication } from '../utils/supabaseUserDataManager';
import { useUserStore } from '../stores/userStore';

export default function AuthCallback() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setUser, setUserProfile, setAuthProvider } = useUserStore();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('Processing auth callback...');
        setAuthProvider('supabase');

        // Get the session from the URL
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          setError(error.message);
          return;
        }

        if (data.session?.user) {
          console.log('User authenticated successfully:', data.session.user.id);
          
          // Handle user authentication and profile setup
          const profile = await handleSupabaseUserAuthentication(data.session.user);
          
          // Update store
          setUser(data.session.user);
          if (profile) {
            const firebaseCompatibleProfile = {
              uid: profile.id,
              email: profile.email,
              displayName: profile.displayName,
              photoURL: profile.photoURL,
              username: profile.username,
              createdAt: profile.created_at,
              updatedAt: profile.updated_at,
              ...profile
            };
            setUserProfile(firebaseCompatibleProfile);
          }

          // Identify user in Featurebase
          if (typeof window.Featurebase === 'function') {
            window.Featurebase(
              "identify",
              {
                organization: "isotopeai",
                email: data.session.user.email || undefined,
                name: profile?.displayName || data.session.user.user_metadata?.full_name || undefined,
                userId: data.session.user.id,
                profilePicture: profile?.photoURL || data.session.user.user_metadata?.avatar_url || undefined,
              },
              (err) => {
                if (err) {
                  console.error("Featurebase identification error:", err);
                }
              }
            );
          }

          // Determine where to navigate
          const returnUrl = searchParams.get('returnUrl');
          const pendingInviteCode = localStorage.getItem('pendingInviteCode');

          if (!profile?.username) {
            // User needs to complete profile setup
            navigate('/profile-setup', { replace: true });
          } else if (pendingInviteCode) {
            // Handle pending group invitation
            localStorage.removeItem('pendingInviteCode');
            navigate(`/groups/join/${pendingInviteCode}`, { replace: true });
          } else if (returnUrl) {
            // Navigate to the originally requested URL
            navigate(returnUrl, { replace: true });
          } else {
            // Default navigation
            navigate('/ai', { replace: true });
          }
        } else {
          console.log('No user session found, redirecting to home');
          navigate('/', { replace: true });
        }
      } catch (err) {
        console.error('Error in auth callback:', err);
        setError(err instanceof Error ? err.message : 'Authentication failed');
      } finally {
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [navigate, searchParams, setUser, setUserProfile, setAuthProvider]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Completing sign in...
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Please wait while we set up your account.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Authentication Error
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={() => navigate('/', { replace: true })}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  return null;
}
