import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { checkUsernameAvailability, updateUserProfile } from '../utils/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../utils/firebase';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';

function ElegantShape({
  className,
  delay = 0,
  width = 400,
  height = 100,
  rotate = 0,
  gradient = "from-white/[0.08]",
}: {
  className?: string;
  delay?: number;
  width?: number;
  height?: number;
  rotate?: number;
  gradient?: string;
}) {
  return (
    <motion.div
      initial={{
        opacity: 0,
        y: -150,
        rotate: rotate - 15,
      }}
      animate={{
        opacity: 1,
        y: 0,
        rotate: rotate,
      }}
      transition={{
        duration: 2.4,
        delay,
        ease: [0.23, 0.86, 0.39, 0.96],
        opacity: { duration: 1.2 },
      }}
      className={className}
    >
      <motion.div
        animate={{
          y: [0, 15, 0],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
        style={{
          width,
          height,
        }}
        className="relative"
      >
        <div
          className={`absolute inset-0 rounded-full bg-gradient-to-r to-transparent ${gradient} backdrop-blur-[2px] border-2 border-white/[0.15] shadow-[0_8px_32px_0_rgba(255,255,255,0.1)] after:absolute after:inset-0 after:rounded-full after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]`}
        />
      </motion.div>
    </motion.div>
  );
}

export default function ProfileSetupPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [username, setUsername] = useState('');
  const [photoURL, setPhotoURL] = useState(user?.photoURL || '');
  const [isLoading, setIsLoading] = useState(false);
  const [usernameError, setUsernameError] = useState('');

  useEffect(() => {
    // Redirect to home if no user
    if (!user) {
      navigate('/');
      return;
    }

    // Check if user already has a profile
    const checkExistingProfile = async () => {
      try {
        const userRef = doc(db, 'users', user.uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists() && userSnap.data()?.username) {
          navigate('/ai');
        }
      } catch (error) {
        console.error('Error checking profile:', error);
      }
    };

    checkExistingProfile();
  }, [user, navigate]);

  useEffect(() => {
    if (user?.photoURL) {
      setPhotoURL(user.photoURL);
    }
  }, [user]);

  const handleUsernameChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    setUsername(value);
    setUsernameError('');

    if (value.length >= 3) {
      try {
        const isAvailable = await checkUsernameAvailability(value);
        if (!isAvailable) {
          setUsernameError('This username is already taken');
        }
      } catch (error) {
        console.error('Error checking username availability:', error);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading || !user) return;

    if (username.length < 3) {
      setUsernameError('Username must be at least 3 characters long');
      return;
    }

    setIsLoading(true);
    try {
      // Check username availability one last time before submitting
      const isAvailable = await checkUsernameAvailability(username);
      if (!isAvailable) {
        setUsernameError('This username is already taken');
        setIsLoading(false);
        return;
      }

      // Create the user profile
      await updateUserProfile(user.uid, {
        username,
        photoURL,
        displayName: user.displayName || username,
      });

      // Verify the profile was created successfully
      const userRef = doc(db, 'users', user.uid);
      const userSnap = await getDoc(userRef);
      const userData = userSnap.data();

      if (!userSnap.exists() || !userData?.username) {
        throw new Error('Failed to create user profile');
      }

      toast({
        title: "Profile Created",
        description: "Your profile has been successfully set up!",
      });

      // Check if there's a pending invite code
      const pendingInviteCode = localStorage.getItem('pendingInviteCode');
      if (pendingInviteCode) {
        // If there's a pending invite, redirect to join handler
        navigate(`/join?code=${pendingInviteCode}`);
      } else {
        // Otherwise, redirect to AI page
        navigate('/ai');
      }
    } catch (error: any) {
      console.error('Error creating profile:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) return null;

  return (
    <div className="relative min-h-screen bg-[#030303] overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/[0.05] via-transparent to-rose-500/[0.05] blur-3xl" />

      {/* Animated Shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <ElegantShape
          delay={0.3}
          width={600}
          height={140}
          rotate={12}
          gradient="from-indigo-500/[0.15]"
          className="absolute left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]"
        />
        <ElegantShape
          delay={0.5}
          width={500}
          height={120}
          rotate={-15}
          gradient="from-rose-500/[0.15]"
          className="absolute right-[-5%] md:right-[0%] top-[70%] md:top-[75%]"
        />
        <ElegantShape
          delay={0.4}
          width={300}
          height={80}
          rotate={-8}
          gradient="from-violet-500/[0.15]"
          className="absolute left-[5%] md:left-[10%] bottom-[5%] md:bottom-[10%]"
        />
      </div>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex flex-col md:flex-row">
        {/* Left Section - Welcome Message */}
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="md:w-1/2 p-8 md:p-16 flex flex-col justify-center"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-white/70">
            Welcome to IsotopeAI
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-8">
            You're just one step away from joining our community of learners.
          </p>
          <div className="space-y-6 text-lg text-white/60">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center mt-1">✨</div>
              <p>Get instant help with your academic doubts in Physics, Chemistry, and Mathematics</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center mt-1">🤝</div>
              <p>Connect with fellow students and share knowledge</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center mt-1">🎯</div>
              <p>Track your learning progress and improve continuously</p>
            </div>
          </div>
        </motion.div>

        {/* Right Section - Profile Setup Form */}
        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="md:w-1/2 p-8 md:p-16 flex items-center justify-center"
        >
          <div className="w-full max-w-md space-y-8">
            <motion.div 
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="relative p-8 rounded-2xl bg-white/[0.03] border border-white/[0.08] backdrop-blur-sm"
            >
              <div className="text-center space-y-2 mb-8">
                <h2 className="text-2xl font-bold text-white/90">Complete Your Profile</h2>
                <p className="text-white/60">
                  Choose a unique username to get started
                </p>
                <p className="text-white/40 text-sm">
                  Your verified email helps maintain community trust and enables important account features.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-8">
                <motion.div 
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                  className="flex flex-col items-center space-y-4"
                >
                  <div className="relative">
                    <Avatar className="h-32 w-32 ring-4 ring-white/[0.08] shadow-xl">
                      <AvatarImage src={photoURL} alt={username} />
                      <AvatarFallback className="bg-gradient-to-br from-indigo-500/30 to-purple-500/30 text-2xl text-white/90">
                        {username ? username.slice(0, 2).toUpperCase() : user.displayName?.slice(0, 2).toUpperCase() || '👤'}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="text-sm text-white/40">
                    Using your Google profile photo
                  </div>
                </motion.div>

                <motion.div 
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.8 }}
                  className="space-y-4"
                >
                  <div className="space-y-2">
                    <Label htmlFor="username" className="text-white/80">Username</Label>
                    <Input
                      id="username"
                      value={username}
                      onChange={handleUsernameChange}
                      placeholder="Choose a unique username"
                      autoFocus
                      minLength={3}
                      className={`h-12 text-lg bg-white/[0.03] border-white/[0.08] text-white placeholder:text-white/40 ${
                        usernameError ? "border-red-500/50" : "focus:border-white/20"
                      }`}
                    />
                    {usernameError && (
                      <p className="text-sm text-red-400/90 flex items-center gap-2">
                        <span>⚠️</span> {usernameError}
                      </p>
                    )}
                    <p className="text-sm text-white/40">
                      This will be your public username in chats and discussions
                    </p>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full h-12 text-lg font-medium bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white border-0 transition-all duration-300"
                    disabled={isLoading || !!usernameError || username.length < 3}
                  >
                    {isLoading ? (
                      <span className="flex items-center gap-2">
                        <Loader2 className="h-5 w-5 animate-spin" />
                        Creating Profile...
                      </span>
                    ) : (
                      "Complete Setup"
                    )}
                  </Button>
                </motion.div>
              </form>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 