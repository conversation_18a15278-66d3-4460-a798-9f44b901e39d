import { useEffect, useState, useRef } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore" // Import updateDoc
import { db, getUserDailyTargetHours, updateUserDailyTargetHours, getUserDailyMotivation, updateUserDailyMotivation } from "@/utils/firebase"
import { getUserProfile } from '@/utils/userDataManager'; // Added import
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useDocumentTitle } from "@/hooks/useDocumentTitle"

import { useLocalStorage } from "@/hooks/useLocalStorage"

import { createTheme } from '@mui/material'
import { useTheme } from "next-themes"; // Import useTheme
import "@/styles/chart-dark-mode.css" // Import chart dark mode styles

import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning'
import { FeedbackWidget } from '@/components/FeedbackWidget'
import Header from "@/components/shared/Header"
import { DailyStat } from '@/components/analytics/StudyCalendar'; // Import DailyStat type only
import DayDetailModal from '@/components/analytics/DayDetailModal'; // Import the modal
import { toast } from "@/components/ui/use-toast";

import OverviewTab from "@/components/analytics/OverviewTab"
import DailyTab from "@/components/analytics/DailyTab"
import WeeklyTab from "@/components/analytics/WeeklyTab"
import MonthlyTab from "@/components/analytics/MonthlyTab"
import SubjectsTab from "@/components/analytics/SubjectsTab"
import TaskTypeTab from "@/components/analytics/TaskTypeTab"
// Removed problematic imports
// import FocusTab from "@/components/analytics/FocusTab"
// import { LoadingIndicator } from "@/components/shared/LoadingIndicator"
// import { CalendarDateRange } from "@/components/productivity/CalendarDateRange"

interface StudySession {
  subject: string
  duration: number
  mode: "pomodoro" | "stopwatch"
  phase: "work" | "shortBreak" | "longBreak"
  completed: boolean
  date: string
  weekNumber: number
  month: string
  year: number
  startTime?: Date
  endTime?: Date
  taskName?: string
  taskType: string
  taskDescription?: string
  focusRating?: "focused" | "neutral" | "distracted"
  notes?: string
  subjectColor?: string
  productivityRating?: number
}

interface Analytics {
  dailyStats: {
    date: string
    totalDuration: number
    subjectDurations: { [key: string]: number }
    completedPomodoros: number
    taskTypeDurations?: { [key: string]: number }
  }[]
  weeklyStats: {
    weekNumber: number
    year: number // Add the year property to fix the linter error
    totalDuration: number
    subjectDurations: { [key: string]: number }
    completedPomodoros: number
    taskTypeDurations?: { [key: string]: number }
  }[]
  monthlyStats: {
    month: string
    year: number
    monthKey: string // Keep the stored key for sorting
    totalDuration: number
    subjectDurations: { [key: string]: number }
    completedPomodoros: number
    taskTypeDurations?: { [key: string]: number }
  }[]
  subjectStats: {
    subject: string
    totalDuration: number
    completedPomodoros: number
    averageSessionDuration: number
  }[]
  taskTypeStats?: {
    taskType: string
    totalDuration: number
    sessionCount: number
    averageSessionDuration: number
    averageProductivityRating?: number
  }[]
}

// Interface definitions for the Analytics page

interface FocusAnalytics {
  totalFocusTime: number
  avgFocusPerDay: number
  categoryDistribution: {
    subject: string
    timeSpent: number
    ratio: number
    change: number
    color: string
  }[]
  dailyFocusTime: {
    [date: string]: number
  }
  dailyFocusPercentChange: {
    [date: string]: number
  }
  avgFocusPercentChange: number
}

const COLORS = ['#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#f97316', '#10b981', '#06b6d4', '#3b82f6']

// Dark theme for MUI charts
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#8b5cf6', // Purple
    },
    secondary: {
      main: '#ec4899', // Pink
    },
    background: {
      paper: 'rgba(30, 30, 60, 0.2)',
    },
    text: {
        primary: '#ffffff',
        secondary: 'rgba(255, 255, 255, 0.7)',
    }
  }
})

// Light theme for MUI charts
const lightTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#6366f1', // Indigo-500
    },
    secondary: {
      main: '#ec4899', // Pink-500
    },
    background: {
      paper: 'rgba(255, 255, 255, 0.8)', // Lighter background for charts
    },
    text: {
      primary: '#1f2937', // Gray-800
      secondary: '#6b7280', // Gray-500
    }
  }
})

// Helper function to format Date object to YYYY-MM-DD in local time
const formatDateToLocalYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Add this function to format date in a nice way
// Keeping this function for potential future use
/*
const formatDateForDisplay = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'short',
    day: 'numeric'
  });
};
*/

// Import DailyOverview and DaySelector from their respective files

// Define an interface for Firestore Timestamp
interface FirestoreTimestamp {
  seconds: number;
  nanoseconds: number;
}

// Interface for SessionDetail, used for editing sessions
interface SessionDetail {
  id: string;
  subject: string;
  taskType: string;
  taskDescription: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  subjectColor?: string;
}

export default function Analytics() {
  useDocumentTitle("Analytics - IsotopeAI");
  const { user } = useAuth()
  const { theme: nextTheme } = useTheme(); // Get current theme
  const theme: 'light' | 'dark' = (nextTheme === 'dark' ? 'dark' : 'light');
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [deletedSubjects, setDeletedSubjects] = useState<string[]>([]);
  const [focusAnalytics, setFocusAnalytics] = useState<FocusAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  // const { backgroundTheme, getBackgroundStyle } = useBackgroundTheme() // Removed background theme logic
  const [selectedDate, setSelectedDate] = useState<string>(formatDateToLocalYYYYMMDD(new Date()))
  // const [isCapturing, setIsCapturing] = useState(false) // isCapturing seems unused
  // Track active tab for potential future use
  const [selectedTab, setSelectedTab] = useState("overview") // Use regular useState instead of useLocalStorage
  const contentRef = useRef<HTMLDivElement>(null)
  const [targetHours, setTargetHours] = useState<number>(6) // Default to 6 hours, will be updated from Firebase
  const [dailyMotivation, setDailyMotivation] = useState<{quote: string, author: string}>({
    quote: "The expert in anything was once a beginner. The secret of getting ahead is getting started.",
    author: "Mark Twain"
  }) // Default motivation, will be updated from Firebase

  // State for Subject Colors
  const [subjectColorMap, setSubjectColorMap] = useState<{ [subject: string]: string }>({});

  // State for Streak Data
  const [streakInfo, setStreakInfo] = useState<{
    currentStreak: number;
    longestStreak: number;
    streakMap: { [date: string]: boolean };
  }>({ currentStreak: 0, longestStreak: 0, streakMap: {} });

  // State for the Day Detail Modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedModalDate, setSelectedModalDate] = useState<Date | null>(null);
  const [selectedModalData, setSelectedModalData] = useState<DailyStat | undefined>(undefined);

  // State for daily sessions
  const [dailySessions, setDailySessions] = useState<{
    date: string;
    sessions: {
      id: string;
      subject: string;
      taskType: string;
      taskDescription: string;
      startTime: Date;
      endTime: Date;
      duration: number;
      subjectColor?: string;
    }[];
  }[]>([]);

  // State for subjects list for dropdowns
  const [availableSubjects, setAvailableSubjects] = useState<string[]>([]);
  const [availableTaskTypes, setAvailableTaskTypes] = useState<string[]>([
    "Study", "Homework", "Revision", "Test", "Practice", "Project", "Research", "Other"
  ]);
  // State to track if we're currently making changes
  const [isOperationPending, setIsOperationPending] = useState(false);

  // Add state to store mapping between display IDs and Firestore IDs
  const [sessionIdMapping, setSessionIdMapping] = useState<{[displayId: string]: string}>({});

  // Handler for clicking a day on the calendar
  const handleDayClick = (date: Date, studyData: DailyStat | undefined) => {
    setSelectedModalDate(date);
    setSelectedModalData(studyData);

    // Get sessions for this date in YYYY-MM-DD format
    const dateStr = formatDateToLocalYYYYMMDD(date);
    const daySessionsData = dailySessions.find(day => day.date === dateStr)?.sessions || [];

    setIsModalOpen(true);
  };

  // Determine which MUI theme to use
  const muiTheme = theme === 'dark' ? darkTheme : lightTheme;

  // Add a new state for tracking subject deletion loading state
  const [deletingSubject, setDeletingSubject] = useState<string | null>(null)

  // Add effect to fetch target hours and daily motivation from Firebase
  useEffect(() => {
    const fetchUserPreferences = async () => {
      if (!user) return;
      try {
        // Fetch target hours
        const hours = await getUserDailyTargetHours(user.uid);
        setTargetHours(hours);

        // Fetch daily motivation
        const motivation = await getUserDailyMotivation(user.uid);
        setDailyMotivation(motivation);
      } catch (error) {
        console.error('Error fetching user preferences:', error);
      }
    };

    fetchUserPreferences();
  }, [user]);

  // Add function to update target hours in Firebase
  const updateTargetHours = async (hours: number) => {
    if (!user) return;
    try {
      await updateUserDailyTargetHours(user.uid, hours);
      setTargetHours(hours);
    } catch (error) {
      console.error('Error updating target hours:', error);
    }
  };

  // Add function to update daily motivation in Firebase
  const updateDailyMotivation = async (motivation: {quote: string, author: string}) => {
    if (!user) return;
    try {
      await updateUserDailyMotivation(user.uid, motivation);
      setDailyMotivation(motivation);
    } catch (error) {
      console.error('Error updating daily motivation:', error);
    }
  };

  // Add useEffect to refetch data when isLoading changes
  useEffect(() => {
    if (isLoading && user) {
      const fetchAnalytics = async () => {
        try {
          // Fetch study sessions data using the new userDataManager
          const userProfile = await getUserProfile(user.uid);

          if (!userProfile) {
            console.log("User document does not exist.");
            setAnalytics({ dailyStats: [], weeklyStats: [], monthlyStats: [], subjectStats: [], taskTypeStats: [] });
            setFocusAnalytics(generateFocusAnalytics({ dailyStats: [], weeklyStats: [], monthlyStats: [], subjectStats: [], taskTypeStats: [] }));
            setSelectedDate(formatDateToLocalYYYYMMDD(new Date()));
            setSubjectColorMap({});
            return;
          }

          const studySessions = userProfile.studySessions || {};
          const sessions = Object.values(studySessions) as StudySession[];

          // Process sessions into analytics
          const processedAnalytics = processAnalytics(sessions);
          setAnalytics(processedAnalytics);

          // Generate focus analytics
          const generatedFocusAnalytics = generateFocusAnalytics(processedAnalytics);
          setFocusAnalytics(generatedFocusAnalytics);

          // Load subject colors
          const colorMap: { [key: string]: string } = {};

          // Try to extract colors from study sessions
          if (studySessions) {
            Object.values(studySessions).forEach((session: any) => {
              if (session.subject && session.subjectColor) {
                colorMap[session.subject] = session.subjectColor;
              }
            });
          }

          // For any subjects without colors, assign from our color palette
          processedAnalytics.subjectStats.forEach((subjectStat, index) => {
            if (!colorMap[subjectStat.subject]) {
              colorMap[subjectStat.subject] = COLORS[index % COLORS.length];
            }
          });

          setSubjectColorMap(colorMap);
        } catch (error) {
          console.error("Error loading study sessions:", error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchAnalytics();
    }
  }, [isLoading, user]);

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!user) return

      try {
        const userProfile = await getUserProfile(user.uid);
        if (!userProfile) {
            console.log("User document does not exist.");
            setAnalytics({ dailyStats: [], weeklyStats: [], monthlyStats: [], subjectStats: [], taskTypeStats: [] }); // Set empty state
            setFocusAnalytics(generateFocusAnalytics({ dailyStats: [], weeklyStats: [], monthlyStats: [], subjectStats: [], taskTypeStats: [] })); // Set empty focus state
            setStreakInfo({ currentStreak: 0, longestStreak: 0, streakMap: {} });
            setSubjectColorMap({});
            setSelectedDate(formatDateToLocalYYYYMMDD(new Date())); // Reset selected date
            setDailySessions([]); // Initialize with empty sessions
            return;
        }

        const studySessionsData = userProfile.studySessions || {};
        console.log(`[useEffect fetchAnalytics] Fetched studySessions on load for user ${user.uid}. Session count: ${Object.keys(studySessionsData).length}`);
        // console.log('[useEffect fetchAnalytics] Fetched studySessions data:', studySessionsData); // Optional: Uncomment for detailed data view
        const sessions = Object.values(studySessionsData) as StudySession[];

        // Extract unique subject and task type lists
        const subjects = [...new Set(sessions.map(session => session.subject))].filter(Boolean);
        setAvailableSubjects(subjects);

        const taskTypes = [...new Set(sessions.map(session => session.taskType))].filter(Boolean);
        // Combine with default task types while avoiding duplicates
        const allTaskTypes = [...new Set([...availableTaskTypes, ...taskTypes])];
        setAvailableTaskTypes(allTaskTypes);

        // Process sessions into analytics
        const processedAnalytics = processAnalytics(sessions)
        setAnalytics(processedAnalytics)

        // Set initial selected date only if data exists
        if (processedAnalytics.dailyStats.length > 0) {
            setSelectedDate(processedAnalytics.dailyStats[processedAnalytics.dailyStats.length - 1].date);
        } else {
            setSelectedDate(formatDateToLocalYYYYMMDD(new Date())); // Default to today if no data
        }

        // Generate focus analytics
        const generatedFocusAnalytics = generateFocusAnalytics(processedAnalytics);
        setFocusAnalytics(generatedFocusAnalytics);

        // Calculate streak data and subject colors
        setStreakInfo(calculateStreakInfo(processedAnalytics.dailyStats));

        // Create a color map for subjects
        const colorMap: { [subject: string]: string } = {};

        // First, try to get colors from the study sessions if they have subjectColor
        if (studySessionsData) { // Use studySessionsData here
          Object.values(studySessionsData).forEach((session: any) => {
            if (session.subject && session.subjectColor) {
              colorMap[session.subject] = session.subjectColor;
            }
          });
        }

        // For any subjects without colors, assign from our color palette
        processedAnalytics.subjectStats.forEach((subjectStat, index) => {
          if (!colorMap[subjectStat.subject]) {
            colorMap[subjectStat.subject] = COLORS[index % COLORS.length];
          }
        });

        setSubjectColorMap(colorMap);

        // Process daily sessions for timeline
        // Use the full studySessionsData object to preserve Firestore IDs
        console.log("Processing daily sessions with Firestore IDs preserved");
        const processedDailySessions = processDailySessions(studySessionsData, colorMap);
        setDailySessions(processedDailySessions);

      } catch (error) {
        console.error("Error fetching analytics:", error)
        // Optionally set an error state here
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalytics()
  }, [user])

  // Utility function to calculate streak data, current streak, and longest streak
  const calculateStreakInfo = (dailyStats: DailyStat[]): {
    currentStreak: number;
    longestStreak: number;
    streakMap: { [date: string]: boolean }; // Map for ONLY the current streak days
  } => {
    const streakMap: { [date: string]: boolean } = {}; // Initialize empty map for current streak days
    let longestStreak = 0;
    let tempCurrentStreak = 0;

    if (!dailyStats || dailyStats.length === 0) {
      return { currentStreak: 0, longestStreak: 0, streakMap };
    }

    // Ensure stats are sorted by date (oldest to newest)
    const sortedStats = [...dailyStats].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const statsMap = new Map(sortedStats.map(stat => [stat.date, stat])); // Use a Map for efficient lookups

    let previousDate: Date | null = null;

    // Calculate longest streak
    for (const stat of sortedStats) {
      const currentDate = new Date(stat.date);
      currentDate.setHours(0, 0, 0, 0); // Normalize date

      if (stat.totalDuration > 0) {
        // Don't populate streakMap here, just calculate longest
        if (previousDate) {
          const diffTime = currentDate.getTime() - previousDate.getTime();
          const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays === 1) {
            // Consecutive day
            tempCurrentStreak++;
          } else {
            // Break in streak
            longestStreak = Math.max(longestStreak, tempCurrentStreak);
            tempCurrentStreak = 1; // Start new streak
          }
        } else {
          // First study day in the sorted list
          tempCurrentStreak = 1;
        }
        previousDate = currentDate;
      } else {
        // Day with no study, breaks the streak
        longestStreak = Math.max(longestStreak, tempCurrentStreak);
        tempCurrentStreak = 0;
        previousDate = currentDate; // Still update previousDate to check the next day correctly
      }
    }
    longestStreak = Math.max(longestStreak, tempCurrentStreak); // Final check for longest streak

    // Calculate the *actual* current streak ending today (or the last study day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let finalCurrentStreak = 0;
    let dateToCheck = new Date(today); // Use a copy for iteration

    // Find the latest date with data, ensuring it's not in the future
    const latestDataDate = sortedStats.length > 0
      ? new Date(sortedStats[sortedStats.length - 1].date)
      : null;
    if (latestDataDate) {
        latestDataDate.setHours(0,0,0,0);
        if (latestDataDate <= today) {
            dateToCheck = latestDataDate; // Start checking from the last day with data if it's today or in the past
        } else {
             // If the latest data is in the future, start checking from today
             dateToCheck = new Date(today);
        }
    }


    // Iterate backwards to find the length of the current streak
    let tempDate = new Date(dateToCheck);
    while (true) {
      const dateString = formatDateToLocalYYYYMMDD(tempDate);
      const dayStat = statsMap.get(dateString); // Use Map for efficiency

      if (dayStat && dayStat.totalDuration > 0) {
        finalCurrentStreak++;
        tempDate.setDate(tempDate.getDate() - 1); // Move to the previous day
      } else {
        // Streak broken or no data for the day
        break;
      }
    }

    // Populate streakMap ONLY for the current streak IF it's >= 2 days
    if (finalCurrentStreak >= 2) {
      tempDate = new Date(dateToCheck); // Reset to the start date of the current streak calculation
      for (let i = 0; i < finalCurrentStreak; i++) {
        const dateString = formatDateToLocalYYYYMMDD(tempDate);
        streakMap[dateString] = true;
        tempDate.setDate(tempDate.getDate() - 1);
      }
    }

    // Return the calculated values, including the potentially empty streakMap
    return { currentStreak: finalCurrentStreak, longestStreak, streakMap };
  };


  const processAnalytics = (sessions: StudySession[]): Analytics => {
    const dailyStats: { [key: string]: any } = {}
    const weeklyStats: { [key: string]: any } = {}
    const monthlyStats: { [key: string]: any } = {}
    const subjectStats: { [key: string]: any } = {}
    const taskTypeStats: { [key: string]: any } = {}

    sessions.forEach(session => {
      // Skip invalid sessions
      if (!session.duration || session.duration < 0 || !session.date || !session.subject) return

      // Extract task type with fallback to "Study"
      const taskType = session.taskType || "Study"

      // Daily stats
      if (!dailyStats[session.date]) {
        dailyStats[session.date] = {
          date: session.date,
          totalDuration: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          completedPomodoros: 0
        }
      }
      dailyStats[session.date].totalDuration += session.duration
      dailyStats[session.date].subjectDurations[session.subject] =
        (dailyStats[session.date].subjectDurations[session.subject] || 0) + session.duration
      dailyStats[session.date].taskTypeDurations[taskType] =
        (dailyStats[session.date].taskTypeDurations[taskType] || 0) + session.duration
      if (session.completed && session.mode === "pomodoro") {
        dailyStats[session.date].completedPomodoros++
      }

      // Weekly stats
      const weekKey = `${session.year}-W${session.weekNumber}`
      if (!weeklyStats[weekKey]) {
        weeklyStats[weekKey] = {
          weekNumber: session.weekNumber,
          year: session.year,
          totalDuration: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          completedPomodoros: 0
        }
      }
      weeklyStats[weekKey].totalDuration += session.duration
      weeklyStats[weekKey].subjectDurations[session.subject] =
        (weeklyStats[weekKey].subjectDurations[session.subject] || 0) + session.duration
      weeklyStats[weekKey].taskTypeDurations[taskType] =
        (weeklyStats[weekKey].taskTypeDurations[taskType] || 0) + session.duration
      if (session.completed && session.mode === "pomodoro") {
        weeklyStats[weekKey].completedPomodoros++
      }

      // Monthly stats
      const monthKey = `${session.year}-${session.month}`;
      if (!monthlyStats[monthKey]) {
        monthlyStats[monthKey] = {
          month: session.month, // Keep original month for display if needed
          year: session.year,
          monthKey: monthKey, // Store the key for sorting
          totalDuration: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          completedPomodoros: 0
        }
      }
      monthlyStats[monthKey].totalDuration += session.duration
      monthlyStats[monthKey].subjectDurations[session.subject] =
        (monthlyStats[monthKey].subjectDurations[session.subject] || 0) + session.duration
      monthlyStats[monthKey].taskTypeDurations[taskType] =
        (monthlyStats[monthKey].taskTypeDurations[taskType] || 0) + session.duration
      if (session.completed && session.mode === "pomodoro") {
        monthlyStats[monthKey].completedPomodoros++
      }

      // Subject stats
      if (!subjectStats[session.subject]) {
        subjectStats[session.subject] = {
          subject: session.subject,
          totalDuration: 0,
          completedPomodoros: 0,
          sessionCount: 0
        }
      }
      subjectStats[session.subject].totalDuration += session.duration
      subjectStats[session.subject].sessionCount++
      if (session.completed && session.mode === "pomodoro") {
        subjectStats[session.subject].completedPomodoros++
      }

      // Task type stats
      if (!taskTypeStats[taskType]) {
        taskTypeStats[taskType] = {
          taskType: taskType,
          totalDuration: 0,
          sessionCount: 0,
          productivityRatings: []
        }
      }
      taskTypeStats[taskType].totalDuration += session.duration
      taskTypeStats[taskType].sessionCount++

      // If this session has a productivity rating, add it to the array
      if (session.productivityRating && session.productivityRating > 0) {
        taskTypeStats[taskType].productivityRatings.push(session.productivityRating)
      }
    })

    // Calculate averages for subject stats
    Object.values(subjectStats).forEach((stats: any) => {
      stats.averageSessionDuration = stats.sessionCount > 0 ? stats.totalDuration / stats.sessionCount : 0;
    })

    // Calculate averages for task type stats
    Object.values(taskTypeStats).forEach((stats: any) => {
      stats.averageSessionDuration = stats.sessionCount > 0 ? stats.totalDuration / stats.sessionCount : 0;

      // Calculate average productivity rating if there are any ratings
      if (stats.productivityRatings && stats.productivityRatings.length > 0) {
        const sum = stats.productivityRatings.reduce((a: number, b: number) => a + b, 0);
        stats.averageProductivityRating = Math.round((sum / stats.productivityRatings.length) * 10) / 10;
      }

      // Remove the array of ratings from the final object
      delete stats.productivityRatings;
    })

    return {
      dailyStats: Object.values(dailyStats).sort((a, b) => a.date.localeCompare(b.date)),
      weeklyStats: Object.values(weeklyStats).sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year
        return a.weekNumber - b.weekNumber
      }),
      monthlyStats: Object.values(monthlyStats).sort((a, b) => a.monthKey.localeCompare(b.monthKey)), // Sort by year-month key
      subjectStats: Object.values(subjectStats),
      taskTypeStats: Object.values(taskTypeStats)
    }
  }

  // Generate dummy focus analytics data from existing analytics
  const generateFocusAnalytics = (analyticsData: Analytics): FocusAnalytics => {
    // Convert existing analytics to focus analytics format
    const categoryDistribution = analyticsData.subjectStats.map((subject, index) => {
      const totalTime = analyticsData.subjectStats.reduce((acc, s) => acc + s.totalDuration, 0);
      const ratio = totalTime > 0 ? (subject.totalDuration / totalTime) * 100 : 0;

      return {
        subject: subject.subject,
        timeSpent: subject.totalDuration,
        ratio: Math.round(ratio),
        change: Math.random() > 0.5 ? Math.floor(Math.random() * 20) : -Math.floor(Math.random() * 20),
        color: COLORS[index % COLORS.length]
      };
    });

    // Create a map of focus time by date
    const dailyFocusTime: { [date: string]: number } = {};
    const dailyFocusPercentChange: { [date: string]: number } = {};

    analyticsData.dailyStats.forEach((day, index) => {
      dailyFocusTime[day.date] = day.totalDuration;

      // Calculate percent change from previous day
      if (index > 0) {
        const prevDay = analyticsData.dailyStats[index - 1];
        if (prevDay.totalDuration > 0) {
          dailyFocusPercentChange[day.date] = ((day.totalDuration - prevDay.totalDuration) / prevDay.totalDuration) * 100;
        } else {
          dailyFocusPercentChange[day.date] = day.totalDuration > 0 ? 100 : 0;
        }
      }
    });

    // Calculate total and average focus time for the current month only
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();
    const currentDay = now.getDate();

    // Filter for days in the current month only
    const currentMonthStats = analyticsData.dailyStats.filter(day => {
      const date = new Date(day.date);
      return date.getMonth() + 1 === currentMonth && date.getFullYear() === currentYear;
    });

    // Sort by date to ensure chronological order
    const sortedCurrentMonthStats = [...currentMonthStats].sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Filter out future days
    const pastAndTodayStats = sortedCurrentMonthStats.filter(day => {
      const date = new Date(day.date);
      return date.getDate() <= currentDay;
    });

    // Calculate total focus time (still include all data for historical purposes)
    const totalFocusTime = analyticsData.dailyStats.reduce((sum, day) => sum + day.totalDuration, 0);

    // Calculate average focus time for current month, excluding future days
    const avgFocusPerDay = pastAndTodayStats.length > 0
      ? pastAndTodayStats.reduce((sum, day) => sum + day.totalDuration, 0) / pastAndTodayStats.length
      : 0;

    // Calculate average focus percent change
    let avgFocusPercentChange = 0;
    const sortedDailyStats = [...analyticsData.dailyStats].sort((a, b) => a.date.localeCompare(b.date));

    if (sortedDailyStats.length > 1) {
      const midPoint = Math.floor(sortedDailyStats.length / 2);
      const recentPeriod = sortedDailyStats.slice(midPoint);
      const earlierPeriod = sortedDailyStats.slice(0, midPoint);

      const recentAvg = recentPeriod.length > 0 ? recentPeriod.reduce((sum, day) => sum + day.totalDuration, 0) / recentPeriod.length : 0;
      const earlierAvg = earlierPeriod.length > 0 ? earlierPeriod.reduce((sum, day) => sum + day.totalDuration, 0) / earlierPeriod.length : 0;

      if (earlierAvg > 0) {
        avgFocusPercentChange = Math.round(((recentAvg - earlierAvg) / earlierAvg) * 100);
      } else {
        avgFocusPercentChange = recentAvg > 0 ? 100 : 0;
      }
    }

    return {
      totalFocusTime,
      avgFocusPerDay,
      categoryDistribution,
      dailyFocusTime,
      dailyFocusPercentChange,
      avgFocusPercentChange
    };
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.round(seconds % 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    }
    if (minutes > 0) {
      return `${minutes}m ${secs}s`
    }
    return `${secs}s`
  }

  // Function to process sessions into daily timeline format
  const processDailySessions = (sessions: StudySession[] | {[id: string]: StudySession}, colorMap: {[key: string]: string}) => {
    const dailySessionsMap: { [date: string]: any[] } = {};

    // Keep a mapping of display IDs to actual Firestore IDs
    const sessionIdMap: {[displayId: string]: string} = {};

    // Handle both array of sessions and object of sessions
    if (Array.isArray(sessions)) {
      console.log(`Processing ${sessions.length} sessions as array`);
      // If sessions is an array, we don't have direct access to Firestore IDs
      sessions.forEach((session, index) => {
        // Skip sessions without proper time data
        if (!session.date || !session.startTime || !session.endTime) {
          console.log(`Skipping session ${index} due to missing date/time data`);
          return;
        }

        const sessionDate = session.date;

        if (!dailySessionsMap[sessionDate]) {
          dailySessionsMap[sessionDate] = [];
        }

        // Properly handle various date formats that may be stored in Firebase
        let startTime: Date;
        let endTime: Date;

        if (session.startTime instanceof Date) {
          startTime = session.startTime;
        } else if (typeof session.startTime === 'object' && session.startTime !== null && 'seconds' in session.startTime) {
          // Handle Firestore Timestamp objects
          const timestamp = session.startTime as FirestoreTimestamp;
          startTime = new Date(timestamp.seconds * 1000);
        } else if (typeof session.startTime === 'number') {
          // Handle millisecond timestamps
          startTime = new Date(session.startTime);
        } else if (typeof session.startTime === 'string') {
          // Handle ISO strings
          startTime = new Date(session.startTime);
        } else {
          // Fallback - create a date at the beginning of the day
          const dateParts = session.date.split('-').map(part => parseInt(part));
          startTime = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], 0, 0, 0);
        }

        if (session.endTime instanceof Date) {
          endTime = session.endTime;
        } else if (typeof session.endTime === 'object' && session.endTime !== null && 'seconds' in session.endTime) {
          // Handle Firestore Timestamp objects
          const timestamp = session.endTime as FirestoreTimestamp;
          endTime = new Date(timestamp.seconds * 1000);
        } else if (typeof session.endTime === 'number') {
          // Handle millisecond timestamps
          endTime = new Date(session.endTime);
        } else if (typeof session.endTime === 'string') {
          // Handle ISO strings
          endTime = new Date(session.endTime);
        } else {
          // Fallback - create a date at the end of the day
          const dateParts = session.date.split('-').map(part => parseInt(part));
          endTime = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], 23, 59, 59);
        }

        // Verify that dates are valid before creating the timeline session
        if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
          console.error('Invalid date detected:', { session, startTime, endTime });
          return; // Skip this session
        }

        // Create a display ID for the UI
        const displayId = `session-${index}`;

        // Create timeline session object
        const timelineSession = {
          id: displayId,
          // We don't have a Firestore ID for array-based sessions
          // but if the session has an explicit ID property, we can try to use that
          firestoreId: (session as any).id || null,
          subject: session.subject,
          taskType: session.taskType || "Study", // Default if not specified
          taskDescription: session.taskDescription || session.taskName || "", // Use taskName as fallback
          startTime,
          endTime,
          duration: session.duration,
          subjectColor: session.subjectColor || colorMap[session.subject] || "#6366f1"
        };

        dailySessionsMap[sessionDate].push(timelineSession);
      });
    } else {
      // If sessions is an object with Firestore IDs as keys
      console.log(`Processing ${Object.keys(sessions).length} sessions as object with IDs`);

      Object.entries(sessions).forEach(([firestoreId, session], index) => {
        // Skip sessions without proper time data
        if (!session.date || !session.startTime || !session.endTime) {
          console.log(`Skipping session ${firestoreId} due to missing date/time data`);
          return;
        }

        const sessionDate = session.date;

        if (!dailySessionsMap[sessionDate]) {
          dailySessionsMap[sessionDate] = [];
        }

        // Properly handle various date formats that may be stored in Firebase
        let startTime: Date;
        let endTime: Date;

        if (session.startTime instanceof Date) {
          startTime = session.startTime;
        } else if (typeof session.startTime === 'object' && session.startTime !== null && 'seconds' in session.startTime) {
          // Handle Firestore Timestamp objects
          const timestamp = session.startTime as FirestoreTimestamp;
          startTime = new Date(timestamp.seconds * 1000);
        } else if (typeof session.startTime === 'number') {
          // Handle millisecond timestamps
          startTime = new Date(session.startTime);
        } else if (typeof session.startTime === 'string') {
          // Handle ISO strings
          startTime = new Date(session.startTime);
        } else {
          // Fallback - create a date at the beginning of the day
          const dateParts = session.date.split('-').map(part => parseInt(part));
          startTime = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], 0, 0, 0);
        }

        if (session.endTime instanceof Date) {
          endTime = session.endTime;
        } else if (typeof session.endTime === 'object' && session.endTime !== null && 'seconds' in session.endTime) {
          // Handle Firestore Timestamp objects
          const timestamp = session.endTime as FirestoreTimestamp;
          endTime = new Date(timestamp.seconds * 1000);
        } else if (typeof session.endTime === 'number') {
          // Handle millisecond timestamps
          endTime = new Date(session.endTime);
        } else if (typeof session.endTime === 'string') {
          // Handle ISO strings
          endTime = new Date(session.endTime);
        } else {
          // Fallback - create a date at the end of the day
          const dateParts = session.date.split('-').map(part => parseInt(part));
          endTime = new Date(dateParts[0], dateParts[1] - 1, dateParts[2], 23, 59, 59);
        }

        // Verify that dates are valid before creating the timeline session
        if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
          console.error('Invalid date detected:', { session, startTime, endTime });
          return; // Skip this session
        }

        // Create a display ID for the UI
        const displayId = `session-${index}`;

        // Store mapping of display ID to Firestore ID
        sessionIdMap[displayId] = firestoreId;
        console.log(`Mapping display ID ${displayId} to Firestore ID ${firestoreId}`);

        // Create timeline session object
        const timelineSession = {
          id: displayId,
          firestoreId: firestoreId, // Store the actual Firestore ID
          subject: session.subject,
          taskType: session.taskType || "Study", // Default if not specified
          taskDescription: session.taskDescription || session.taskName || "", // Use taskName as fallback
          startTime,
          endTime,
          duration: session.duration,
          subjectColor: session.subjectColor || colorMap[session.subject] || "#6366f1"
        };

        dailySessionsMap[sessionDate].push(timelineSession);
      });

      // Store the session ID mapping in state
      console.log(`Setting session ID mapping with ${Object.keys(sessionIdMap).length} entries`);
      setSessionIdMapping(sessionIdMap);
    }

    // Convert to array format and sort sessions by time
    return Object.keys(dailySessionsMap).map(date => ({
      date,
      sessions: dailySessionsMap[date].sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
    }));
  };

  // Alternative approach: Add function to get Firestore ID for a session display ID
  const getFirestoreIdForSession = (displayId: string): string | null => {
    // Check if we have a valid mapping
    if (!Object.keys(sessionIdMapping).length) {
      console.warn("Session ID mapping is empty. This could happen if sessions were loaded as an array instead of an object with Firestore IDs.");

      // Try to extract firestoreId from the display object if it's stored there
      const allSessions = dailySessions.flatMap(day => day.sessions);
      const session = allSessions.find(s => s.id === displayId);

      if (session && 'firestoreId' in session) {
        console.log(`Found firestoreId ${(session as any).firestoreId} directly in session object for ${displayId}`);
        return (session as any).firestoreId;
      }

      console.error("Cannot find Firestore ID for session:", displayId);
      console.info("Available sessions:", allSessions.length);
      return null;
    }

    const firestoreId = sessionIdMapping[displayId];
    if (!firestoreId) {
      console.error("Session mapping ID not found for:", displayId);
      console.info("Available mappings:", Object.keys(sessionIdMapping));
    }
    return firestoreId || null;
  }

  // Function to handle session editing
  const handleEditSession = async (sessionId: string, updatedSession: Partial<SessionDetail>): Promise<boolean> => {
    if (!user) return false;

    setIsOperationPending(true);

    try {
      // Find the actual Firestore ID using our mapping
      const firestoreId = getFirestoreIdForSession(sessionId);

      if (!firestoreId) {
        console.error("Cannot find Firestore ID for session:", sessionId);

        // Try to find the session object directly to see if it has firestoreId
        const allSessions = dailySessions.flatMap(day => day.sessions);
        const session = allSessions.find(s => s.id === sessionId);

        const errorMsg = session
          ? "Session found but Firestore ID is missing. This may happen when using the 'What You Studied' section with sessions imported without IDs."
          : "Session ID mapping not found. Please refresh the page and try again.";

        toast({
          title: "Error",
          description: errorMsg,
          variant: "destructive"
        });
        return false;
      }

      console.log(`Editing session ${sessionId} with Firestore ID: ${firestoreId}`);

      const userDoc = await getDoc(doc(db, "users", user.uid));
      if (!userDoc.exists()) {
        console.error("User document does not exist");
        toast({
          title: "Error",
          description: "Could not find your user data",
          variant: "destructive"
        });
        return false;
      }

      const userData = userDoc.data();
      const studySessions = userData.studySessions || {};

      // Check if the session exists using the Firestore ID
      if (!studySessions[firestoreId]) {
        console.error("Session not found in Firestore:", firestoreId);
        toast({
          title: "Error",
          description: "Session not found",
          variant: "destructive"
        });
        return false;
      }

      // Calculate new duration if start or end time changed
      let newDuration = studySessions[firestoreId].duration;
      if (updatedSession.startTime && updatedSession.endTime) {
        newDuration = Math.floor((updatedSession.endTime.getTime() - updatedSession.startTime.getTime()) / 1000);
      }

      // Update the session
      const updatedSessionData = {
        ...studySessions[firestoreId],
        ...updatedSession,
        duration: newDuration
      };

      // Update the session in Firestore
      await updateDoc(doc(db, "users", user.uid), {
        [`studySessions.${firestoreId}`]: updatedSessionData,
        lastUpdated: new Date().toISOString()
      });

      // Update local state
      const updatedSessions = { ...studySessions };
      updatedSessions[firestoreId] = updatedSessionData;

      // Recalculate analytics based on updated sessions
      const sessionsArray = Object.values(updatedSessions) as StudySession[];
      const updatedAnalytics = processAnalytics(sessionsArray);
      setAnalytics(updatedAnalytics);

      // Update focus analytics
      const updatedFocusAnalytics = generateFocusAnalytics(updatedAnalytics);
      setFocusAnalytics(updatedFocusAnalytics);

      // Update daily sessions
      const updatedDailySessions = processDailySessions(updatedSessions, subjectColorMap);
      setDailySessions(updatedDailySessions);

      toast({
        title: "Session updated",
        description: "Your study session has been updated successfully.",
      });

      return true;
    } catch (error) {
      console.error("Error updating session:", error);
      toast({
        title: "Error",
        description: "Failed to update session",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsOperationPending(false);
    }
  };

  // Function to handle session deletion
  const handleDeleteSession = async (sessionId: string): Promise<boolean> => {
    if (!user) return false;

    setIsOperationPending(true);

    try {
      // Find the actual Firestore ID using our mapping
      const firestoreId = getFirestoreIdForSession(sessionId);

      if (!firestoreId) {
        console.error("Cannot find Firestore ID for session:", sessionId);

        // Try to find the session object directly to see if it has firestoreId
        const allSessions = dailySessions.flatMap(day => day.sessions);
        const session = allSessions.find(s => s.id === sessionId);

        const errorMsg = session
          ? "Session found but Firestore ID is missing. This may happen when using the 'What You Studied' section with sessions imported without IDs."
          : "Session ID mapping not found. Please refresh the page and try again.";

        toast({
          title: "Error",
          description: errorMsg,
          variant: "destructive"
        });
        return false;
      }

      console.log(`Deleting session ${sessionId} with Firestore ID: ${firestoreId}`);

      const userDoc = await getDoc(doc(db, "users", user.uid));
      if (!userDoc.exists()) {
        console.error("User document does not exist");
        toast({
          title: "Error",
          description: "Could not find your user data",
          variant: "destructive"
        });
        return false;
      }

      const userData = userDoc.data();
      const studySessions = userData.studySessions ? { ...userData.studySessions } : {};

      // Check if the session exists using the Firestore ID
      if (!studySessions[firestoreId]) {
        console.error("Session not found in Firestore:", firestoreId);
        toast({
          title: "Error",
          description: "Session not found",
          variant: "destructive"
        });
        return false;
      }

      // Delete the session
      delete studySessions[firestoreId];

      // Update Firestore
      await updateDoc(doc(db, "users", user.uid), {
        studySessions: studySessions,
        lastUpdated: new Date().toISOString()
      });

      // Recalculate analytics based on updated sessions
      const sessionsArray = Object.values(studySessions) as StudySession[];
      const updatedAnalytics = processAnalytics(sessionsArray);
      setAnalytics(updatedAnalytics);

      // Update focus analytics
      const updatedFocusAnalytics = generateFocusAnalytics(updatedAnalytics);
      setFocusAnalytics(updatedFocusAnalytics);

      // Update streak info
      const updatedStreakInfo = calculateStreakInfo(updatedAnalytics.dailyStats);
      setStreakInfo(updatedStreakInfo);

      // Update daily sessions
      const updatedDailySessions = processDailySessions(studySessions, subjectColorMap);
      setDailySessions(updatedDailySessions);

      // Update modal data if open
      if (selectedModalDate) {
        const dateStr = formatDateToLocalYYYYMMDD(selectedModalDate);
        const updatedDailyStat = updatedAnalytics.dailyStats.find(day => day.date === dateStr);
        setSelectedModalData(updatedDailyStat);
      }

      toast({
        title: "Session deleted",
        description: "Your study session has been deleted successfully.",
      });

      return true;
    } catch (error) {
      console.error("Error deleting session:", error);
      toast({
        title: "Error",
        description: "Failed to delete session",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsOperationPending(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="relative flex flex-col items-center">
          <div className="absolute -inset-10 bg-gradient-to-r from-primary/10 to-pink-500/10 rounded-full blur-xl opacity-70 animate-pulse"></div>
          <div className="h-12 w-12 rounded-full border-2 border-primary/30 border-t-primary/80 animate-spin"></div>
          <span className="mt-4 text-muted-foreground text-sm font-medium">Loading analytics...</span>
        </div>
      </div>
    )
  }

  // Handle case where analytics or focusAnalytics might still be null after loading
  if (!analytics || !focusAnalytics) {
      return (
          <div className="flex items-center justify-center min-h-screen bg-background text-foreground">
              <div className="bg-card/60 backdrop-blur-md rounded-xl border border-border/40 shadow-lg p-8 max-w-md">
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 rounded-full bg-muted/60 flex items-center justify-center mb-4 shadow-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" className="text-muted-foreground">
                      <circle cx="12" cy="12" r="10"/>
                      <line x1="12" y1="8" x2="12" y2="12"/>
                      <line x1="12" y1="16" x2="12.01" y2="16"/>
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Analytics Data Available</h3>
                  <p className="text-muted-foreground">Your analytics data could not be loaded. Please try refreshing the page or start tracking your study sessions.</p>
                  <button 
                    onClick={() => window.location.reload()}
                    className="mt-4 px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-lg font-medium transition-colors"
                  >
                    Reload Page
                  </button>
                </div>
              </div>
          </div>
      );
  }

  // Function to delete a subject
  const handleDeleteSubject = async (subjectName: string) => {
    if (!user) return;

    // Set deleting state
    setDeletingSubject(subjectName);

    try {
      // Get user subjects from Firebase
      const userSubjectsRef = doc(db, 'userSubjects', user.uid);
      const userSubjectsSnap = await getDoc(userSubjectsRef);

      if (!userSubjectsSnap.exists()) {
        console.error('User subjects not found');
        setDeletingSubject(null);
        toast({
          title: "Error",
          description: "User subjects not found",
          variant: "destructive"
        });
        return;
      }

      // Get current subjects
      const userData = userSubjectsSnap.data();
      const subjects = userData.subjects || [];

      // Filter out the subject to delete
      const updatedSubjects = subjects.filter((subject: any) => subject.name !== subjectName);

      // Update Firebase userSubjects collection
      await setDoc(userSubjectsRef, { subjects: updatedSubjects }, { merge: true });

      // Get all study sessions
      const userRef = doc(db, 'users', user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        const studySessions = userData.studySessions || {};

        // Create new sessions object without the deleted subject's sessions
        const filteredSessions: { [key: string]: any } = {};

        // Only keep sessions that don't match the deleted subject
        Object.entries(studySessions).forEach(([sessionId, session]: [string, any]) => {
          if (session.subject !== subjectName) {
            filteredSessions[sessionId] = session;
          }
        });

        console.log(`[handleDeleteSubject] Deleting subject: ${subjectName}`);
        console.log(`[handleDeleteSubject] Original session count: ${Object.keys(studySessions).length}`);
        console.log(`[handleDeleteSubject] Filtered session count: ${Object.keys(filteredSessions).length}`);
        // console.log(`[handleDeleteSubject] Filtered sessions keys:`, Object.keys(filteredSessions)); // Optional: Log keys if needed for deep debugging

        // Update the user document with filtered sessions using updateDoc
        await updateDoc(userRef, {
          studySessions: filteredSessions, // Overwrite the studySessions field
          lastUpdated: new Date().toISOString()
        });
        // Note: No { merge: true } needed with updateDoc when replacing a field

        console.log(`[handleDeleteSubject] Successfully updated studySessions in Firestore for user ${user.uid} using updateDoc`);

        // Update local subject color map
        const updatedColorMap = { ...subjectColorMap };
        delete updatedColorMap[subjectName];
        setSubjectColorMap(updatedColorMap);

        // Process the updated sessions to refresh analytics
        const updatedAnalytics = processAnalytics(Object.values(filteredSessions));

        setAnalytics(updatedAnalytics);

        // Update focus analytics
        if (updatedAnalytics) {
          const updatedFocusAnalytics = generateFocusAnalytics(updatedAnalytics);
          setFocusAnalytics(updatedFocusAnalytics);
        }

        // Show success toast
        toast({
          title: "Subject deleted",
          description: `${subjectName} has been deleted successfully.`,
        });
      }

      // Clear deleting state
      setDeletingSubject(null);
    } catch (error) {
      console.error('Error deleting subject:', error);
      setDeletingSubject(null);
      toast({
        title: "Error",
        description: "Failed to delete subject",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="relative min-h-screen w-full bg-background text-foreground overflow-hidden">
      <SmallDeviceWarning />

      {/* Removed complex background elements for theme compatibility */}
      {/* <style dangerouslySetInnerHTML={{ __html: animationStyles }} /> */}
      {/* <div className="absolute inset-0 bg-[url('...')]"></div> */}
      {/* <div className="absolute inset-0 bg-gradient-to-tr ..."></div> */}
      {/* <div className={`absolute inset-0 ${getBackgroundStyle()} opacity-30`} /> */}
      {/* <div className="absolute ... animate-blob"></div> */}

      <Header />

      {/* Main content */}
      <div className="relative z-10 container mx-auto px-4 md:px-6 py-4">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-8 pt-20 pb-24" ref={contentRef}>
          <Tabs
            defaultValue="overview"
            className="w-full"
            onValueChange={(value) => setSelectedTab(value)}
          >
            <div className="flex justify-center mb-8 overflow-x-auto">
              <div className="w-full max-w-3xl">
                <TabsList className="flex flex-wrap md:flex-nowrap w-full bg-background rounded-xl p-1 border border-border shadow-sm">
                  {[
                    { value: "overview", label: "Overview" },
                    { value: "daily", label: "Daily" },
                    { value: "weekly", label: "Weekly" },
                    { value: "monthly", label: "Monthly" },
                    { value: "subjects", label: "Subjects" },
                    { value: "taskTypes", label: "Task Types" }
                  ].map((tab, index, array) => (
                    <TabsTrigger
                      key={tab.value}
                      value={tab.value}
                      className={`
                        flex-1 py-2.5 text-sm font-medium transition-all
                        ${index === 0 ? 'rounded-l-lg' : ''}
                        ${index === array.length - 1 ? 'rounded-r-lg' : ''}
                        ${(index !== 0 && index !== array.length - 1) ? 'rounded-md' : ''}
                        data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-500
                        data-[state=active]:text-white data-[state=active]:shadow-md data-[state=active]:z-10
                        data-[state=inactive]:text-muted-foreground data-[state=inactive]:hover:text-foreground
                        data-[state=inactive]:hover:bg-muted/50
                      `}
                    >
                      {tab.label}
                    </TabsTrigger>
                  ))}
                  {/* Commented out Focus tab */}
                  {/*
                  <TabsTrigger
                    value="focus"
                    className="flex-1 py-2.5 text-sm font-medium rounded-md transition-all
                      data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-500
                      data-[state=active]:text-white data-[state=active]:shadow-md data-[state=active]:z-10
                      data-[state=inactive]:text-muted-foreground data-[state=inactive]:hover:text-foreground
                      data-[state=inactive]:hover:bg-muted/50"
                  >
                    Focus Insights
                  </TabsTrigger>
                  */}
                </TabsList>
              </div>
            </div>

            <TabsContent value="overview" data-tab="overview">
              {analytics && focusAnalytics && (
                <OverviewTab
                  analytics={analytics}
                  focusAnalytics={focusAnalytics}
                  selectedDate={selectedDate}
                  setSelectedDate={setSelectedDate}
                  formatDuration={formatDuration}
                  subjectColorMap={subjectColorMap}
                  targetHours={targetHours}
                  setTargetHours={updateTargetHours}
                  dailyMotivation={dailyMotivation}
                  setDailyMotivation={updateDailyMotivation}
                  streakInfo={streakInfo}
                  handleDayClick={handleDayClick}
                  theme={theme}
                  muiTheme={muiTheme}
                  dailySessions={dailySessions.filter(day => day.date === selectedDate)}
                  onEditSession={handleEditSession}
                  onDeleteSession={handleDeleteSession}
                  subjects={availableSubjects}
                  taskTypes={availableTaskTypes}
                />
              )}
            </TabsContent>

            <TabsContent value="daily" data-tab="daily" className="outline-none mt-6">
              {analytics && (
                <DailyTab
                  analytics={analytics}
                  dailySessions={dailySessions}
                  selectedDate={selectedDate}
                  setSelectedDate={setSelectedDate}
                  formatDuration={formatDuration}
                  subjectColorMap={subjectColorMap}
                  targetHours={targetHours}
                  theme={theme}
                  muiTheme={theme === 'dark' ? darkTheme : lightTheme}
                />
              )}
            </TabsContent>

            <TabsContent value="weekly" data-tab="weekly">
              {analytics && (
                <WeeklyTab
                  analytics={analytics}
                  formatDuration={formatDuration}
                  subjectColorMap={subjectColorMap}
                  theme={theme}
                  muiTheme={muiTheme}
                />
              )}
            </TabsContent>

            <TabsContent value="monthly" data-tab="monthly">
              {analytics && (
                <MonthlyTab
                  analytics={analytics}
                  formatDuration={formatDuration}
                  subjectColorMap={subjectColorMap}
                  muiTheme={muiTheme}
                />
              )}
            </TabsContent>

            <TabsContent value="subjects" data-tab="subjects">
              {analytics && (
                <SubjectsTab
                  analytics={analytics}
                  formatDuration={formatDuration}
                  subjectColorMap={subjectColorMap}
                  theme={theme}
                  muiTheme={muiTheme}
                  handleDeleteSubject={handleDeleteSubject}
                  deletingSubject={deletingSubject}
                  allSessions={[]}
                />
              )}
            </TabsContent>

            <TabsContent value="taskTypes" data-tab="taskTypes">
              {analytics && (
                <TaskTypeTab
                  analytics={analytics}
                  formatDuration={formatDuration}
                  theme={theme === 'dark' ? 'dark' : 'light'}
                  muiTheme={muiTheme}
                />
              )}
            </TabsContent>

            {/* Comment out FocusTab since its import is missing */}
            {/*
            <TabsContent value="focus" data-tab="focus">
              {focusAnalytics && (
                <FocusTab
                  focusAnalytics={focusAnalytics}
                  formatDuration={formatDuration}
                  streakInfo={streakInfo}
                  theme={theme}
                  muiTheme={muiTheme}
                />
              )}
            </TabsContent>
            */}
          </Tabs>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative z-10 mt-4 border-t bg-muted/50 backdrop-blur-xl"> {/* Use theme colors */}
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-3">
                <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-10 h-10 rounded-full shadow-lg" /> {/* Removed specific shadow color */}
                {/* Use primary color for logo text or keep gradient */}
                <span className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-pink-400">
                  IsotopeAI
                </span>
              </div>
              <p className="text-muted-foreground max-w-md">
                Your all-in-one platform for AI-powered learning, productivity tools, and collaborative study.
              </p>
            </div>

            <div className="flex flex-col md:items-end space-y-4">
              <div className="flex items-center space-x-4">
                <FeedbackWidget className="text-muted-foreground hover:text-primary transition-colors" />
                <span className="text-border">|</span>
                <a href="https://isotopeai.featurebase.app/changelog" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors">
                  Changelog
                </a>
                <span className="text-border">|</span>
                <a
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Contact
                </a>
                <span className="text-border">|</span>
                <a
                  href="https://www.instagram.com/isotope.ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                >
                  <svg /* Instagram Icon */
                    xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-instagram" > <rect width="20" height="20" x="2" y="2" rx="5" ry="5" /> <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" /> <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" /> </svg>
                </a>
                <span className="text-border">|</span>
                <a
                  href="https://www.reddit.com/r/Isotope/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors flex items-center gap-1"
                >
                  <svg /* Reddit Icon */
                    xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" > <circle cx="12" cy="12" r="10" /> <circle cx="12" cy="9" r="1" /> <circle cx="12" cy="15" r="1" /> <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" /> <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" /> <path d="M7.5 13h9" /> <path d="M10 16v-3" /> <path d="M14 16v-3" /> </svg>
                </a>
              </div>
              <p className="text-muted-foreground text-sm">
                Built with <span className="text-pink-500">❤️</span> by a fellow JEEtard
              </p>
            </div>
          </div>
        </div>

        {/* Removed dark gradient overlay */}
        {/* <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent pointer-events-none" /> */}
      </footer>

      {/* Render the Day Detail Modal */}
      <DayDetailModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedDate={selectedModalDate}
        studyData={selectedModalData}
        sessionDetails={selectedModalDate ?
          dailySessions.find(day => day.date === formatDateToLocalYYYYMMDD(selectedModalDate))?.sessions || []
          : []
        }
        formatDuration={formatDuration}
        subjectColorMap={subjectColorMap}
        onEditSession={handleEditSession}
        onDeleteSession={handleDeleteSession}
        subjects={availableSubjects}
        taskTypes={availableTaskTypes}
      />
    </div>
  )
}
