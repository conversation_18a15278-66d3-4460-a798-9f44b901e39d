import { useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import { useUserStore } from '@/stores/userStore'

export default function Login() {
  const navigate = useNavigate()
  const location = useLocation()
  const { authProvider } = useUserStore()

  // Use the appropriate auth hook based on the provider
  let user, loading, signInWithGoogle;

  if (authProvider === 'supabase') {
    try {
      const supabaseAuth = useSupabaseAuth();
      user = supabaseAuth.user;
      loading = supabaseAuth.loading;
      signInWithGoogle = supabaseAuth.signInWithGoogle;
    } catch {
      // Fallback to Firebase auth if Supabase context is not available
      const firebaseAuth = useAuth();
      user = firebaseAuth.user;
      loading = firebaseAuth.loading;
      signInWithGoogle = firebaseAuth.signInWithGoogle;
    }
  } else {
    const firebaseAuth = useAuth();
    user = firebaseAuth.user;
    loading = firebaseAuth.loading;
    signInWithGoogle = firebaseAuth.signInWithGoogle;
  }

  useEffect(() => {
    if (!loading && user) {
      // Check for returnUrl in query parameters
      const searchParams = new URLSearchParams(location.search)
      const returnUrl = searchParams.get('returnUrl')
      
      // Navigate to the return URL, intended path from state, or default to AI page
      const intendedPath = returnUrl || location.state?.from || '/ai'
      navigate(intendedPath, { replace: true })
    }
  }, [user, loading, navigate, location])

  const handleLogin = async () => {
    try {
      await signInWithGoogle()
    } catch (error) {
      console.error('Login error:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Welcome Back</h1>
          <p className="mt-2 text-muted-foreground">
            Sign in to continue to IsotopeAI
          </p>
        </div>

        <div className="space-y-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleLogin}
          >
            Continue with Google
          </Button>

          {authProvider === 'supabase' && (
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-2">
                Have existing data from the old system?
              </p>
              <Button
                variant="link"
                onClick={() => navigate('/data-migration')}
                className="text-blue-600 hover:text-blue-700"
              >
                Link Your Existing Data
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 