import { But<PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, Timer, BarChart, Calendar, Target, Zap, LineChart, Menu, X, Instagram, Mail } from "lucide-react";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { SignIn } from "@/components/SignIn";
import { Header } from "@/components/shared";
import { Footer } from "@/components/shared";
import { Helmet } from "react-helmet";

const ProductivityLanding = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signInWithGoogle } = useAuth();
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/productivity');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted relative overflow-hidden flex flex-col">
      <Helmet>
        <title>Study Productivity Tools & Time Management | IsotopeAI</title>
        <meta
          name="description"
          content="Boost your study productivity with our time management tools. Track progress, use Pomodoro timer, set goals, and visualize your study patterns."
        />
        <meta
          name="keywords"
          content="study productivity, pomodoro timer, study time management, focus timer, progress tracking, study analytics, study scheduler, goal tracking, focus mode, performance insights, JEE preparation tools, NEET study tools"
        />
        <meta property="og:title" content="Study Productivity Tools & Time Management | IsotopeAI" />
        <meta property="og:description" content="Boost your study productivity with our time management tools. Track progress, use Pomodoro timer, set goals, and visualize your study patterns." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/productivity-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/productivity-landing" />
      </Helmet>
      
      {/* Header */}
      <Header />
      
      {/* Floating Elements */}
      <FloatingElements />
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-4 py-16 pt-28 md:pt-40 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <motion.div 
                className="inline-block mb-4"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ 
                  duration: 0.5,
                  type: "spring",
                  stiffness: 200
                }}
              >
                <div className="bg-primary/20 p-3 rounded-full inline-block">
                  <Zap className="w-8 h-8 text-primary" />
                </div>
              </motion.div>
              
              {/* 100% Free Badge */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="mb-4"
              >
                <span className="bg-green-500/20 text-green-500 font-semibold px-4 py-2 rounded-full inline-flex items-center gap-1 border border-green-500/30">
                  <img src="/favicon.ico" alt="IsotopeAI Logo" className="w-4 h-4" />
                  100% Free Forever
                </span>
              </motion.div>
              
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
                Boost Your Study Productivity
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto lg:mx-0">
                Track your progress, manage your time, and achieve your study goals effectively
              </p>
              
              {user ? (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Go to Productivity Tools <ArrowRight className="ml-2" />
                </Button>
              ) : (
                <Button 
                  size="lg" 
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 sm:px-8 py-4 sm:py-6 h-auto text-base sm:text-lg shadow-lg shadow-primary/20 w-full sm:w-auto"
                  onClick={handleGetStarted}
                >
                  Try now (It's 100% FREE) <ArrowRight className="ml-2" />
                </Button>
              )}
            </motion.div>
            
            {/* Right side - Productivity Demo */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="lg:mt-0 mt-8"
            >
              <div className="bg-card rounded-xl p-6 border border-white/10 shadow-lg">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/20 p-2 rounded-full">
                      <Timer className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold">Pomodoro Timer</h3>
                      <p className="text-sm text-muted-foreground">Focus & Break Sessions</p>
                    </div>
                  </div>
                  <span className="bg-green-500/20 text-green-500 px-2 py-1 rounded-full text-xs">Active</span>
                </div>
                
                <div className="text-center py-8">
                  <div className="text-5xl font-bold mb-4 text-primary">25:00</div>
                  <p className="text-muted-foreground mb-6">Focus Session</p>
                  
                  <div className="flex justify-center gap-3">
                    <Button variant="outline" size="sm" className="rounded-full px-4">
                      Start
                    </Button>
                    <Button variant="outline" size="sm" className="rounded-full px-4">
                      Reset
                    </Button>
                  </div>
                </div>
                
                <div className="mt-6 space-y-4">
                  <div>
                    <div className="flex justify-between mb-1 text-sm">
                      <span className="text-muted-foreground">Today's Progress</span>
                      <span className="font-medium">2/4 sessions</span>
                    </div>
                    <div className="h-2 bg-primary/20 rounded-full">
                      <div className="h-full w-1/2 bg-primary rounded-full"></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1 text-sm">
                      <span className="text-muted-foreground">Focus Score</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="h-2 bg-primary/20 rounded-full">
                      <div className="h-full w-[85%] bg-primary rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="container mx-auto px-4 py-16 relative z-10">
          <motion.h2 
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            Powerful Features for <span className="text-primary">Effective Productivity</span>
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Timer className="w-8 h-8" />}
              title="Pomodoro Timer"
              description="Stay focused with customizable study and break intervals"
              delay={0.1}
            />
            <FeatureCard
              icon={<BarChart className="w-8 h-8" />}
              title="Progress Analytics"
              description="Visualize your study patterns and improvements"
              delay={0.2}
            />
            <FeatureCard
              icon={<Calendar className="w-8 h-8" />}
              title="Study Scheduler"
              description="Plan your study sessions and set reminders"
              delay={0.3}
            />
            <FeatureCard
              icon={<Target className="w-8 h-8" />}
              title="Goal Tracking"
              description="Set and monitor your study objectives"
              delay={0.4}
            />
            <FeatureCard
              icon={<Zap className="w-8 h-8" />}
              title="Focus Mode"
              description="Eliminate distractions during study sessions"
              delay={0.5}
            />
            <FeatureCard
              icon={<LineChart className="w-8 h-8" />}
              title="Performance Insights"
              description="Get detailed analytics about your study habits"
              delay={0.6}
            />
          </div>
        </section>

        {/* Stats Section */}
        <section className="container mx-auto px-4 py-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <StatCard
              number="25%"
              label="Average Productivity Increase"
            />
            <StatCard
              number="2x"
              label="Better Focus Duration"
            />
            <StatCard
              number="45+"
              label="Minutes Saved Per Study Session"
            />
          </div>
        </section>

        {/* CTA Section */}
        <section className="container mx-auto px-4 py-20 text-center relative z-10">
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-3xl p-12 backdrop-blur-sm border border-primary/20">
            <motion.h2 
              className="text-4xl font-bold mb-6"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Ready to Maximize Your Study Time?
            </motion.h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of students who have improved their study efficiency
            </p>
            
            {user ? (
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 rounded-full px-8 py-6 h-auto text-lg shadow-lg shadow-primary/20"
                onClick={handleGetStarted}
              >
                Go to Dashboard <ArrowRight className="ml-2" />
              </Button>
            ) : (
              <div className="flex justify-center">
                <SignIn />
              </div>
            )}
          </div>
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

const FeatureCard = ({ icon, title, description, delay = 0 }: { 
  icon: React.ReactNode; 
  title: string; 
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.05, boxShadow: "0 10px 30px -15px rgba(0, 0, 0, 0.3)" }}
      className="bg-card p-6 rounded-xl shadow-md border border-white/5 backdrop-blur-sm"
    >
      <div className="mb-4 text-primary bg-primary/10 p-3 rounded-lg inline-block">{icon}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </motion.div>
  );
};

const StatCard = ({ number, label }: { number: string; label: string }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      className="bg-card p-6 rounded-lg shadow-md text-center"
    >
      <h3 className="text-4xl font-bold text-primary mb-2">{number}</h3>
      <p className="text-muted-foreground">{label}</p>
    </motion.div>
  );
};

export default ProductivityLanding;

const FloatingElements = () => {
  return (
    <>
      <div className="absolute top-20 left-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute top-40 right-20 w-80 h-80 bg-primary/10 rounded-full blur-3xl" />
      <div className="absolute bottom-40 left-20 w-72 h-72 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-20 right-10 w-60 h-60 bg-primary/10 rounded-full blur-3xl" />
      
      <motion.div 
        className="absolute top-1/4 left-10 w-6 h-6 bg-primary/30 rounded-full"
        animate={{
          y: [0, 20, 0],
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div 
        className="absolute top-1/3 right-20 w-4 h-4 bg-primary/20 rounded-full"
        animate={{
          y: [0, -15, 0],
          opacity: [0.3, 0.8, 0.3]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div 
        className="absolute bottom-1/4 left-1/4 w-5 h-5 bg-primary/20 rounded-full"
        animate={{
          y: [0, 10, 0],
          opacity: [0.4, 0.9, 0.4]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />
    </>
  );
};

 