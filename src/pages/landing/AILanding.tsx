import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowRight, Brain, Check, FileText, Image, MessageSquare, Upload, Sparkles, User, Bot, Github, Instagram, Mail, Zap } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { SignIn } from "@/components/SignIn";
import { Header, Footer } from "@/components/shared";
import { Helmet } from "react-helmet";
import { useInView } from "framer-motion";
import { cn } from "@/lib/utils";
import { User as FirebaseUser } from "firebase/auth";
import { AdPlaceholder } from "@/components/ads";

const AILanding = () => {
  const { user, signInWithGoogle } = useAuth();
  const navigate = useNavigate();
  const [scrolled, setScrolled] = useState(false);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);

    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleGetStarted = () => {
    if (user) {
      navigate('/ai');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030014] font-onest">
      <Helmet>
        <title>AI Assistant for PCMB - Physics, Chemistry & Math Help | IsotopeAI</title>
        <meta
          name="description"
          content="Get instant help with Physics, Chemistry, and Mathematics problems for JEE, NEET & BITSAT preparation. Our AI assistant provides step-by-step solutions, exam-specific guidance, and personalized learning."
        />
        <meta
          name="keywords"
          content="AI tutor, physics help, chemistry assistant, math solver, PCMB doubt solver, JEE Main preparation, JEE Advanced preparation, NEET UG preparation, BITSAT preparation, IIT JEE coaching, NEET coaching, step-by-step solutions, educational AI, image recognition, homework help, competitive exam AI, entrance exam preparation, PCMB practice, JEE question solver, NEET question solver, BITSAT practice"
        />
        <meta property="og:title" content="AI Assistant for PCMB - Physics, Chemistry & Math Help | IsotopeAI" />
        <meta property="og:description" content="Get instant help with Physics, Chemistry, and Mathematics problems. Our AI assistant provides step-by-step solutions, image recognition, and personalized learning." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/ai-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/ai-landing" />

        {/* Ad-related meta tags */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="ad-client" content="ca-pub-9602732057654649" />
        <meta name="ad-format" content="auto" />
      </Helmet>

      {/* Modern Background */}
      <BackgroundElements />

      {/* Header */}
      <AnimatedHeader scrolled={scrolled} />

      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <HeroSection handleGetStarted={handleGetStarted} user={user} />

        {/* Top Ad Banner */}
        <div className="container mx-auto px-4 my-8">
          <AdPlaceholder format="horizontal" className="mx-auto" />
        </div>

        {/* Features Section */}
        <FeaturesSection />

        {/* Before/After Comparison */}
        <ResultsSection />

        {/* Middle Ad Banner */}
        <div className="container mx-auto px-4 my-8">
          <AdPlaceholder format="rectangle" className="mx-auto" />
        </div>

        {/* How It Works */}
        <HowItWorksSection />

        {/* CTA Section */}
        <CTASection handleGetStarted={handleGetStarted} user={user} />

        {/* Bottom Ad Banner */}
        <div className="container mx-auto px-4 my-8">
          <AdPlaceholder format="horizontal" className="mx-auto" />
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

// Background elements with modern design
const BackgroundElements = () => {
  return (
    <>
      {/* Dark gradient background with depth */}
      <div className="fixed inset-0 bg-gradient-radial from-[#0A0A1F] via-[#070722] to-[#030014] opacity-80 z-[-2]" />

      {/* Animated grid pattern */}
      <div className="fixed inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.015] z-[-1]" />

      {/* Animated subtle noise texture */}
      <div
        className="fixed inset-0 z-[-1] opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)' opacity='0.4'/%3E%3C/svg%3E")`,
          transform: 'translateZ(0)',
        }}
      />

      {/* Main glow elements */}
      <div className="fixed top-[-10%] right-[0%] w-[600px] h-[600px] bg-[#4338ca]/30 rounded-full blur-[120px] opacity-30 z-[-1]" />
      <div className="fixed bottom-[-15%] left-[-5%] w-[500px] h-[500px] bg-[#3b0764]/30 rounded-full blur-[120px] opacity-30 z-[-1]" />
      <div className="fixed top-[30%] left-[15%] w-[300px] h-[300px] bg-[#4f46e5]/20 rounded-full blur-[120px] opacity-20 z-[-1]" />

      {/* Animated floating particles */}
      {[...Array(20)].map((_, i) => (
        <motion.div
          key={i}
          className="fixed w-[2px] h-[2px] rounded-full bg-white opacity-70 z-[-1]"
          style={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, Math.random() * 30 - 15],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 3 + Math.random() * 5,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: Math.random() * 5,
          }}
        />
      ))}

      {/* Accent glowing orbs */}
      <motion.div
        className="fixed top-[20%] left-[10%] w-2 h-2 bg-cyan-500/90 rounded-full z-0"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />
      <motion.div
        className="fixed top-[40%] right-[15%] w-3 h-3 bg-purple-500/90 rounded-full z-0"
        animate={{
          scale: [1, 1.8, 1],
          opacity: [0.5, 0.7, 0.5],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1,
        }}
      />
      <motion.div
        className="fixed bottom-[30%] left-[25%] w-2 h-2 bg-blue-500/90 rounded-full z-0"
        animate={{
          scale: [1, 1.6, 1],
          opacity: [0.5, 0.9, 0.5],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2,
        }}
      />
    </>
  );
};

const AnimatedHeader = ({ scrolled }: { scrolled: boolean }) => {
  return (
    <motion.header
      className={`fixed w-full z-50 transition-all duration-500 ${scrolled ? 'py-2 backdrop-blur-md' : 'py-4 bg-transparent'}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className={`absolute inset-0 ${scrolled ? 'bg-[#030014]/80 backdrop-blur-xl border-b border-white/10' : 'bg-transparent'}`}></div>
      <Header />
    </motion.header>
  );
};

const HeroSection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: FirebaseUser | null }) => {
  return (
    <section className="container mx-auto px-4 py-16 pt-36 md:pt-40 relative z-10 min-h-screen flex items-center">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Left side - Text content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center lg:text-left"
        >
          <motion.div
            className="inline-block mb-6"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              duration: 0.7,
              type: "spring",
              stiffness: 200
            }}
          >
            <div className="bg-gradient-to-br from-indigo-500/30 to-purple-600/30 p-4 rounded-2xl inline-block shadow-inner shadow-white/5">
              <Bot className="w-8 h-8 text-indigo-400" />
            </div>
          </motion.div>

          {/* 100% Free Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-6"
          >
            <span className="bg-gradient-to-r from-emerald-500/20 to-emerald-700/20 text-emerald-400 font-medium px-5 py-2.5 rounded-full inline-flex items-center gap-2 border border-emerald-500/20 shadow-sm shadow-emerald-500/10">
              <Check className="w-4 h-4" />
              100% Free Forever
            </span>
          </motion.div>

          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            Your Personal
            <span className="relative inline-block ml-3 whitespace-nowrap">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">AI Study Assistant</span>
              <motion.span
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-indigo-400/0 via-purple-400 to-indigo-400/0"
                initial={{ width: 0, left: "50%" }}
                animate={{ width: "100%", left: 0 }}
                transition={{ duration: 1, delay: 1 }}
              ></motion.span>
            </span>
          </h1>

          <p className="text-xl text-white/70 mb-10 max-w-xl mx-auto lg:mx-0 leading-relaxed">
            Get instant help with PCMB subjects, step-by-step solutions, and personalized learning for your competitive exam preparation
          </p>

          {user ? (
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl px-8 py-6 h-auto text-base sm:text-lg shadow-lg shadow-indigo-600/20 font-medium transition-all duration-300"
                onClick={handleGetStarted}
              >
                Go to AI Assistant <ArrowRight className="ml-2" />
              </Button>
            </motion.div>
          ) : (
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl px-8 py-6 h-auto text-base sm:text-lg shadow-lg shadow-indigo-600/20 font-medium transition-all duration-300"
                onClick={handleGetStarted}
              >
                Try now (It's 100% FREE) <ArrowRight className="ml-2" />
              </Button>
            </motion.div>
          )}

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
            className="mt-12 flex flex-wrap justify-center lg:justify-start gap-4"
          >
            <div className="bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full text-xs text-white/50 border border-white/10">
              Used by 10,000+ students
            </div>
            <div className="bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full text-xs text-white/50 border border-white/10">
              Made for competitive exams
            </div>
            <div className="bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full text-xs text-white/50 border border-white/10">
              Updated daily
            </div>
          </motion.div>
        </motion.div>

        {/* Right side - Chat Demo */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="lg:mt-0 mt-8"
        >
          <AnimatedChatDemo />
        </motion.div>
      </div>
    </section>
  );
};

const FeaturesSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <div className="relative mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
            <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
            Powerful Features
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
            <span className="relative">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">Tools</span>
              <motion.span
                className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-indigo-400/0 via-purple-400 to-indigo-400/0"
                initial={{ width: 0, left: "50%" }}
                whileInView={{ width: "100%", left: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 1 }}
              ></motion.span>
            </span> for{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
              Effective Learning
            </span>
          </h2>
          <p className="text-white/60 max-w-2xl mx-auto text-lg">
            Our AI assistant is equipped with everything you need to master PCMB subjects and excel in your competitive exams
          </p>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <FeatureCard
          icon={<MessageSquare className="w-8 h-8" />}
          title="Smart Chat Interface"
          description="Ask questions naturally and get detailed explanations in real-time"
          delay={0.1}
          gradient="from-blue-600/20 via-indigo-600/20 to-indigo-600/10"
        />
        <FeatureCard
          icon={<Brain className="w-8 h-8" />}
          title="PCMB Expertise"
          description="Comprehensive coverage of Physics, Chemistry, and Mathematics"
          delay={0.2}
          gradient="from-purple-600/20 via-indigo-600/20 to-purple-600/10"
        />
        <FeatureCard
          icon={<Image className="w-8 h-8" />}
          title="Image Recognition"
          description="Upload images of problems for instant solutions"
          delay={0.3}
          gradient="from-indigo-600/20 via-violet-600/20 to-indigo-600/10"
        />
        <FeatureCard
          icon={<FileText className="w-8 h-8" />}
          title="Step-by-Step Solutions"
          description="Detailed explanations with every step clearly explained"
          delay={0.4}
          gradient="from-blue-600/20 via-indigo-600/20 to-blue-600/10"
        />
        <FeatureCard
          icon={<Upload className="w-8 h-8" />}
          title="Resource Integration"
          description="Seamlessly integrate with your study materials (Coming Soon)"
          delay={0.5}
          gradient="from-violet-600/20 via-purple-600/20 to-violet-600/10"
        />
        <FeatureCard
          icon={<Sparkles className="w-8 h-8" />}
          title="Personalized Learning"
          description="AI adapts to your learning style and knowledge level"
          delay={0.6}
          gradient="from-purple-600/20 via-indigo-600/20 to-purple-600/10"
        />
      </div>
    </section>
  );
};

const ResultsSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-transparent to-purple-600/5 z-[-1]"></div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
          <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
          Proven Results
        </div>
        <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
            Transformative
          </span>{" "}
          Learning Experience
        </h2>
        <p className="text-white/60 max-w-2xl mx-auto text-lg">
          See how IsotopeAI has helped students significantly improve their performance and understanding
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        {/* Before using IsotopeAI */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="relative group"
        >
          {/* Decorative gradient background */}
          <div className="absolute -inset-1 bg-gradient-to-r from-rose-600/10 via-red-600/5 to-rose-600/10 blur-xl opacity-70 rounded-2xl group-hover:opacity-100 transition-opacity duration-500"></div>

          <div className="bg-[#0d0d35]/60 backdrop-blur-md rounded-2xl p-8 border border-white/5 shadow-xl relative overflow-hidden h-full">
            {/* Glass effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-20"></div>

            <h3 className="text-xl font-semibold mb-6 flex items-center text-white/90 relative">
              <span className="bg-gradient-to-br from-rose-500/30 to-red-600/30 p-2 rounded-xl mr-3 border border-rose-500/20">
                <ArrowRight className="w-5 h-5 text-rose-400" />
              </span>
              Before Using IsotopeAI
            </h3>

            <div className="space-y-6 relative">
              <ResultBar
                label="Physics Test Scores"
                value={65}
                colorClass="from-rose-400 to-red-500/80"
                delay={0.1}
              />

              <ResultBar
                label="Chemistry Understanding"
                value={58}
                colorClass="from-rose-400 to-red-500/80"
                delay={0.2}
              />

              <ResultBar
                label="Math Problem-Solving"
                value={72}
                colorClass="from-rose-400 to-red-500/80"
                delay={0.3}
              />

              <ResultBar
                label="Study Efficiency"
                value={50}
                colorClass="from-rose-400 to-red-500/80"
                delay={0.4}
              />
            </div>

            <div className="mt-8 p-4 bg-[#0a0a2a]/80 rounded-xl text-white/60 text-sm italic border border-white/5 relative">
              "I was struggling to keep up with my PCMB subjects and felt overwhelmed by the complexity."
            </div>
          </div>
        </motion.div>

        {/* After using IsotopeAI */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative group"
        >
          {/* Decorative gradient background */}
          <div className="absolute -inset-1 bg-gradient-to-r from-emerald-600/10 via-green-600/5 to-emerald-600/10 blur-xl opacity-70 rounded-2xl group-hover:opacity-100 transition-opacity duration-500"></div>

          <div className="bg-[#0d0d35]/60 backdrop-blur-md rounded-2xl p-8 border border-white/5 shadow-xl relative overflow-hidden h-full">
            {/* Glass effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-20"></div>

            <h3 className="text-xl font-semibold mb-6 flex items-center text-white/90 relative">
              <span className="bg-gradient-to-br from-emerald-500/30 to-green-600/30 p-2 rounded-xl mr-3 border border-emerald-500/20">
                <ArrowRight className="w-5 h-5 text-emerald-400" />
              </span>
              After Using IsotopeAI
            </h3>

            <div className="space-y-6 relative">
              <ResultBar
                label="Physics Test Scores"
                value={92}
                initialValue={65}
                colorClass="from-emerald-400 to-green-500"
                delay={0.5}
              />

              <ResultBar
                label="Chemistry Understanding"
                value={89}
                initialValue={58}
                colorClass="from-emerald-400 to-green-500"
                delay={0.7}
              />

              <ResultBar
                label="Math Problem-Solving"
                value={95}
                initialValue={72}
                colorClass="from-emerald-400 to-green-500"
                delay={0.9}
              />

              <ResultBar
                label="Study Efficiency"
                value={90}
                initialValue={50}
                colorClass="from-emerald-400 to-green-500"
                delay={1.1}
              />
            </div>

            <div className="mt-8 p-4 bg-[#0a0a2a]/80 rounded-xl text-emerald-400 text-sm italic border border-white/5 relative">
              "With IsotopeAI, I've gained confidence in all my subjects and my grades have improved significantly!"
            </div>
          </div>
        </motion.div>
      </div>

      {/* Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="mt-20 text-center"
      >
        <p className="text-lg text-white/60 mb-10">
          Based on a survey of 500+ students who used IsotopeAI for at least 3 months
        </p>
        <div className="flex flex-wrap justify-center gap-8">
          <StatCard value="27%" label="Average Grade Improvement" delay={0.1} />
          <StatCard value="85%" label="Reported Better Understanding" delay={0.2} />
          <StatCard value="92%" label="Would Recommend to Friends" delay={0.3} />
        </div>
      </motion.div>
    </section>
  );
};

const HowItWorksSection = () => {
  return (
    <section className="relative py-28 overflow-hidden">
      {/* Background effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-indigo-900/5 via-purple-900/5 to-indigo-900/5 z-0"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
            <Brain className="h-4 w-4 mr-2 text-indigo-400" />
            Intelligent Process
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
            How IsotopeAI{" "}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
              Solves
            </span>{" "}
            Your Problems
          </h2>
          <p className="text-white/60 max-w-2xl mx-auto text-lg">
            Our advanced AI works through a streamlined process to deliver clear, accurate solutions for any PCMB question
          </p>
        </motion.div>

        <div className="relative">
          {/* Connection Line - animated gradient */}
          <div className="absolute top-1/2 left-0 w-full h-1 -translate-y-1/2 hidden md:block">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/0 via-indigo-500/30 to-indigo-500/0"></div>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-indigo-500/0 via-purple-500/50 to-indigo-500/0"
              animate={{
                x: ["-100%", "100%"]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
            {/* Step 1 */}
            <StepCard
              number={1}
              icon={<Upload className="w-10 h-10 text-indigo-400" />}
              title="Upload Your Question"
              description="Type your question directly or upload an image of your problem to get started instantly"
              delay={0.1}
              gradient="from-indigo-600/30 via-indigo-600/20 to-indigo-600/10"
            />

            {/* Step 2 */}
            <StepCard
              number={2}
              icon={<Brain className="w-10 h-10 text-indigo-400" />}
              title="AI Analyzes Problem"
              description="Our advanced AI rapidly understands the context and identifies the optimal solution approach"
              delay={0.3}
              gradient="from-purple-600/30 via-purple-600/20 to-purple-600/10"
            />

            {/* Step 3 */}
            <StepCard
              number={3}
              icon={<FileText className="w-10 h-10 text-indigo-400" />}
              title="Get Detailed Solution"
              description="Receive step-by-step explanations with clear reasoning designed specifically for JEE/NEET level learning"
              delay={0.5}
              gradient="from-indigo-600/30 via-indigo-600/20 to-indigo-600/10"
            />
          </div>
        </div>

        {/* Added content after the step cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-20 mb-10 max-w-4xl mx-auto"
        >
          <div className="bg-[#0d0d35]/30 backdrop-blur-sm rounded-xl p-8 border border-white/5 shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-2xl font-semibold mb-4 text-white/90 flex items-center gap-3">
                  <Sparkles className="text-indigo-400" /> What Sets Us Apart
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-lg mt-1">
                      <Check className="w-4 h-4 text-indigo-400" />
                    </div>
                    <p className="text-white/70 text-sm">
                      <span className="font-semibold text-white/90">Exam-focused solutions</span> - Tailored specifically for JEE, NEET, and BITSAT patterns
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-lg mt-1">
                      <Check className="w-4 h-4 text-indigo-400" />
                    </div>
                    <p className="text-white/70 text-sm">
                      <span className="font-semibold text-white/90">Conceptual clarity</span> - Focus on understanding rather than just memorizing formulas
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-lg mt-1">
                      <Check className="w-4 h-4 text-indigo-400" />
                    </div>
                    <p className="text-white/70 text-sm">
                      <span className="font-semibold text-white/90">24/7 availability</span> - Get help whenever you need it, no scheduling required
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-4 text-white/90 flex items-center gap-3">
                  <Zap className="text-indigo-400" /> Enhanced Learning
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-lg mt-1">
                      <Check className="w-4 h-4 text-indigo-400" />
                    </div>
                    <p className="text-white/70 text-sm">
                      <span className="font-semibold text-white/90">Multiple approaches</span> - Learn different methods to solve the same problem
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-lg mt-1">
                      <Check className="w-4 h-4 text-indigo-400" />
                    </div>
                    <p className="text-white/70 text-sm">
                      <span className="font-semibold text-white/90">Visual learning</span> - Diagrams and visual explanations for complex concepts
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-indigo-500/20 p-2 rounded-lg mt-1">
                      <Check className="w-4 h-4 text-indigo-400" />
                    </div>
                    <p className="text-white/70 text-sm">
                      <span className="font-semibold text-white/90">Practice recommendations</span> - Get suggested problems to reinforce your learning
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Example preview area */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="max-w-4xl mx-auto"
        >
          <div className="relative p-1 rounded-2xl overflow-hidden backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-purple-500/10 to-indigo-500/20 opacity-70 animate-pulse" style={{ animationDuration: '4s' }}></div>

            <div className="relative rounded-2xl overflow-hidden border border-white/10 shadow-xl">
              <img
                src="/feature-preview.png"
                alt="IsotopeAI in action"
                className="w-full h-auto rounded-2xl"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />

              {/* Feature annotation badges */}
              <div className="absolute top-6 right-6 bg-black/80 text-indigo-400 text-xs font-medium px-3 py-1 rounded-full shadow-lg border border-indigo-500/30 backdrop-blur-md">
                Detailed Explanations
              </div>
              <div className="absolute bottom-24 left-6 bg-black/80 text-indigo-400 text-xs font-medium px-3 py-1 rounded-full shadow-lg border border-indigo-500/30 backdrop-blur-md">
                Step-by-Step Solutions
              </div>
              <div className="absolute bottom-12 right-8 bg-black/80 text-indigo-400 text-xs font-medium px-3 py-1 rounded-full shadow-lg border border-indigo-500/30 backdrop-blur-md">
                Formula Explanation
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// Step Card Component
const StepCard = ({
  number,
  icon,
  title,
  description,
  delay,
  gradient
}: {
  number: number;
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
  gradient: string;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay }}
      className="group"
    >
      <div className="bg-[#0d0d35]/40 backdrop-blur-sm rounded-2xl p-8 border border-white/5 shadow-xl relative z-10 h-full transition-all duration-500 hover:border-indigo-500/30 hover:bg-[#0d0d35]/60">
        <div className="absolute -top-8 left-1/2 -translate-x-1/2 bg-gradient-to-br from-indigo-600 to-purple-600 text-white w-16 h-16 rounded-2xl flex items-center justify-center font-bold text-xl shadow-lg shadow-indigo-500/20 z-20 md:flex group-hover:scale-110 transition-transform duration-300">
          {number}
        </div>

        <div className="mt-8 mb-6 flex items-start gap-4">
          <div className={`bg-gradient-to-br ${gradient} p-4 rounded-xl relative border border-white/10 shrink-0`}>
            <motion.div
              animate={{
                scale: [1, 1.05, 1],
                rotate: [0, 3, 0, -3, 0]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                repeatType: "loop"
              }}
              className="text-white"
            >
              {icon}
            </motion.div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-white/90">{title}</h3>
          </div>
        </div>

        <p className="text-white/60 mb-6">
          {description}
        </p>

        <div className="bg-[#080820]/60 p-4 rounded-xl border border-white/5">
          <motion.div
            initial={{ width: 0 }}
            whileInView={{ width: "100%" }}
            viewport={{ once: true }}
            transition={{ duration: 1.5, delay: delay + 0.5 }}
            className="h-1 bg-gradient-to-r from-indigo-500/60 to-purple-500/20 rounded-full"
          ></motion.div>
        </div>
      </div>
    </motion.div>
  );
};

const CTASection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: FirebaseUser | null }) => {
  return (
    <section className="relative py-28 overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
      <div className="absolute -top-40 left-1/4 w-80 h-80 bg-indigo-600/10 rounded-full blur-[120px] opacity-50"></div>
      <div className="absolute -bottom-40 right-1/4 w-80 h-80 bg-purple-600/10 rounded-full blur-[120px] opacity-50"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="relative"
        >
          <div className="absolute -inset-1 bg-gradient-to-r from-indigo-600/20 via-purple-600/10 to-indigo-600/20 blur-xl opacity-70 rounded-3xl"></div>
          <div className="bg-[#0d0d35]/60 rounded-3xl p-14 backdrop-blur-lg border border-white/5 relative overflow-hidden shadow-xl">
            {/* Animated subtle background pattern */}
            <motion.div
              className="absolute inset-0 opacity-5"
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%'],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
              style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.4"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
              }}
            ></motion.div>

            <div className="text-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
                className="bg-gradient-to-br from-indigo-500/30 to-purple-600/30 p-4 rounded-xl inline-block mb-6 border border-white/5"
              >
                <Sparkles className="w-8 h-8 text-indigo-400" />
              </motion.div>

              <motion.h2
                className="text-4xl md:text-5xl font-bold mb-6 text-white leading-tight"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Ready to Excel in Your{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
                  Studies?
                </span>
              </motion.h2>

              <motion.p
                className="text-xl text-white/70 mb-10 max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                Join thousands of students already using our AI assistant to master Physics, Chemistry, and Mathematics
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col sm:flex-row items-center justify-center gap-6"
              >
                {user ? (
                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-xl px-8 py-6 h-auto text-lg shadow-lg shadow-indigo-600/20 font-medium transition-all duration-300"
                      onClick={handleGetStarted}
                    >
                      Go to AI Assistant <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </motion.div>
                ) : (
                  <div className="w-full sm:w-auto flex flex-col items-center">
                    <SignIn />
                    <div className="mt-4 flex items-center justify-center gap-2 text-white/40 text-sm">
                      <Check className="w-4 h-4 text-emerald-400" /> No credit card required
                    </div>
                  </div>
                )}
              </motion.div>
            </div>

            {/* Decorative floating elements */}
            <motion.div
              className="absolute top-10 left-10 w-20 h-20 border border-white/10 rounded-full"
              animate={{
                y: [0, 10, 0],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute bottom-10 right-10 w-16 h-16 border border-white/10 rounded-full"
              animate={{
                y: [0, -10, 0],
                opacity: [0.1, 0.2, 0.1],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1,
              }}
            />
            <motion.div
              className="absolute top-1/2 right-[15%] w-6 h-6 bg-indigo-500/40 rounded-full blur-sm"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </div>
        </motion.div>

        {/* Trust indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <p className="text-white/50 text-sm mb-6">TRUSTED BY STUDENTS FROM</p>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16">
            <UniversityLogo name="IIT Delhi" />
            <UniversityLogo name="IIT Bombay" />
            <UniversityLogo name="AIIMS" />
            <UniversityLogo name="BITS Pilani" />
            <UniversityLogo name="NIT Trichy" />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

// University Logo Component
const UniversityLogo = ({ name }: { name: string }) => {
  return (
    <motion.div
      className="text-white/40 hover:text-white/70 transition-colors duration-300 text-sm font-medium relative group"
      whileHover={{ scale: 1.05 }}
    >
      <span className="relative">
        {name}
        <motion.span
          className="absolute -bottom-1 left-0 right-0 h-px bg-white/20 origin-left"
          initial={{ scaleX: 0 }}
          whileHover={{ scaleX: 1 }}
          transition={{ duration: 0.3 }}
        />
      </span>
    </motion.div>
  );
};

const AnimatedChatDemo = () => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [typing, setTyping] = useState(false);
  const [autoPlay, setAutoPlay] = useState(true);

  const conversation = [
    {
      question: "How do I solve quadratic equations?",
      answer: "Let me help you with quadratic equations! Here's a step-by-step approach:\n\n1. Standard form: ax² + bx + c = 0\n2. Use quadratic formula: x = (-b ± √(b² - 4ac)) / 2a\n3. Or factoring if possible\n4. Don't forget to check your solutions!"
    },
    {
      question: "Explain Newton's laws of motion",
      answer: "Newton's three laws of motion are fundamental principles:\n\n1. First Law (Inertia): An object stays at rest or in motion unless acted upon by a force\n2. Second Law (F = ma): Force equals mass times acceleration\n3. Third Law: For every action, there is an equal and opposite reaction"
    },
    {
      question: "What is the periodic table?",
      answer: "The periodic table is a systematic arrangement of chemical elements:\n\n• Organized by atomic number, electron configuration, and chemical properties\n• Elements in the same column (group) have similar properties\n• Helps predict how elements will interact\n• Essential tool for understanding chemistry"
    }
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (autoPlay) {
      interval = setInterval(() => {
        if (!typing) {
          setTyping(true);
          setTimeout(() => {
            setTyping(false);
            setCurrentMessageIndex((prev) => (prev + 1) % conversation.length);
          }, 2000);
        }
      }, 6000);
    }

    return () => clearInterval(interval);
  }, [typing, conversation.length, autoPlay]);

  return (
    <div className="relative">
      {/* Decorative elements */}
      <div className="absolute -inset-4 bg-gradient-to-br from-indigo-600/20 via-purple-600/20 to-indigo-600/20 blur-xl opacity-70 animate-pulse rounded-[2rem]"
        style={{ animationDuration: '8s' }} />

      <motion.div
        className="bg-[#080824]/40 backdrop-blur-lg rounded-[2rem] p-6 shadow-xl border border-white/10 overflow-hidden relative"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
      >
        {/* Glass morphism effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-20"></div>

        {/* Header */}
        <div className="flex items-center justify-between mb-6 border-b border-white/5 pb-4 relative z-10">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-br from-indigo-500/30 to-purple-600/30 p-2 rounded-xl">
              <Bot className="w-5 h-5 text-indigo-400" />
            </div>
            <div>
              <h3 className="font-medium text-white/90">IsotopeAI Assistant</h3>
              <div className="flex items-center gap-1.5">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <span className="text-white/40 text-xs">Online</span>
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500/80" />
            <div className="w-3 h-3 rounded-full bg-yellow-500/80" />
            <div className="w-3 h-3 rounded-full bg-green-500/80" />
          </div>
        </div>

        {/* Chat area with proper scrollbar styling */}
        <div className="space-y-6 h-[380px] overflow-y-auto p-2 pb-6 relative z-10 custom-scrollbar">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentMessageIndex}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-4"
            >
              {/* User message */}
              <div className="flex items-start gap-3">
                <div className="bg-gradient-to-br from-indigo-600/30 to-indigo-800/30 p-2 rounded-xl mt-1 border border-white/5">
                  <User className="w-4 h-4 text-indigo-400" />
                </div>
                <div className="bg-gradient-to-br from-indigo-600/10 to-indigo-800/10 rounded-2xl p-4 rounded-tl-none flex-1 backdrop-blur-sm text-white/90 border border-white/5">
                  <p className="font-medium text-white">{conversation[currentMessageIndex].question}</p>
                </div>
              </div>

              {/* Bot message with improved styling */}
              <div className="flex items-start gap-3">
                <div className="bg-gradient-to-br from-purple-600/30 to-indigo-600/30 p-2 rounded-xl mt-1 border border-white/5">
                  <Bot className="w-4 h-4 text-indigo-400" />
                </div>
                <div className="bg-[#0d0d35] border border-white/5 rounded-2xl p-4 rounded-tl-none flex-1 text-white/80 shadow-inner shadow-white/5">
                  {typing ? (
                    <div className="flex gap-1.5 items-center">
                      <motion.div
                        className="w-2 h-2 bg-indigo-500 rounded-full"
                        animate={{ y: [0, -6, 0] }}
                        transition={{ duration: 0.5, repeat: Infinity, repeatType: "loop" }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-purple-500 rounded-full"
                        animate={{ y: [0, -6, 0] }}
                        transition={{ duration: 0.5, repeat: Infinity, repeatType: "loop", delay: 0.1 }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-indigo-500 rounded-full"
                        animate={{ y: [0, -6, 0] }}
                        transition={{ duration: 0.5, repeat: Infinity, repeatType: "loop", delay: 0.2 }}
                      />
                    </div>
                  ) : (
                    <p className="whitespace-pre-line">{conversation[currentMessageIndex].answer}</p>
                  )}
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Fake input field */}
        <div className="relative mt-4 z-10">
          <div className="flex items-center border border-white/10 bg-[#0d0d35]/70 rounded-xl p-3 shadow-inner shadow-white/5">
            <input
              type="text"
              className="bg-transparent w-full focus:outline-none text-white/70 placeholder:text-white/40"
              placeholder="Ask any PCMB question..."
              disabled
            />
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 rounded-xl bg-gradient-to-r from-indigo-600 to-purple-600 cursor-pointer ml-2 shadow-md shadow-indigo-600/20"
            >
              <ArrowRight className="w-4 h-4 text-white" />
            </motion.div>
          </div>
        </div>

        {/* Gradient overlay for fade effect */}
        <div className="absolute bottom-[72px] left-0 right-0 h-12 bg-gradient-to-t from-[#080824]/90 to-transparent pointer-events-none"></div>
      </motion.div>

      {/* Decorative elements */}
      <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-purple-500/20 rounded-full blur-3xl"></div>
      <div className="absolute -top-4 -left-4 w-20 h-20 bg-indigo-500/20 rounded-full blur-3xl"></div>

      {/* Tech pattern overlay for added depth */}
      <motion.div
        className="absolute inset-0 opacity-5 pointer-events-none mix-blend-screen z-20"
        animate={{
          backgroundPosition: ['0% 0%', '100% 100%'],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          repeatType: 'reverse',
        }}
        style={{
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.4"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        }}
      ></motion.div>

      {/* Add global style for scrollbar - fixed version */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
          }
        `
      }} />
    </div>
  );
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay,
  gradient
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
  gradient: string;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      className="relative group"
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-lg`}></div>
      <div className="bg-[#0d0d35]/40 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/5 relative h-full flex flex-col group-hover:border-indigo-500/20 transition-colors duration-300">
        <div className="flex items-start gap-4 mb-5">
          <div className={`bg-gradient-to-br ${gradient} p-4 rounded-xl relative border border-white/10 shrink-0`}>
            <motion.div
              animate={{ rotate: [0, 5, 0, -5, 0] }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-white"
            >
              {icon}
            </motion.div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-white/90">{title}</h3>
          </div>
        </div>

        <p className="text-white/60">{description}</p>

        {/* Bottom flourish */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-b-2xl`}></div>
      </div>
    </motion.div>
  );
};

const ResultBar = ({
  label,
  value,
  initialValue = 0,
  colorClass,
  delay
}: {
  label: string;
  value: number;
  initialValue?: number;
  colorClass: string;
  delay: number;
}) => {
  return (
    <div className="bg-[#080820]/70 p-5 rounded-xl border border-white/5 shadow-inner shadow-white/5">
      <div className="flex justify-between mb-2">
        <span className="text-white/70">{label}</span>
        <span className="font-medium text-white">{value}%</span>
      </div>
      <div className="h-2 bg-white/5 rounded-full overflow-hidden">
        <motion.div
          className={`h-full bg-gradient-to-r ${colorClass} rounded-full`}
          initial={{ width: `${initialValue}%` }}
          whileInView={{ width: `${value}%` }}
          viewport={{ once: true }}
          transition={{ duration: 1.5, delay }}
        ></motion.div>
      </div>
    </div>
  );
};

const StatCard = ({ value, label, delay }: { value: string; label: string; delay: number }) => {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="bg-[#0d0d35]/60 backdrop-blur-sm p-8 rounded-2xl border border-white/5 w-64 shadow-xl relative overflow-hidden group"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      <div className="relative">
        <h4 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400 mb-2">{value}</h4>
        <p className="text-base text-white/70">{label}</p>
      </div>
    </motion.div>
  );
};

export default AILanding;