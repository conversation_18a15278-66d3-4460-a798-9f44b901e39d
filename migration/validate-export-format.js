// Validation script to check if export format matches Supabase schema
// This script helps verify that the exported CSV data has the correct field names

const expectedSchemas = {
  users: [
    'id', 'uid', 'email', 'displayName', 'photoURL', 'username', 
    'created_at', 'lastLogin', 'updated_at', 'welcomeEmailSent',
    'backgroundImage', 'bio', 'location', 'stats', 'progress',
    'studySessions', 'mockTests'
  ],
  
  ai_chats: [
    'id', 'user_id', 'created_by', 'title', 'slug', 'messages',
    'created_at', 'updated_at', 'is_public', 'view_count', 'preview',
    'comments', 'is_pinned', 'is_starred', 'status', 'tags'
  ],
  
  groups: [
    'id', 'name', 'description', 'members', 'created_by', 'created_at',
    'is_public', 'invite_code'
  ],
  
  todos: [
    'id', 'title', 'description', 'priority', 'created_at', 'updated_at',
    'due_date', 'assigned_to', 'assigned_to_name', 'assigned_to_photo_url',
    'created_by', 'group_id', 'column_id'
  ],
  
  user_subjects: [
    'id', 'user_id', 'name', 'color', 'created_at'
  ],
  
  exams: [
    'id', 'user_id', 'name', 'date', 'total_marks', 'total_marks_obtained',
    'subject_marks', 'notes', 'created_at'
  ],
  
  study_sessions: [
    'id', 'user_id', 'subject', 'duration', 'mode', 'phase', 'completed',
    'start_time', 'end_time', 'notes', 'created_at'
  ],
  
  mock_tests: [
    'id', 'user_id', 'name', 'test_date', 'subject_marks',
    'total_marks_obtained', 'total_marks', 'notes', 'created_at'
  ],
  
  chat_comments: [
    'id', 'chat_id', 'author_name', 'author_id', 'author_username',
    'author_photo_url', 'content', 'parent_id', 'created_at', 'updated_at'
  ],
  
  messages: [
    'id', 'group_id', 'sender_id', 'sender_name', 'content',
    'message_type', 'metadata', 'created_at', 'updated_at'
  ]
};

function validateCSVHeaders(csvContent, expectedSchema, tableName) {
  const lines = csvContent.split('\n');
  if (lines.length === 0) {
    console.error(`❌ ${tableName}: CSV is empty`);
    return false;
  }
  
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const missing = expectedSchema.filter(field => !headers.includes(field));
  const extra = headers.filter(field => !expectedSchema.includes(field));
  
  if (missing.length === 0 && extra.length === 0) {
    console.log(`✅ ${tableName}: All fields match perfectly`);
    return true;
  }
  
  if (missing.length > 0) {
    console.warn(`⚠️  ${tableName}: Missing fields: ${missing.join(', ')}`);
  }
  
  if (extra.length > 0) {
    console.warn(`⚠️  ${tableName}: Extra fields: ${extra.join(', ')}`);
  }
  
  console.log(`📋 ${tableName}: Expected: ${expectedSchema.join(', ')}`);
  console.log(`📋 ${tableName}: Actual: ${headers.join(', ')}`);
  
  return missing.length === 0; // Only fail if missing required fields
}

// Example usage:
// const csvContent = "id,user_id,title,created_at\n1,user123,Test Chat,2024-01-01T00:00:00Z";
// validateCSVHeaders(csvContent, expectedSchemas.ai_chats, 'ai_chats');

console.log('🔍 Export Format Validation Schema');
console.log('Use this script to validate your exported CSV files match Supabase schema');
console.log('');
console.log('Expected schemas:');
Object.entries(expectedSchemas).forEach(([table, fields]) => {
  console.log(`${table}: ${fields.join(', ')}`);
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { expectedSchemas, validateCSVHeaders };
}
