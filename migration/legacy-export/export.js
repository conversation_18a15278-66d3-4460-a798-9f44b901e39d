// Firebase configuration - You need to replace these with your actual Firebase config values
// Get these from your Firebase project settings
const firebaseConfig = {
    apiKey: "AIzaSyA2odde622bEF93tx3KcwBymHVUMdvXnjk", // Replace with your actual API key
    authDomain: "doubtgpt.firebaseapp.com", // Replace with your actual auth domain
    projectId: "doubtgpt", // Replace with your actual project ID
    storageBucket: "doubtgpt.firebasestorage.app", // Replace with your actual storage bucket
    messagingSenderId: "120215440192", // Replace with your actual sender ID
    appId: "1:120215440192:web:aeb308ad5df2b659bd48a5" // Replace with your actual app ID
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore();

let currentUser = null;
const ADMIN_EMAIL = '<EMAIL>';

// Check if current user is admin
function isAdmin() {
    return currentUser && currentUser.email === ADMIN_EMAIL;
}

// Authentication
document.getElementById('signInBtn').addEventListener('click', signIn);

async function signIn() {
    try {
        const provider = new firebase.auth.GoogleAuthProvider();
        provider.addScope('https://www.googleapis.com/auth/userinfo.email');
        provider.addScope('https://www.googleapis.com/auth/userinfo.profile');
        
        const result = await auth.signInWithPopup(provider);
        currentUser = result.user;
        
        showAuthStatus('Successfully signed in!', 'success');
        showExportSection();
    } catch (error) {
        console.error('Sign in error:', error);
        showAuthStatus('Sign in failed: ' + error.message, 'error');
    }
}

function showAuthStatus(message, type) {
    const statusEl = document.getElementById('authStatus');
    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = 'block';
}

function showExportSection() {
    document.getElementById('authSection').style.display = 'none';
    document.getElementById('exportSection').classList.add('active');

    // Show user info
    const userInfoEl = document.getElementById('userInfo');
    const adminBadge = isAdmin() ? '<span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin-left: 10px;">ADMIN</span>' : '';
    userInfoEl.innerHTML = `
        <strong>Signed in as:</strong> ${currentUser.displayName || currentUser.email}${adminBadge}<br>
        <strong>Email:</strong> ${currentUser.email}<br>
        <strong>User ID:</strong> ${currentUser.uid}
        ${isAdmin() ? '<br><strong>Admin Mode:</strong> Can export all user data' : ''}
    `;

    // Show admin export options if user is admin
    if (isAdmin()) {
        showAdminExportOptions();
    }
}

function showAdminExportOptions() {
    const exportSection = document.getElementById('exportSection');

    // Add admin export section
    const adminSection = document.createElement('div');
    adminSection.innerHTML = `
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <h4 style="color: #856404; margin-bottom: 10px;">🔧 Admin Export Options</h4>
            <p style="color: #856404; margin-bottom: 15px;">As an admin, you can export data from all users:</p>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Users Data</h3>
                    <p>Export all user profiles and account information</p>
                    <div id="allUsersProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allUsersStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allUsers')">Export All Users</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All AI Chats</h3>
                    <p>Export all AI conversations from all users</p>
                    <div id="allAIChatsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allAIChatsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allAIChats')">Export All Chats</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Groups</h3>
                    <p>Export all groups and messages from all users</p>
                    <div id="allGroupsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allGroupsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allGroups')">Export All Groups</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Todos</h3>
                    <p>Export all todos and tasks from all users</p>
                    <div id="allTodosProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allTodosStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allTodos')">Export All Todos</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All User Subjects</h3>
                    <p>Export all custom subjects from all users</p>
                    <div id="allSubjectsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allSubjectsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allSubjects')">Export All Subjects</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Exams</h3>
                    <p>Export all exam countdowns from all users</p>
                    <div id="allExamsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allExamsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allExams')">Export All Exams</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Study Sessions</h3>
                    <p>Export all study sessions from all users</p>
                    <div id="allStudySessionsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allStudySessionsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allStudySessions')">Export All Study Sessions</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Mock Tests</h3>
                    <p>Export all mock tests from all users</p>
                    <div id="allMockTestsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allMockTestsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allMockTests')">Export All Mock Tests</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Chat Comments</h3>
                    <p>Export all comments from AI chats</p>
                    <div id="allChatCommentsProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allChatCommentsStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allChatComments')">Export All Comments</button>
            </div>

            <div class="collection-item">
                <div class="collection-info">
                    <h3>All Messages</h3>
                    <p>Export all group messages separately</p>
                    <div id="allMessagesProgress" class="progress" style="display: none;">
                        <div class="progress-bar"></div>
                    </div>
                    <div id="allMessagesStatus" class="status" style="display: none;"></div>
                </div>
                <button class="btn export-btn" onclick="exportCollection('allMessages')">Export All Messages</button>
            </div>

            <button class="btn export-all-btn" onclick="exportAllAdminData()" style="background: #6f42c1;">Export ALL Collections</button>
        </div>
    `;

    // Insert admin section before the regular export section
    const firstCollectionItem = exportSection.querySelector('.collection-item');
    exportSection.insertBefore(adminSection, firstCollectionItem);
}

// Export functions
async function exportCollection(collectionType) {
    if (!currentUser) {
        alert('Please sign in first');
        return;
    }
    
    const progressEl = document.getElementById(`${collectionType}Progress`);
    const statusEl = document.getElementById(`${collectionType}Status`);
    const progressBar = progressEl.querySelector('.progress-bar');
    
    progressEl.style.display = 'block';
    statusEl.style.display = 'none';
    progressBar.style.width = '0%';
    
    try {
        let data = [];
        let filename = '';
        
        switch (collectionType) {
            case 'aiChats':
                data = await exportAIChats();
                filename = 'isotope-ai-chats.csv';
                break;
            case 'groups':
                data = await exportGroups();
                filename = 'isotope-groups-messages.csv';
                break;
            case 'todos':
                data = await exportTodos();
                filename = 'isotope-todos.csv';
                break;
            case 'subjects':
                data = await exportSubjects();
                filename = 'isotope-subjects.csv';
                break;
            case 'exams':
                data = await exportExams();
                filename = 'isotope-exams.csv';
                break;
            case 'userData':
                data = await exportUserData();
                filename = 'isotope-user-data.csv';
                break;
            // Admin-only exports
            case 'allUsers':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllUsers();
                filename = 'isotope-all-users.csv';
                break;
            case 'allAIChats':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllAIChats();
                filename = 'isotope-all-ai-chats.csv';
                break;
            case 'allGroups':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllGroups();
                filename = 'isotope-all-groups.csv';
                break;
            case 'allTodos':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllTodos();
                filename = 'isotope-all-todos.csv';
                break;
            case 'allSubjects':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllSubjects();
                filename = 'isotope-all-subjects.csv';
                break;
            case 'allExams':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllExams();
                filename = 'isotope-all-exams.csv';
                break;
            case 'allStudySessions':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllStudySessions();
                filename = 'isotope-all-study-sessions.csv';
                break;
            case 'allMockTests':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllMockTests();
                filename = 'isotope-all-mock-tests.csv';
                break;
            case 'allChatComments':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllChatComments();
                filename = 'isotope-all-chat-comments.csv';
                break;
            case 'allMessages':
                if (!isAdmin()) {
                    throw new Error('Admin access required');
                }
                data = await exportAllMessages();
                filename = 'isotope-all-messages.csv';
                break;
            case 'studySessionsTable':
                data = await exportStudySessionsTable();
                filename = 'isotope-study-sessions-table.csv';
                break;
            case 'mockTestsTable':
                data = await exportMockTestsTable();
                filename = 'isotope-mock-tests-table.csv';
                break;
        }
        
        progressBar.style.width = '100%';
        
        if (data.length > 0) {
            downloadCSV(data, filename);
            showStatus(statusEl, `Successfully exported ${data.length} records`, 'success');
        } else {
            showStatus(statusEl, 'No data found to export', 'info');
        }
        
    } catch (error) {
        console.error(`Export error for ${collectionType}:`, error);
        showStatus(statusEl, `Export failed: ${error.message}`, 'error');
    }
}

async function exportAIChats() {
    const chatsRef = db.collection('aiChats');
    const query = chatsRef.where('userId', '==', currentUser.uid);
    const snapshot = await query.get();

    const data = [];
    snapshot.forEach(doc => {
        const chatData = doc.data();
        data.push({
            id: doc.id,
            user_id: chatData.userId, // Changed from userId to user_id
            created_by: chatData.userId, // Changed from createdBy to created_by
            title: chatData.title || '',
            slug: chatData.slug || '',
            messages: JSON.stringify(chatData.messages || []),
            created_at: formatTimestamp(chatData.createdAt), // Changed from createdAt to created_at
            updated_at: formatTimestamp(chatData.updatedAt), // Changed from updatedAt to updated_at
            is_public: chatData.isPublic || false, // Changed from isPublic to is_public
            view_count: chatData.viewCount || 0, // Changed from viewCount to view_count
            preview: chatData.preview || '',
            comments: JSON.stringify(chatData.comments || []),
            is_pinned: chatData.isPinned || false, // Changed from isPinned to is_pinned
            is_starred: chatData.isStarred || false, // Changed from isStarred to is_starred
            status: chatData.status || 'approved',
            tags: JSON.stringify(chatData.tags || [])
        });
    });

    return data;
}

async function exportGroups() {
    const groupsRef = db.collection('groups');
    const query = groupsRef.where('members', 'array-contains', currentUser.uid);
    const snapshot = await query.get();
    
    const data = [];
    
    for (const doc of snapshot.docs) {
        const groupData = doc.data();
        
        // Get messages for this group
        const messagesRef = db.collection(`groups/${doc.id}/messages`);
        const messagesSnapshot = await messagesRef.orderBy('timestamp', 'asc').get();
        
        const messages = [];
        messagesSnapshot.forEach(msgDoc => {
            const msgData = msgDoc.data();
            messages.push({
                id: msgDoc.id,
                content: msgData.content,
                senderId: msgData.senderId,
                timestamp: formatTimestamp(msgData.timestamp)
            });
        });
        
        data.push({
            id: doc.id,
            name: groupData.name,
            description: groupData.description || '',
            members: JSON.stringify(groupData.members || []),
            created_by: groupData.ownerId || groupData.createdBy, // Changed from createdBy to created_by
            created_at: formatTimestamp(groupData.createdAt), // Changed from createdAt to created_at
            is_public: groupData.isPublic || false, // Changed from isPublic to is_public
            invite_code: groupData.inviteCode || '' // Changed from inviteCode to invite_code
        });
    }
    
    return data;
}

async function exportTodos() {
    const todosRef = db.collection('todos');
    const query = todosRef.where('createdBy', '==', currentUser.uid);
    const snapshot = await query.get();
    
    const data = [];
    snapshot.forEach(doc => {
        const todoData = doc.data();
        data.push({
            id: doc.id,
            title: todoData.title,
            description: todoData.description || '',
            priority: todoData.priority || 'medium',
            created_at: formatTimestamp(todoData.createdAt), // Changed from createdAt to created_at and format timestamp
            updated_at: formatTimestamp(todoData.updatedAt), // Changed from updatedAt to updated_at and format timestamp
            due_date: todoData.dueDate || null, // Changed from dueDate to due_date
            assigned_to: todoData.assignedTo || null, // Changed from assignedTo to assigned_to
            assigned_to_name: todoData.assignedToName || null, // Changed from assignedToName to assigned_to_name
            assigned_to_photo_url: todoData.assignedToPhotoUrl || null, // Changed from assignedToPhotoUrl to assigned_to_photo_url
            created_by: todoData.createdBy, // Changed from createdBy to created_by
            group_id: todoData.groupId || null, // Changed from groupId to group_id
            column_id: todoData.columnId || 'column-1' // Changed from columnId to column_id
        });
    });
    
    return data;
}

async function exportSubjects() {
    const subjectsRef = db.collection('userSubjects').doc(currentUser.uid);
    const doc = await subjectsRef.get();

    const data = [];
    if (doc.exists) { // Fixed: removed parentheses for v8 compat
        const subjectsData = doc.data();
        const subjects = subjectsData.subjects || [];

        subjects.forEach(subject => {
            data.push({
                id: subject.id,
                user_id: currentUser.uid, // Changed from userId to user_id
                name: subject.name,
                color: subject.color || '#000000',
                created_at: formatTimestamp(subject.createdAt) // Changed from createdAt to created_at
            });
        });
    }

    return data;
}

async function exportExams() {
    const examsRef = db.collection('exams');
    const query = examsRef.where('userId', '==', currentUser.uid);
    const snapshot = await query.get();
    
    const data = [];
    snapshot.forEach(doc => {
        const examData = doc.data();
        data.push({
            id: doc.id,
            user_id: examData.userId, // Changed from userId to user_id
            name: examData.name,
            date: examData.date,
            total_marks: safeParseInt(examData.totalMarks), // Changed from totalMarks to total_marks
            total_marks_obtained: safeParseInt(examData.totalMarksObtained), // Changed from totalMarksObtained to total_marks_obtained
            subject_marks: JSON.stringify(examData.subjectMarks || []), // Changed from subjectMarks to subject_marks
            notes: examData.notes || '',
            created_at: formatTimestamp(examData.createdAt) // Changed from createdAt to created_at
        });
    });
    
    return data;
}

async function exportUserData() {
    const userRef = db.collection('users').doc(currentUser.uid);
    const doc = await userRef.get();

    const data = [];
    if (doc.exists) { // Fixed: removed parentheses for v8 compat
        const userData = doc.data();

        // Export study sessions with correct field names for Supabase
        const studySessions = userData.studySessions || {};
        Object.entries(studySessions).forEach(([sessionId, session]) => {
            data.push({
                type: 'study_session',
                id: sessionId,
                user_id: currentUser.uid, // Changed from userId to user_id
                subject: session.subject || '',
                duration: safeParseInt(session.duration),
                mode: session.mode || 'stopwatch',
                phase: session.phase || 'work',
                completed: session.completed || false,
                start_time: formatTimestamp(session.startTime), // Changed from startTime to start_time
                end_time: formatTimestamp(session.endTime) || null, // Changed from endTime to end_time
                notes: session.notes || '',
                created_at: formatTimestamp(session.startTime) // Add created_at field
            });
        });

        // Export mock tests with correct field names for Supabase
        const mockTests = userData.mockTests || {};
        Object.entries(mockTests).forEach(([testId, test]) => {
            data.push({
                type: 'mock_test',
                id: testId,
                user_id: currentUser.uid, // Changed from userId to user_id
                name: test.name,
                test_date: test.date, // Changed from date to test_date
                subject_marks: JSON.stringify(test.subjectMarks || []), // Changed from subjectMarks to subject_marks
                total_marks_obtained: safeParseInt(test.totalMarksObtained), // Changed from totalMarksObtained to total_marks_obtained
                total_marks: safeParseInt(test.totalMarks), // Changed from totalMarks to total_marks
                notes: test.notes || '',
                created_at: formatTimestamp(test.createdAt) // Changed from createdAt to created_at
            });
        });

        // Export user profile with correct field names for Supabase users table
        data.push({
            type: 'user_profile',
            id: currentUser.uid,
            uid: currentUser.uid,
            email: userData.email || currentUser.email,
            displayName: userData.displayName || currentUser.displayName, // Keep as displayName (matches Supabase)
            photoURL: userData.photoURL || currentUser.photoURL, // Keep as photoURL (matches Supabase)
            username: userData.username || '',
            created_at: formatTimestamp(userData.createdAt),
            lastLogin: formatTimestamp(userData.lastLogin), // Keep as lastLogin (matches Supabase)
            updated_at: formatTimestamp(userData.updatedAt || new Date()),
            welcomeEmailSent: userData.welcomeEmailSent || false, // Keep as welcomeEmailSent (matches Supabase)
            backgroundImage: userData.backgroundImage || '', // Keep as backgroundImage (matches Supabase)
            bio: userData.bio || '',
            location: userData.location || '',
            stats: JSON.stringify(userData.stats || {}),
            progress: JSON.stringify(userData.progress || {}),
            studySessions: JSON.stringify(userData.studySessions || {}), // Keep as studySessions (matches Supabase)
            mockTests: JSON.stringify(userData.mockTests || {}) // Keep as mockTests (matches Supabase)
        });
    }

    return data;
}

// Utility functions
function formatTimestamp(timestamp) {
    if (!timestamp) return '';

    // Handle Firebase Timestamp objects
    if (timestamp.toDate) {
        return timestamp.toDate().toISOString();
    }

    // Handle Firebase Timestamp serialized objects
    if (timestamp.seconds && typeof timestamp.seconds === 'number') {
        return new Date(timestamp.seconds * 1000).toISOString();
    }

    // Handle Firebase Timestamp serialized objects (older format)
    if (timestamp._seconds && typeof timestamp._seconds === 'number') {
        return new Date(timestamp._seconds * 1000).toISOString();
    }

    // Handle numeric timestamps
    if (typeof timestamp === 'number') {
        return new Date(timestamp).toISOString();
    }

    // Handle string timestamps - ensure they're in proper ISO format
    if (typeof timestamp === 'string') {
        const date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
            return date.toISOString();
        }
        return '';
    }

    // Handle structured date objects
    if (typeof timestamp === 'object' && timestamp !== null) {
        if (timestamp.day && timestamp.month && timestamp.year) {
            const date = new Date(timestamp.year, timestamp.month - 1, timestamp.day);
            return date.toISOString();
        }
        if (timestamp.seconds && timestamp.nanoseconds) {
            return new Date(timestamp.seconds * 1000).toISOString();
        }
    }

    return '';
}

// Helper function to safely parse integers
function safeParseInt(value) {
    if (value === null || value === undefined || value === '') {
        return 0;
    }
    const parsed = parseInt(value);
    return isNaN(parsed) ? 0 : parsed;
}

function showStatus(statusEl, message, type) {
    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = 'block';
}

function downloadCSV(data, filename) {
    if (data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => 
            headers.map(header => {
                const value = row[header] || '';
                // Escape quotes and wrap in quotes if contains comma or quote
                if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                    return '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            }).join(',')
        )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

async function exportAllData() {
    const collections = ['aiChats', 'groups', 'todos', 'subjects', 'exams', 'userData'];

    for (const collection of collections) {
        await exportCollection(collection);
        // Add a small delay between exports
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    alert('All data exported successfully! Please check your downloads folder.');
}

async function exportAllAdminData() {
    if (!isAdmin()) {
        alert('Admin access required');
        return;
    }

    const adminCollections = [
        'allUsers', 'allAIChats', 'allGroups', 'allTodos',
        'allSubjects', 'allExams', 'allStudySessions',
        'allMockTests', 'allChatComments', 'allMessages'
    ];

    for (const collection of adminCollections) {
        await exportCollection(collection);
        // Add a small delay between exports
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    alert('All admin data exported successfully! Please check your downloads folder.');
}

// Admin export functions
async function exportAllUsers() {
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const userData = doc.data();
        data.push({
            id: doc.id,
            uid: doc.id,
            email: userData.email || '',
            displayName: userData.displayName || '', // Keep as displayName (matches Supabase)
            photoURL: userData.photoURL || '', // Keep as photoURL (matches Supabase)
            username: userData.username || '',
            created_at: formatTimestamp(userData.createdAt),
            lastLogin: formatTimestamp(userData.lastLogin), // Keep as lastLogin (matches Supabase)
            updated_at: formatTimestamp(userData.updatedAt || new Date()),
            welcomeEmailSent: userData.welcomeEmailSent || false, // Keep as welcomeEmailSent (matches Supabase)
            backgroundImage: userData.backgroundImage || '', // Keep as backgroundImage (matches Supabase)
            bio: userData.bio || '',
            location: userData.location || '',
            stats: JSON.stringify(userData.stats || {}),
            progress: JSON.stringify(userData.progress || {}),
            studySessions: JSON.stringify(userData.studySessions || {}), // Keep as studySessions (matches Supabase)
            mockTests: JSON.stringify(userData.mockTests || {}) // Keep as mockTests (matches Supabase)
        });
    });

    return data;
}

async function exportAllAIChats() {
    const chatsRef = db.collection('aiChats');
    const snapshot = await chatsRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const chatData = doc.data();
        data.push({
            id: doc.id,
            user_id: chatData.userId, // Changed from userId to user_id
            created_by: chatData.userId, // Changed from createdBy to created_by
            title: chatData.title || '',
            slug: chatData.slug || '',
            messages: JSON.stringify(chatData.messages || []),
            created_at: formatTimestamp(chatData.createdAt), // Changed from createdAt to created_at
            updated_at: formatTimestamp(chatData.updatedAt), // Changed from updatedAt to updated_at
            is_public: chatData.isPublic || false, // Changed from isPublic to is_public
            view_count: chatData.viewCount || 0, // Changed from viewCount to view_count
            preview: chatData.preview || '',
            comments: JSON.stringify(chatData.comments || []),
            is_pinned: chatData.isPinned || false, // Changed from isPinned to is_pinned
            is_starred: chatData.isStarred || false, // Changed from isStarred to is_starred
            status: chatData.status || 'approved',
            tags: JSON.stringify(chatData.tags || [])
        });
    });

    return data;
}

async function exportAllGroups() {
    const groupsRef = db.collection('groups');
    const snapshot = await groupsRef.get();

    const data = [];

    for (const doc of snapshot.docs) {
        const groupData = doc.data();

        // Get messages for this group
        try {
            const messagesRef = db.collection(`groups/${doc.id}/messages`);
            const messagesSnapshot = await messagesRef.orderBy('timestamp', 'asc').get();

            const messages = [];
            messagesSnapshot.forEach(msgDoc => {
                const msgData = msgDoc.data();
                messages.push({
                    id: msgDoc.id,
                    content: msgData.content,
                    senderId: msgData.senderId,
                    timestamp: formatTimestamp(msgData.timestamp)
                });
            });

            data.push({
                id: doc.id,
                name: groupData.name,
                description: groupData.description || '',
                members: JSON.stringify(groupData.members || []),
                created_by: groupData.ownerId || groupData.createdBy, // Changed from createdBy to created_by
                created_at: formatTimestamp(groupData.createdAt), // Changed from createdAt to created_at
                is_public: groupData.isPublic || false, // Changed from isPublic to is_public
                invite_code: groupData.inviteCode || '' // Changed from inviteCode to invite_code
            });
        } catch (error) {
            console.warn(`Could not fetch messages for group ${doc.id}:`, error);
            data.push({
                id: doc.id,
                name: groupData.name,
                description: groupData.description || '',
                members: JSON.stringify(groupData.members || []),
                created_by: groupData.ownerId || groupData.createdBy, // Changed from createdBy to created_by
                created_at: formatTimestamp(groupData.createdAt), // Changed from createdAt to created_at
                is_public: groupData.isPublic || false, // Changed from isPublic to is_public
                invite_code: groupData.inviteCode || '' // Changed from inviteCode to invite_code
            });
        }
    }

    return data;
}

async function exportAllTodos() {
    const todosRef = db.collection('todos');
    const snapshot = await todosRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const todoData = doc.data();
        data.push({
            id: doc.id,
            title: todoData.title,
            description: todoData.description || '',
            priority: todoData.priority || 'medium',
            created_at: formatTimestamp(todoData.createdAt), // Changed from createdAt to created_at and format timestamp
            updated_at: formatTimestamp(todoData.updatedAt), // Changed from updatedAt to updated_at and format timestamp
            due_date: todoData.dueDate || null, // Changed from dueDate to due_date
            assigned_to: todoData.assignedTo || null, // Changed from assignedTo to assigned_to
            assigned_to_name: todoData.assignedToName || null, // Changed from assignedToName to assigned_to_name
            assigned_to_photo_url: todoData.assignedToPhotoUrl || null, // Changed from assignedToPhotoUrl to assigned_to_photo_url
            created_by: todoData.createdBy, // Changed from createdBy to created_by
            group_id: todoData.groupId || null, // Changed from groupId to group_id
            column_id: todoData.columnId || 'column-1' // Changed from columnId to column_id
        });
    });

    return data;
}

async function exportAllSubjects() {
    const usersRef = db.collection('userSubjects');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const subjectsData = doc.data();
        const subjects = subjectsData.subjects || [];

        subjects.forEach(subject => {
            data.push({
                id: subject.id,
                user_id: doc.id, // Changed from userId to user_id
                name: subject.name,
                color: subject.color || '#000000',
                created_at: formatTimestamp(subject.createdAt) // Changed from createdAt to created_at
            });
        });
    });

    return data;
}

async function exportAllExams() {
    const examsRef = db.collection('exams');
    const snapshot = await examsRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const examData = doc.data();
        data.push({
            id: doc.id,
            user_id: examData.userId, // Changed from userId to user_id
            name: examData.name,
            date: examData.date,
            total_marks: safeParseInt(examData.totalMarks), // Changed from totalMarks to total_marks
            total_marks_obtained: safeParseInt(examData.totalMarksObtained), // Changed from totalMarksObtained to total_marks_obtained
            subject_marks: JSON.stringify(examData.subjectMarks || []), // Changed from subjectMarks to subject_marks
            notes: examData.notes || '',
            created_at: formatTimestamp(examData.createdAt) // Changed from createdAt to created_at
        });
    });

    return data;
}

async function exportAllStudySessions() {
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const userData = doc.data();
        const studySessions = userData.studySessions || {};

        Object.entries(studySessions).forEach(([sessionId, session]) => {
            data.push({
                id: sessionId,
                user_id: doc.id, // Changed from userId to user_id
                subject: session.subject || '',
                duration: safeParseInt(session.duration),
                mode: session.mode || 'stopwatch',
                phase: session.phase || 'work',
                completed: session.completed || false,
                start_time: formatTimestamp(session.startTime), // Changed from startTime to start_time and format timestamp
                end_time: formatTimestamp(session.endTime) || null, // Changed from endTime to end_time and format timestamp
                notes: session.notes || '',
                created_at: formatTimestamp(session.startTime) // Add created_at field
            });
        });
    });

    return data;
}

async function exportAllMockTests() {
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();

    const data = [];
    snapshot.forEach(doc => {
        const userData = doc.data();
        const mockTests = userData.mockTests || {};

        Object.entries(mockTests).forEach(([testId, test]) => {
            data.push({
                id: testId,
                user_id: doc.id, // Changed from userId to user_id
                name: test.name,
                test_date: test.date, // Changed from date to test_date
                subject_marks: JSON.stringify(test.subjectMarks || []), // Changed from subjectMarks to subject_marks
                total_marks_obtained: safeParseInt(test.totalMarksObtained), // Changed from totalMarksObtained to total_marks_obtained
                total_marks: safeParseInt(test.totalMarks), // Changed from totalMarks to total_marks
                notes: test.notes || '',
                created_at: formatTimestamp(test.createdAt) // Changed from createdAt to created_at and format timestamp
            });
        });
    });

    return data;
}

async function exportAllChatComments() {
    const chatsRef = db.collection('aiChats');
    const snapshot = await chatsRef.get();

    const data = [];

    for (const doc of snapshot.docs) {
        const chatData = doc.data();
        const comments = chatData.comments || [];

        comments.forEach(comment => {
            data.push({
                id: comment.id,
                chat_id: doc.id, // Changed from chatId to chat_id
                content: comment.content,
                author_name: comment.author, // Changed from author to author_name
                author_id: comment.authorId, // Changed from authorId to author_id
                author_username: comment.authorUsername, // Changed from authorUsername to author_username
                author_photo_url: comment.authorPhotoURL, // Changed from authorPhotoURL to author_photo_url
                created_at: new Date(comment.timestamp).toISOString(), // Changed from timestamp to created_at
                updated_at: new Date(comment.timestamp).toISOString(), // Add updated_at field
                parent_id: comment.parentId || null, // Add parent_id field for replies
                replies: JSON.stringify(comment.replies || []),
                mentioned_user_ids: JSON.stringify(comment.mentionedUserIds || []) // Changed from mentionedUserIds to mentioned_user_ids
            });
        });
    }

    return data;
}

async function exportAllMessages() {
    const groupsRef = db.collection('groups');
    const groupsSnapshot = await groupsRef.get();

    const data = [];

    for (const groupDoc of groupsSnapshot.docs) {
        try {
            const messagesRef = db.collection(`groups/${groupDoc.id}/messages`);
            const messagesSnapshot = await messagesRef.orderBy('timestamp', 'asc').get();

            messagesSnapshot.forEach(msgDoc => {
                const msgData = msgDoc.data();
                data.push({
                    id: msgDoc.id,
                    group_id: groupDoc.id, // Changed from groupId to group_id
                    sender_id: msgData.senderId, // Changed from senderId to sender_id
                    sender_name: msgData.senderName || '', // Changed from senderName to sender_name
                    content: msgData.content,
                    message_type: msgData.messageType || 'text', // Changed from messageType to message_type
                    metadata: JSON.stringify(msgData.metadata || {}),
                    created_at: formatTimestamp(msgData.timestamp), // Changed from createdAt to created_at
                    updated_at: formatTimestamp(msgData.timestamp) // Changed from updatedAt to updated_at
                });
            });
        } catch (error) {
            console.warn(`Could not fetch messages for group ${groupDoc.id}:`, error);
        }
    }

    return data;
}

// Export study sessions as separate table records
async function exportStudySessionsTable() {
    const userRef = db.collection('users').doc(currentUser.uid);
    const doc = await userRef.get();

    const data = [];
    if (doc.exists) {
        const userData = doc.data();
        const studySessions = userData.studySessions || {};

        Object.entries(studySessions).forEach(([sessionId, session]) => {
            data.push({
                id: sessionId,
                user_id: currentUser.uid,
                subject: session.subject || '',
                duration: safeParseInt(session.duration),
                mode: session.mode || 'stopwatch',
                phase: session.phase || 'work',
                completed: session.completed || false,
                start_time: formatTimestamp(session.startTime),
                end_time: formatTimestamp(session.endTime),
                notes: session.notes || '',
                created_at: formatTimestamp(session.startTime)
            });
        });
    }

    return data;
}

// Export mock tests as separate table records
async function exportMockTestsTable() {
    const userRef = db.collection('users').doc(currentUser.uid);
    const doc = await userRef.get();

    const data = [];
    if (doc.exists) {
        const userData = doc.data();
        const mockTests = userData.mockTests || {};

        Object.entries(mockTests).forEach(([testId, test]) => {
            data.push({
                id: testId,
                user_id: currentUser.uid,
                name: test.name,
                test_date: test.date,
                subject_marks: JSON.stringify(test.subjectMarks || []),
                total_marks_obtained: safeParseInt(test.totalMarksObtained),
                total_marks: safeParseInt(test.totalMarks),
                notes: test.notes || '',
                created_at: formatTimestamp(test.createdAt),
                updated_at: formatTimestamp(test.createdAt)
            });
        });
    }

    return data;
}
