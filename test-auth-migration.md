# 🧪 Authentication Migration Test Plan

## 📋 Pre-Migration Checklist

### Environment Setup
- [ ] Supabase project is configured
- [ ] Database tables are created and populated with migrated data
- [ ] Environment variables are set correctly
- [ ] Both Firebase and Supabase credentials are valid

### Required Environment Variables
```env
VITE_USE_SUPABASE_AUTH=true
VITE_SUPABASE_URL=https://pcfrgvhigvklersufktf.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🔄 Test Scenarios

### Scenario 1: New User Registration (Supabase)
**Objective**: Verify new users can register and use the system

**Steps**:
1. Set `VITE_USE_SUPABASE_AUTH=true`
2. Open application in incognito mode
3. Click "Continue with Google"
4. Sign in with a NEW Google account (not previously used)
5. Complete profile setup with a unique username
6. Navigate through the application

**Expected Results**:
- [ ] OAuth flow completes successfully
- [ ] User is redirected to profile setup
- [ ] Profile creation works
- [ ] User can access all protected routes
- [ ] User data is stored in Supabase

### Scenario 2: Existing User Migration
**Objective**: Verify existing users can link their data

**Steps**:
1. Ensure you have an existing username in the migrated data
2. Set `VITE_USE_SUPABASE_AUTH=true`
3. Open application in incognito mode
4. Sign in with Google (can be new or existing Google account)
5. Navigate to `/data-migration`
6. Enter your existing username
7. Click "Check for Data"
8. Click "Link Data" if data is found
9. Verify access to previous data

**Expected Results**:
- [ ] Data migration page loads correctly
- [ ] Username lookup works
- [ ] Data linking completes successfully
- [ ] Previous AI chats are accessible
- [ ] Previous study data is available
- [ ] User profile shows correct information

### Scenario 3: Firebase Fallback
**Objective**: Verify Firebase auth still works

**Steps**:
1. Set `VITE_USE_SUPABASE_AUTH=false` (or remove the variable)
2. Clear browser cache and localStorage
3. Restart development server
4. Sign in with Firebase auth
5. Verify all functionality works

**Expected Results**:
- [ ] Firebase auth loads correctly
- [ ] Sign in process works
- [ ] All existing functionality preserved
- [ ] No errors in console

### Scenario 4: Auth Provider Switching
**Objective**: Verify smooth switching between auth providers

**Steps**:
1. Start with Firebase auth (`VITE_USE_SUPABASE_AUTH=false`)
2. Sign in and use the application
3. Sign out
4. Switch to Supabase auth (`VITE_USE_SUPABASE_AUTH=true`)
5. Restart development server
6. Sign in with Supabase auth
7. Switch back to Firebase auth
8. Verify no data corruption

**Expected Results**:
- [ ] Switching works without errors
- [ ] No data loss occurs
- [ ] Both auth systems function independently
- [ ] User store updates correctly

## 🔍 Detailed Testing

### Authentication Flow Testing

#### Google OAuth (Supabase)
- [ ] OAuth popup opens correctly
- [ ] User can select Google account
- [ ] Redirect to `/auth/callback` works
- [ ] Callback processing completes
- [ ] User is redirected to appropriate page

#### Profile Management
- [ ] Profile creation works for new users
- [ ] Profile updates work correctly
- [ ] Username validation functions
- [ ] Profile data persists correctly

#### Data Linking
- [ ] Username search finds existing data
- [ ] Data linking updates user profile
- [ ] Linked data becomes accessible
- [ ] No data duplication occurs

### Error Handling Testing

#### Network Issues
- [ ] Graceful handling of network failures
- [ ] Appropriate error messages shown
- [ ] Retry mechanisms work

#### Invalid Data
- [ ] Invalid usernames handled correctly
- [ ] Non-existent data handled gracefully
- [ ] Malformed requests rejected

#### Permission Issues
- [ ] Unauthorized access blocked
- [ ] RLS policies enforced
- [ ] Proper error messages displayed

## 📊 Performance Testing

### Load Times
- [ ] Authentication flows complete quickly
- [ ] Profile loading is responsive
- [ ] Data migration completes in reasonable time

### Memory Usage
- [ ] No memory leaks in auth contexts
- [ ] Proper cleanup on component unmount
- [ ] Efficient state management

## 🛠️ Development Testing

### Code Quality
- [ ] No TypeScript errors
- [ ] No console warnings
- [ ] Proper error boundaries
- [ ] Clean component unmounting

### Browser Compatibility
- [ ] Works in Chrome
- [ ] Works in Firefox
- [ ] Works in Safari
- [ ] Works in Edge

### Mobile Testing
- [ ] OAuth works on mobile browsers
- [ ] Responsive design maintained
- [ ] Touch interactions work

## 📝 Test Results Template

### Test Environment
- **Date**: ___________
- **Browser**: ___________
- **Environment**: Development/Production
- **Supabase Auth**: Enabled/Disabled

### Results Summary
- **New User Registration**: ✅ Pass / ❌ Fail
- **Existing User Migration**: ✅ Pass / ❌ Fail
- **Firebase Fallback**: ✅ Pass / ❌ Fail
- **Auth Provider Switching**: ✅ Pass / ❌ Fail

### Issues Found
1. **Issue**: ___________
   **Severity**: High/Medium/Low
   **Status**: Open/Resolved

2. **Issue**: ___________
   **Severity**: High/Medium/Low
   **Status**: Open/Resolved

### Recommendations
- [ ] Ready for production deployment
- [ ] Needs additional testing
- [ ] Requires bug fixes

## 🚀 Deployment Checklist

Before deploying to production:

### Configuration
- [ ] Production environment variables set
- [ ] Supabase production project configured
- [ ] OAuth domains whitelisted
- [ ] CORS settings configured

### Security
- [ ] RLS policies tested
- [ ] API keys secured
- [ ] No sensitive data in client code
- [ ] HTTPS enforced

### Monitoring
- [ ] Error tracking configured
- [ ] Analytics tracking works
- [ ] Performance monitoring active
- [ ] User feedback collection ready

---

**🎯 Complete this test plan before deploying the authentication migration to production!**
